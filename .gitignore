# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/
.env
.venv

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Testing
.coverage
htmlcov/
.tox/
.pytest_cache/
coverage.xml
*.cover
.hypothesis/

# Splunk specific
local/
metadata/local.meta
*.log
*.pid

# Misc
*.bak
*.tmp
.env.local
.env.development.local
.env.test.local
.env.production.local