{".class": "MypyFile", "_fullname": "errno", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "E2BIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.E2BIG", "name": "E2BIG", "setter_type": null, "type": "builtins.int"}}, "EACCES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EACCES", "name": "EACCES", "setter_type": null, "type": "builtins.int"}}, "EADDRINUSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EADDRINUSE", "name": "EADDRINUSE", "setter_type": null, "type": "builtins.int"}}, "EADDRNOTAVAIL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EADDRNOTAVAIL", "name": "EADDRNOTAVAIL", "setter_type": null, "type": "builtins.int"}}, "EAFNOSUPPORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EAFNOSUPPORT", "name": "EAFNOSUPPORT", "setter_type": null, "type": "builtins.int"}}, "EAGAIN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EAGAIN", "name": "EAGAIN", "setter_type": null, "type": "builtins.int"}}, "EALREADY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EALREADY", "name": "EALREADY", "setter_type": null, "type": "builtins.int"}}, "EAUTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EAUTH", "name": "EAUTH", "setter_type": null, "type": "builtins.int"}}, "EBADARCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EBADARCH", "name": "EBADARCH", "setter_type": null, "type": "builtins.int"}}, "EBADEXEC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EBADEXEC", "name": "EBADEXEC", "setter_type": null, "type": "builtins.int"}}, "EBADF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EBADF", "name": "EBADF", "setter_type": null, "type": "builtins.int"}}, "EBADMACHO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EBADMACHO", "name": "EBADMACHO", "setter_type": null, "type": "builtins.int"}}, "EBADMSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EBADMSG", "name": "EBADMSG", "setter_type": null, "type": "builtins.int"}}, "EBADRPC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EBADRPC", "name": "EBADRPC", "setter_type": null, "type": "builtins.int"}}, "EBUSY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EBUSY", "name": "EBUSY", "setter_type": null, "type": "builtins.int"}}, "ECANCELED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ECANCELED", "name": "ECANCELED", "setter_type": null, "type": "builtins.int"}}, "ECHILD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ECHILD", "name": "ECHILD", "setter_type": null, "type": "builtins.int"}}, "ECONNABORTED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ECONNABORTED", "name": "ECONNABORTED", "setter_type": null, "type": "builtins.int"}}, "ECONNREFUSED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ECONNREFUSED", "name": "ECONNREFUSED", "setter_type": null, "type": "builtins.int"}}, "ECONNRESET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ECONNRESET", "name": "ECONNRESET", "setter_type": null, "type": "builtins.int"}}, "EDEADLK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EDEADLK", "name": "EDEADLK", "setter_type": null, "type": "builtins.int"}}, "EDESTADDRREQ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EDESTADDRREQ", "name": "EDESTADDRREQ", "setter_type": null, "type": "builtins.int"}}, "EDEVERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EDEVERR", "name": "EDEVERR", "setter_type": null, "type": "builtins.int"}}, "EDOM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EDOM", "name": "EDOM", "setter_type": null, "type": "builtins.int"}}, "EDQUOT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EDQUOT", "name": "EDQUOT", "setter_type": null, "type": "builtins.int"}}, "EEXIST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EEXIST", "name": "EEXIST", "setter_type": null, "type": "builtins.int"}}, "EFAULT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EFAULT", "name": "EFAULT", "setter_type": null, "type": "builtins.int"}}, "EFBIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EFBIG", "name": "EFBIG", "setter_type": null, "type": "builtins.int"}}, "EFTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EFTYPE", "name": "EFTYPE", "setter_type": null, "type": "builtins.int"}}, "EHOSTDOWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EHOSTDOWN", "name": "EHOSTDOWN", "setter_type": null, "type": "builtins.int"}}, "EHOSTUNREACH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EHOSTUNREACH", "name": "EHOSTUNREACH", "setter_type": null, "type": "builtins.int"}}, "EIDRM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EIDRM", "name": "EIDRM", "setter_type": null, "type": "builtins.int"}}, "EILSEQ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EILSEQ", "name": "EILSEQ", "setter_type": null, "type": "builtins.int"}}, "EINPROGRESS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EINPROGRESS", "name": "EINPROGRESS", "setter_type": null, "type": "builtins.int"}}, "EINTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EINTR", "name": "EINTR", "setter_type": null, "type": "builtins.int"}}, "EINVAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EINVAL", "name": "EINVAL", "setter_type": null, "type": "builtins.int"}}, "EIO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EIO", "name": "EIO", "setter_type": null, "type": "builtins.int"}}, "EISCONN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EISCONN", "name": "EISCONN", "setter_type": null, "type": "builtins.int"}}, "EISDIR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EISDIR", "name": "EISDIR", "setter_type": null, "type": "builtins.int"}}, "ELOOP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ELOOP", "name": "ELOOP", "setter_type": null, "type": "builtins.int"}}, "EMFILE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EMFILE", "name": "EMFILE", "setter_type": null, "type": "builtins.int"}}, "EMLINK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EMLINK", "name": "EMLINK", "setter_type": null, "type": "builtins.int"}}, "EMSGSIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EMSGSIZE", "name": "EMSGSIZE", "setter_type": null, "type": "builtins.int"}}, "EMULTIHOP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EMULTIHOP", "name": "EMULTIHOP", "setter_type": null, "type": "builtins.int"}}, "ENAMETOOLONG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENAMETOOLONG", "name": "ENAMETOOLONG", "setter_type": null, "type": "builtins.int"}}, "ENEEDAUTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENEEDAUTH", "name": "ENEEDAUTH", "setter_type": null, "type": "builtins.int"}}, "ENETDOWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENETDOWN", "name": "ENETDOWN", "setter_type": null, "type": "builtins.int"}}, "ENETRESET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENETRESET", "name": "ENETRESET", "setter_type": null, "type": "builtins.int"}}, "ENETUNREACH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENETUNREACH", "name": "ENETUNREACH", "setter_type": null, "type": "builtins.int"}}, "ENFILE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENFILE", "name": "ENFILE", "setter_type": null, "type": "builtins.int"}}, "ENOATTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOATTR", "name": "ENOATTR", "setter_type": null, "type": "builtins.int"}}, "ENOBUFS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOBUFS", "name": "ENOBUFS", "setter_type": null, "type": "builtins.int"}}, "ENODATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENODATA", "name": "ENODATA", "setter_type": null, "type": "builtins.int"}}, "ENODEV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENODEV", "name": "ENODEV", "setter_type": null, "type": "builtins.int"}}, "ENOENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOENT", "name": "ENOENT", "setter_type": null, "type": "builtins.int"}}, "ENOEXEC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOEXEC", "name": "ENOEXEC", "setter_type": null, "type": "builtins.int"}}, "ENOLCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOLCK", "name": "ENOLCK", "setter_type": null, "type": "builtins.int"}}, "ENOLINK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOLINK", "name": "ENOLINK", "setter_type": null, "type": "builtins.int"}}, "ENOMEM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOMEM", "name": "ENOMEM", "setter_type": null, "type": "builtins.int"}}, "ENOMSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOMSG", "name": "ENOMSG", "setter_type": null, "type": "builtins.int"}}, "ENOPOLICY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOPOLICY", "name": "ENOPOLICY", "setter_type": null, "type": "builtins.int"}}, "ENOPROTOOPT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOPROTOOPT", "name": "ENOPROTOOPT", "setter_type": null, "type": "builtins.int"}}, "ENOSPC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOSPC", "name": "ENOSPC", "setter_type": null, "type": "builtins.int"}}, "ENOSR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOSR", "name": "ENOSR", "setter_type": null, "type": "builtins.int"}}, "ENOSTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOSTR", "name": "ENOSTR", "setter_type": null, "type": "builtins.int"}}, "ENOSYS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOSYS", "name": "ENOSYS", "setter_type": null, "type": "builtins.int"}}, "ENOTBLK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTBLK", "name": "ENOTBLK", "setter_type": null, "type": "builtins.int"}}, "ENOTCONN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTCONN", "name": "ENOTCONN", "setter_type": null, "type": "builtins.int"}}, "ENOTDIR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTDIR", "name": "ENOTDIR", "setter_type": null, "type": "builtins.int"}}, "ENOTEMPTY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTEMPTY", "name": "ENOTEMPTY", "setter_type": null, "type": "builtins.int"}}, "ENOTRECOVERABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTRECOVERABLE", "name": "ENOTRECOVERABLE", "setter_type": null, "type": "builtins.int"}}, "ENOTSOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTSOCK", "name": "ENOTSOCK", "setter_type": null, "type": "builtins.int"}}, "ENOTSUP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTSUP", "name": "ENOTSUP", "setter_type": null, "type": "builtins.int"}}, "ENOTTY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTTY", "name": "ENOTTY", "setter_type": null, "type": "builtins.int"}}, "ENXIO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENXIO", "name": "ENXIO", "setter_type": null, "type": "builtins.int"}}, "EOPNOTSUPP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EOPNOTSUPP", "name": "EOPNOTSUPP", "setter_type": null, "type": "builtins.int"}}, "EOVERFLOW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EOVERFLOW", "name": "EOVERFLOW", "setter_type": null, "type": "builtins.int"}}, "EOWNERDEAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EOWNERDEAD", "name": "EOWNERDEAD", "setter_type": null, "type": "builtins.int"}}, "EPERM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPERM", "name": "EPERM", "setter_type": null, "type": "builtins.int"}}, "EPFNOSUPPORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPFNOSUPPORT", "name": "EPFNOSUPPORT", "setter_type": null, "type": "builtins.int"}}, "EPIPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPIPE", "name": "EPIPE", "setter_type": null, "type": "builtins.int"}}, "EPROCLIM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPROCLIM", "name": "EPROCLIM", "setter_type": null, "type": "builtins.int"}}, "EPROCUNAVAIL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPROCUNAVAIL", "name": "EPROCUNAVAIL", "setter_type": null, "type": "builtins.int"}}, "EPROGMISMATCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPROGMISMATCH", "name": "EPROGMISMATCH", "setter_type": null, "type": "builtins.int"}}, "EPROGUNAVAIL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPROGUNAVAIL", "name": "EPROGUNAVAIL", "setter_type": null, "type": "builtins.int"}}, "EPROTO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPROTO", "name": "EPROTO", "setter_type": null, "type": "builtins.int"}}, "EPROTONOSUPPORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPROTONOSUPPORT", "name": "EPROTONOSUPPORT", "setter_type": null, "type": "builtins.int"}}, "EPROTOTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPROTOTYPE", "name": "EPROTOTYPE", "setter_type": null, "type": "builtins.int"}}, "EPWROFF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPWROFF", "name": "EPWROFF", "setter_type": null, "type": "builtins.int"}}, "EQFULL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EQFULL", "name": "EQFULL", "setter_type": null, "type": "builtins.int"}}, "ERANGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ERANGE", "name": "ERANGE", "setter_type": null, "type": "builtins.int"}}, "EREMOTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EREMOTE", "name": "EREMOTE", "setter_type": null, "type": "builtins.int"}}, "EROFS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EROFS", "name": "EROFS", "setter_type": null, "type": "builtins.int"}}, "ERPCMISMATCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ERPCMISMATCH", "name": "ERPCMISMATCH", "setter_type": null, "type": "builtins.int"}}, "ESHLIBVERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ESHLIBVERS", "name": "ESHLIBVERS", "setter_type": null, "type": "builtins.int"}}, "ESHUTDOWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ESHUTDOWN", "name": "ESHUTDOWN", "setter_type": null, "type": "builtins.int"}}, "ESOCKTNOSUPPORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ESOCKTNOSUPPORT", "name": "ESOCKTNOSUPPORT", "setter_type": null, "type": "builtins.int"}}, "ESPIPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ESPIPE", "name": "ESPIPE", "setter_type": null, "type": "builtins.int"}}, "ESRCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ESRCH", "name": "ESRCH", "setter_type": null, "type": "builtins.int"}}, "ESTALE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ESTALE", "name": "ESTALE", "setter_type": null, "type": "builtins.int"}}, "ETIME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ETIME", "name": "ETIME", "setter_type": null, "type": "builtins.int"}}, "ETIMEDOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ETIMEDOUT", "name": "ETIMEDOUT", "setter_type": null, "type": "builtins.int"}}, "ETOOMANYREFS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ETOOMANYREFS", "name": "ETOOMANYREFS", "setter_type": null, "type": "builtins.int"}}, "ETXTBSY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ETXTBSY", "name": "ETXTBSY", "setter_type": null, "type": "builtins.int"}}, "EUSERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EUSERS", "name": "EUSERS", "setter_type": null, "type": "builtins.int"}}, "EWOULDBLOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EWOULDBLOCK", "name": "EWOULDBLOCK", "setter_type": null, "type": "builtins.int"}}, "EXDEV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EXDEV", "name": "EXDEV", "setter_type": null, "type": "builtins.int"}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "errorcode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.errorcode", "name": "errorcode", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/mypy/typeshed/stdlib/errno.pyi"}