{".class": "MypyFile", "_fullname": "http.client", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ACCEPTED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.ACCEPTED", "name": "ACCEPTED", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 202}}}, "ALREADY_REPORTED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.ALREADY_REPORTED", "name": "ALREADY_REPORTED", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 208}}}, "BAD_GATEWAY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.BAD_GATEWAY", "name": "BAD_GATEWAY", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 502}}}, "BAD_REQUEST": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.BAD_REQUEST", "name": "BAD_REQUEST", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 400}}}, "BadStatusLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.client.HTTPException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.client.BadStatusLine", "name": "BadStatusLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.client.BadStatusLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.client", "mro": ["http.client.BadStatusLine", "http.client.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.BadStatusLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["http.client.BadStatusLine", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BadStatusLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client.BadStatusLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.client.BadStatusLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BinaryIO": {".class": "SymbolTableNode", "cross_ref": "typing.BinaryIO", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CONFLICT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.CONFLICT", "name": "CONFLICT", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 409}}}, "CONTINUE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.CONTINUE", "name": "CONTINUE", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 100}}}, "CREATED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.CREATED", "name": "CREATED", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 201}}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CannotSendHeader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.client.ImproperConnectionState"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.client.CannotSendHeader", "name": "CannotSendHeader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.client.CannotSendHeader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.client", "mro": ["http.client.CannotSendHeader", "http.client.ImproperConnectionState", "http.client.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client.CannotSendHeader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.client.CannotSendHeader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CannotSendRequest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.client.ImproperConnectionState"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.client.CannotSendRequest", "name": "CannotSendRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.client.CannotSendRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.client", "mro": ["http.client.CannotSendRequest", "http.client.ImproperConnectionState", "http.client.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client.CannotSendRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.client.CannotSendRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EARLY_HINTS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.EARLY_HINTS", "name": "EARLY_HINTS", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 103}}}, "EXPECTATION_FAILED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.EXPECTATION_FAILED", "name": "EXPECTATION_FAILED", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 417}}}, "FAILED_DEPENDENCY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.FAILED_DEPENDENCY", "name": "FAILED_DEPENDENCY", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 424}}}, "FORBIDDEN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.FORBIDDEN", "name": "FORBIDDEN", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 403}}}, "FOUND": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.FOUND", "name": "FOUND", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 302}}}, "GATEWAY_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.GATEWAY_TIMEOUT", "name": "GATEWAY_TIMEOUT", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 504}}}, "GONE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.GONE", "name": "GONE", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 410}}}, "HTTPConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.client.HTTPConnection", "name": "HTTPConnection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.client.HTTPConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.client", "mro": ["http.client.HTTPConnection", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "host", "port", "timeout", "source_address", "blocksize"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPConnection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "host", "port", "timeout", "source_address", "blocksize"], "arg_types": ["http.client.HTTPConnection", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "auto_open": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.HTTPConnection.auto_open", "name": "auto_open", "setter_type": null, "type": "builtins.int"}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPConnection.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["http.client.HTTPConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPConnection.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["http.client.HTTPConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "connect of HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "debuglevel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.HTTPConnection.debuglevel", "name": "debuglevel", "setter_type": null, "type": "builtins.int"}}, "default_port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.HTTPConnection.default_port", "name": "default_port", "setter_type": null, "type": "builtins.int"}}, "endheaders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5], "arg_names": ["self", "message_body", "encode_chunked"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPConnection.endheaders", "name": "endheaders", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5], "arg_names": ["self", "message_body", "encode_chunked"], "arg_types": ["http.client.HTTPConnection", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "http.client._DataType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "endheaders of HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getresponse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPConnection.getresponse", "name": "getresponse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["http.client.HTTPConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getresponse of HTTPConnection", "ret_type": "http.client.HTTPResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.HTTPConnection.host", "name": "host", "setter_type": null, "type": "builtins.str"}}, "port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.HTTPConnection.port", "name": "port", "setter_type": null, "type": "builtins.int"}}, "putheader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "header", "values"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPConnection.putheader", "name": "putheader", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "header", "values"], "arg_types": ["http.client.HTTPConnection", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "http.client._HeaderValue"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "putheader of HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "putrequest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "method", "url", "skip_host", "skip_accept_encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPConnection.putrequest", "name": "putrequest", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "method", "url", "skip_host", "skip_accept_encoding"], "arg_types": ["http.client.HTTPConnection", "builtins.str", "builtins.str", "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "putrequest of HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 5], "arg_names": ["self", "method", "url", "body", "headers", "encode_chunked"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPConnection.request", "name": "request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 5], "arg_names": ["self", "method", "url", "body", "headers", "encode_chunked"], "arg_types": ["http.client.HTTPConnection", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "http.client._DataType"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "http.client._HeaderValue"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "request of HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "response_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.HTTPConnection.response_class", "name": "response_class", "setter_type": null, "type": {".class": "TypeType", "item": "http.client.HTTPResponse"}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPConnection.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["http.client.HTTPConnection", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "http.client._DataType"}, "builtins.str"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send of HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_debuglevel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "level"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPConnection.set_debuglevel", "name": "set_debuglevel", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "level"], "arg_types": ["http.client.HTTPConnection", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_debuglevel of HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_tunnel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "host", "port", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPConnection.set_tunnel", "name": "set_tunnel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "host", "port", "headers"], "arg_types": ["http.client.HTTPConnection", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_tunnel of HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.HTTPConnection.sock", "name": "sock", "setter_type": null, "type": {".class": "UnionType", "items": ["socket.socket", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}}}, "timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.HTTPConnection.timeout", "name": "timeout", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client.HTTPConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.client.HTTPConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.client.HTTPException", "name": "HTTPException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.client.HTTPException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.client", "mro": ["http.client.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client.HTTPException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.client.HTTPException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPMessage": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.client.HTTPMessage", "name": "HTTPMessage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.client.HTTPMessage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.client", "mro": ["http.client.HTTPMessage", "email.message.Message", "builtins.object"], "names": {".class": "SymbolTable", "getallmatchingheaders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPMessage.getallmatchingheaders", "name": "getallmatchingheaders", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["http.client.HTTPMessage", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getallmatchingheaders of HTTPMessage", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client.HTTPMessage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.client.HTTPMessage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["io.BufferedIOBase", "typing.BinaryIO"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.client.HTTPResponse", "name": "HTTPResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.client.HTTPResponse", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "http.client", "mro": ["http.client.HTTPResponse", "io.BufferedIOBase", "_io._BufferedIOBase", "io.IOBase", "_io._IOBase", "typing.BinaryIO", "typing.IO", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.client.HTTPResponse.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client.HTTPResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.client.HTTPResponse", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__enter__ of HTTPResponse", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client.HTTPResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.client.HTTPResponse", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client.HTTPResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.client.HTTPResponse", "values": [], "variance": 0}]}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPResponse.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["http.client.HTTPResponse", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__exit__ of HTTPResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "sock", "debuglevel", "method", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "sock", "debuglevel", "method", "url"], "arg_types": ["http.client.HTTPResponse", "socket.socket", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of HTTPResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPResponse.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["http.client.HTTPResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__iter__ of HTTPResponse", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPResponse.begin", "name": "begin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["http.client.HTTPResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "begin of HTTPResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "chunk_left": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.HTTPResponse.chunk_left", "name": "chunk_left", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "chunked": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.HTTPResponse.chunked", "name": "chunked", "setter_type": null, "type": "builtins.bool"}}, "closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.HTTPResponse.closed", "name": "closed", "setter_type": null, "type": "builtins.bool"}}, "debuglevel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.HTTPResponse.debuglevel", "name": "debuglevel", "setter_type": null, "type": "builtins.int"}}, "fp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.HTTPResponse.fp", "name": "fp", "setter_type": null, "type": {".class": "Instance", "args": ["_io._BufferedReaderStream"], "extra_attrs": null, "type_ref": "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, "getcode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPResponse.getcode", "name": "getcode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["http.client.HTTPResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getcode of HTTPResponse", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getheader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "http.client.HTTPResponse.getheader", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "http.client.HTTPResponse.getheader", "name": "getheader", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["http.client.HTTPResponse", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getheader of HTTPResponse", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "http.client.HTTPResponse.getheader", "name": "getheader", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["http.client.HTTPResponse", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getheader of HTTPResponse", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "http.client.HTTPResponse.getheader", "name": "getheader", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "default"], "arg_types": ["http.client.HTTPResponse", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client._T", "id": -1, "name": "_T", "namespace": "http.client.HTTPResponse.getheader", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getheader of HTTPResponse", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client._T", "id": -1, "name": "_T", "namespace": "http.client.HTTPResponse.getheader", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client._T", "id": -1, "name": "_T", "namespace": "http.client.HTTPResponse.getheader", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "http.client.HTTPResponse.getheader", "name": "getheader", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "default"], "arg_types": ["http.client.HTTPResponse", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client._T", "id": -1, "name": "_T", "namespace": "http.client.HTTPResponse.getheader", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getheader of HTTPResponse", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client._T", "id": -1, "name": "_T", "namespace": "http.client.HTTPResponse.getheader", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client._T", "id": -1, "name": "_T", "namespace": "http.client.HTTPResponse.getheader", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["http.client.HTTPResponse", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getheader of HTTPResponse", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "default"], "arg_types": ["http.client.HTTPResponse", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client._T", "id": -1, "name": "_T", "namespace": "http.client.HTTPResponse.getheader", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getheader of HTTPResponse", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client._T", "id": -1, "name": "_T", "namespace": "http.client.HTTPResponse.getheader", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client._T", "id": -1, "name": "_T", "namespace": "http.client.HTTPResponse.getheader", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "getheaders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPResponse.getheaders", "name": "getheaders", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["http.client.HTTPResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getheaders of HTTPResponse", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "geturl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPResponse.geturl", "name": "geturl", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["http.client.HTTPResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "geturl of HTTPResponse", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.HTTPResponse.headers", "name": "headers", "setter_type": null, "type": "http.client.HTTPMessage"}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPResponse.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["http.client.HTTPResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "info of HTTPResponse", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isclosed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPResponse.isclosed", "name": "isclosed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["http.client.HTTPResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "isclosed of HTTPResponse", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.HTTPResponse.length", "name": "length", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "msg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.HTTPResponse.msg", "name": "msg", "setter_type": null, "type": "http.client.HTTPMessage"}}, "peek": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPResponse.peek", "name": "peek", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "n"], "arg_types": ["http.client.HTTPResponse", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "peek of HTTPResponse", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "amt"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPResponse.read", "name": "read", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "amt"], "arg_types": ["http.client.HTTPResponse", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read of HTTPResponse", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPResponse.read1", "name": "read1", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "n"], "arg_types": ["http.client.HTTPResponse", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read1 of HTTPResponse", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "readinto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPResponse.readinto", "name": "readinto", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "b"], "arg_types": ["http.client.HTTPResponse", "typing_extensions.Buffer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "readinto of HTTPResponse", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "readline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPResponse.readline", "name": "readline", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "limit"], "arg_types": ["http.client.HTTPResponse", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "readline of HTTPResponse", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reason": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.HTTPResponse.reason", "name": "reason", "setter_type": null, "type": "builtins.str"}}, "status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.HTTPResponse.status", "name": "status", "setter_type": null, "type": "builtins.int"}}, "url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.HTTPResponse.url", "name": "url", "setter_type": null, "type": "builtins.str"}}, "version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.HTTPResponse.version", "name": "version", "setter_type": null, "type": "builtins.int"}}, "will_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.HTTPResponse.will_close", "name": "will_close", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client.HTTPResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.client.HTTPResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPSConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.client.HTTPConnection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.client.HTTPSConnection", "name": "HTTPSConnection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.client.HTTPSConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.client", "mro": ["http.client.HTTPSConnection", "http.client.HTTPConnection", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 5, 5, 5], "arg_names": ["self", "host", "port", "key_file", "cert_file", "timeout", "source_address", "context", "check_hostname", "blocksize"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.HTTPSConnection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 5, 5, 5], "arg_names": ["self", "host", "port", "key_file", "cert_file", "timeout", "source_address", "context", "check_hostname", "blocksize"], "arg_types": ["http.client.HTTPSConnection", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of HTTPSConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.HTTPSConnection.sock", "name": "sock", "setter_type": null, "type": {".class": "UnionType", "items": ["ssl.SSLSocket", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client.HTTPSConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.client.HTTPSConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPS_PORT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.HTTPS_PORT", "name": "HTTPS_PORT", "setter_type": null, "type": "builtins.int"}}, "HTTP_PORT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.HTTP_PORT", "name": "HTTP_PORT", "setter_type": null, "type": "builtins.int"}}, "HTTP_VERSION_NOT_SUPPORTED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.HTTP_VERSION_NOT_SUPPORTED", "name": "HTTP_VERSION_NOT_SUPPORTED", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 505}}}, "IM_A_TEAPOT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.IM_A_TEAPOT", "name": "IM_A_TEAPOT", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 418}}}, "IM_USED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.IM_USED", "name": "IM_USED", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 226}}}, "INSUFFICIENT_STORAGE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.INSUFFICIENT_STORAGE", "name": "INSUFFICIENT_STORAGE", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 507}}}, "INTERNAL_SERVER_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.INTERNAL_SERVER_ERROR", "name": "INTERNAL_SERVER_ERROR", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 500}}}, "ImproperConnectionState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.client.HTTPException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.client.ImproperConnectionState", "name": "ImproperConnectionState", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.client.ImproperConnectionState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.client", "mro": ["http.client.ImproperConnectionState", "http.client.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client.ImproperConnectionState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.client.ImproperConnectionState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IncompleteRead": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.client.HTTPException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.client.IncompleteRead", "name": "IncompleteRead", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.client.IncompleteRead", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.client", "mro": ["http.client.IncompleteRead", "http.client.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "partial", "expected"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.IncompleteRead.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "partial", "expected"], "arg_types": ["http.client.IncompleteRead", "builtins.bytes", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of IncompleteRead", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "expected": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.IncompleteRead.expected", "name": "expected", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "partial": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.client.IncompleteRead.partial", "name": "partial", "setter_type": null, "type": "builtins.bytes"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client.IncompleteRead.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.client.IncompleteRead", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidURL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.client.HTTPException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.client.InvalidURL", "name": "InvalidURL", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.client.InvalidURL", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.client", "mro": ["http.client.InvalidURL", "http.client.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client.InvalidURL.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.client.InvalidURL", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LENGTH_REQUIRED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.LENGTH_REQUIRED", "name": "LENGTH_REQUIRED", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 411}}}, "LOCKED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.LOCKED", "name": "LOCKED", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 423}}}, "LOOP_DETECTED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.LOOP_DETECTED", "name": "LOOP_DETECTED", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 508}}}, "LineTooLong": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.client.HTTPException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.client.LineTooLong", "name": "LineTooLong", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.client.LineTooLong", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.client", "mro": ["http.client.LineTooLong", "http.client.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.LineTooLong.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line_type"], "arg_types": ["http.client.LineTooLong", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of LineTooLong", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client.LineTooLong.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.client.LineTooLong", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "METHOD_NOT_ALLOWED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.METHOD_NOT_ALLOWED", "name": "METHOD_NOT_ALLOWED", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 405}}}, "MISDIRECTED_REQUEST": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.MISDIRECTED_REQUEST", "name": "MISDIRECTED_REQUEST", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 421}}}, "MOVED_PERMANENTLY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.MOVED_PERMANENTLY", "name": "MOVED_PERMANENTLY", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 301}}}, "MULTIPLE_CHOICES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.MULTIPLE_CHOICES", "name": "MULTIPLE_CHOICES", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 300}}}, "MULTI_STATUS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.MULTI_STATUS", "name": "MULTI_STATUS", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 207}}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MaybeNone": {".class": "SymbolTableNode", "cross_ref": "_typeshed.MaybeNone", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NETWORK_AUTHENTICATION_REQUIRED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.NETWORK_AUTHENTICATION_REQUIRED", "name": "NETWORK_AUTHENTICATION_REQUIRED", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 511}}}, "NON_AUTHORITATIVE_INFORMATION": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.NON_AUTHORITATIVE_INFORMATION", "name": "NON_AUTHORITATIVE_INFORMATION", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 203}}}, "NOT_ACCEPTABLE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.NOT_ACCEPTABLE", "name": "NOT_ACCEPTABLE", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 406}}}, "NOT_EXTENDED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.NOT_EXTENDED", "name": "NOT_EXTENDED", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 510}}}, "NOT_FOUND": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.NOT_FOUND", "name": "NOT_FOUND", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 404}}}, "NOT_IMPLEMENTED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.NOT_IMPLEMENTED", "name": "NOT_IMPLEMENTED", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 501}}}, "NOT_MODIFIED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.NOT_MODIFIED", "name": "NOT_MODIFIED", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 304}}}, "NO_CONTENT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.NO_CONTENT", "name": "NO_CONTENT", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 204}}}, "NotConnected": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.client.HTTPException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.client.NotConnected", "name": "NotConnected", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.client.NotConnected", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.client", "mro": ["http.client.NotConnected", "http.client.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client.NotConnected.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.client.NotConnected", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OK": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.OK", "name": "OK", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 200}}}, "PARTIAL_CONTENT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.PARTIAL_CONTENT", "name": "PARTIAL_CONTENT", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 206}}}, "PAYMENT_REQUIRED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.PAYMENT_REQUIRED", "name": "PAYMENT_REQUIRED", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 402}}}, "PERMANENT_REDIRECT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.PERMANENT_REDIRECT", "name": "PERMANENT_REDIRECT", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 308}}}, "PRECONDITION_FAILED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.PRECONDITION_FAILED", "name": "PRECONDITION_FAILED", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 412}}}, "PRECONDITION_REQUIRED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.PRECONDITION_REQUIRED", "name": "PRECONDITION_REQUIRED", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 428}}}, "PROCESSING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.PROCESSING", "name": "PROCESSING", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 102}}}, "PROXY_AUTHENTICATION_REQUIRED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.PROXY_AUTHENTICATION_REQUIRED", "name": "PROXY_AUTHENTICATION_REQUIRED", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 407}}}, "REQUESTED_RANGE_NOT_SATISFIABLE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.REQUESTED_RANGE_NOT_SATISFIABLE", "name": "REQUESTED_RANGE_NOT_SATISFIABLE", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 416}}}, "REQUEST_ENTITY_TOO_LARGE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.REQUEST_ENTITY_TOO_LARGE", "name": "REQUEST_ENTITY_TOO_LARGE", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 413}}}, "REQUEST_HEADER_FIELDS_TOO_LARGE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.REQUEST_HEADER_FIELDS_TOO_LARGE", "name": "REQUEST_HEADER_FIELDS_TOO_LARGE", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 431}}}, "REQUEST_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.REQUEST_TIMEOUT", "name": "REQUEST_TIMEOUT", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 408}}}, "REQUEST_URI_TOO_LONG": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.REQUEST_URI_TOO_LONG", "name": "REQUEST_URI_TOO_LONG", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 414}}}, "RESET_CONTENT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.RESET_CONTENT", "name": "RESET_CONTENT", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 205}}}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RemoteDisconnected": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ConnectionResetError", "http.client.BadStatusLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.client.RemoteDisconnected", "name": "RemoteDisconnected", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.client.RemoteDisconnected", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.client", "mro": ["http.client.RemoteDisconnected", "builtins.ConnectionResetError", "builtins.ConnectionError", "builtins.OSError", "http.client.BadStatusLine", "http.client.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client.RemoteDisconnected.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.client.RemoteDisconnected", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResponseNotReady": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.client.ImproperConnectionState"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.client.ResponseNotReady", "name": "ResponseNotReady", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.client.ResponseNotReady", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.client", "mro": ["http.client.ResponseNotReady", "http.client.ImproperConnectionState", "http.client.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client.ResponseNotReady.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.client.ResponseNotReady", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SEE_OTHER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.SEE_OTHER", "name": "SEE_OTHER", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 303}}}, "SERVICE_UNAVAILABLE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.SERVICE_UNAVAILABLE", "name": "SERVICE_UNAVAILABLE", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 503}}}, "SWITCHING_PROTOCOLS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.SWITCHING_PROTOCOLS", "name": "SWITCHING_PROTOCOLS", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 101}}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsRead": {".class": "SymbolTableNode", "cross_ref": "_typeshed.SupportsRead", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsReadline": {".class": "SymbolTableNode", "cross_ref": "_typeshed.SupportsReadline", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TEMPORARY_REDIRECT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.TEMPORARY_REDIRECT", "name": "TEMPORARY_REDIRECT", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 307}}}, "TOO_EARLY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.TOO_EARLY", "name": "TOO_EARLY", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 425}}}, "TOO_MANY_REQUESTS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.TOO_MANY_REQUESTS", "name": "TOO_MANY_REQUESTS", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 429}}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UNAUTHORIZED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.UNAUTHORIZED", "name": "UNAUTHORIZED", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 401}}}, "UNAVAILABLE_FOR_LEGAL_REASONS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.UNAVAILABLE_FOR_LEGAL_REASONS", "name": "UNAVAILABLE_FOR_LEGAL_REASONS", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 451}}}, "UNPROCESSABLE_ENTITY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.UNPROCESSABLE_ENTITY", "name": "UNPROCESSABLE_ENTITY", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 422}}}, "UNSUPPORTED_MEDIA_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.UNSUPPORTED_MEDIA_TYPE", "name": "UNSUPPORTED_MEDIA_TYPE", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 415}}}, "UPGRADE_REQUIRED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.UPGRADE_REQUIRED", "name": "UPGRADE_REQUIRED", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 426}}}, "USE_PROXY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.USE_PROXY", "name": "USE_PROXY", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 305}}}, "UnimplementedFileMode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.client.HTTPException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.client.UnimplementedFileMode", "name": "UnimplementedFileMode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.client.UnimplementedFileMode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.client", "mro": ["http.client.UnimplementedFileMode", "http.client.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client.UnimplementedFileMode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.client.UnimplementedFileMode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnknownProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.client.HTTPException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.client.UnknownProtocol", "name": "UnknownProtocol", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.client.UnknownProtocol", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.client", "mro": ["http.client.UnknownProtocol", "http.client.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "version"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "http.client.UnknownProtocol.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "version"], "arg_types": ["http.client.UnknownProtocol", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of UnknownProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client.UnknownProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.client.UnknownProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnknownTransferEncoding": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.client.HTTPException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.client.UnknownTransferEncoding", "name": "UnknownTransferEncoding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.client.UnknownTransferEncoding", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.client", "mro": ["http.client.UnknownTransferEncoding", "http.client.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client.UnknownTransferEncoding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.client.UnknownTransferEncoding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VARIANT_ALSO_NEGOTIATES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.VARIANT_ALSO_NEGOTIATES", "name": "VARIANT_ALSO_NEGOTIATES", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 506}}}, "WriteableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.WriteableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_DataType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "http.client._DataType", "line": 35, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "_typeshed.SupportsRead"}, {".class": "Instance", "args": ["typing_extensions.Buffer"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "typing_extensions.Buffer"], "uses_pep604_syntax": true}}}, "_HeaderValue": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "http.client._HeaderValue", "line": 37, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["typing_extensions.Buffer", "builtins.str", "builtins.int"], "uses_pep604_syntax": true}}}, "_MessageT": {".class": "SymbolTableNode", "cross_ref": "email._policybase._MessageT", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.client._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "http.client.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "email": {".class": "SymbolTableNode", "cross_ref": "email", "kind": "Gdef", "module_hidden": true, "module_public": false}, "error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "http.client.error", "line": 238, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "http.client.HTTPException"}}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "parse_headers": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "http.client.parse_headers", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["fp", "_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "http.client.parse_headers", "name": "parse_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["fp", "_class"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "_typeshed.SupportsReadline"}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": -1, "name": "_MessageT", "namespace": "http.client.parse_headers#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_headers", "ret_type": {".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": -1, "name": "_MessageT", "namespace": "http.client.parse_headers#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": -1, "name": "_MessageT", "namespace": "http.client.parse_headers#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "http.client.parse_headers", "name": "parse_headers", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["fp", "_class"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "_typeshed.SupportsReadline"}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": -1, "name": "_MessageT", "namespace": "http.client.parse_headers#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_headers", "ret_type": {".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": -1, "name": "_MessageT", "namespace": "http.client.parse_headers#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": -1, "name": "_MessageT", "namespace": "http.client.parse_headers#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["fp"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "http.client.parse_headers", "name": "parse_headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["fp"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "_typeshed.SupportsReadline"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_headers", "ret_type": "http.client.HTTPMessage", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "http.client.parse_headers", "name": "parse_headers", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["fp"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "_typeshed.SupportsReadline"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_headers", "ret_type": "http.client.HTTPMessage", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["fp", "_class"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "_typeshed.SupportsReadline"}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": -1, "name": "_MessageT", "namespace": "http.client.parse_headers#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_headers", "ret_type": {".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": -1, "name": "_MessageT", "namespace": "http.client.parse_headers#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "fullname": "email._policybase._MessageT", "id": -1, "name": "_MessageT", "namespace": "http.client.parse_headers#0", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "email.message.Message"}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["fp"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "_typeshed.SupportsReadline"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_headers", "ret_type": "http.client.HTTPMessage", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "responses": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.client.responses", "name": "responses", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket.socket", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/mypy/typeshed/stdlib/http/client.pyi"}