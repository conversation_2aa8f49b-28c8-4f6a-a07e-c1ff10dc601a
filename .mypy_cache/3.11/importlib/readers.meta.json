{"data_mtime": 1753080772, "dep_lines": [19, 9, 15, 19, 5, 6, 7, 8, 10, 11, 12, 16, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 20, 10, 10, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["importlib.resources.abc", "collections.abc", "importlib._bootstrap_external", "importlib.resources", "pathlib", "sys", "zipfile", "_typeshed", "io", "typing", "typing_extensions", "zipimport", "builtins", "_frozen_importlib", "_frozen_importlib_external", "_io", "abc", "os", "types"], "hash": "546107aedd9a5c9e24ba2ec12487a0d340dfaf17", "id": "importlib.readers", "ignore_all": true, "interface_hash": "4b5ca618938603ebbb6e58c76c7599d8f0647260", "mtime": 1753079184, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": ["name-defined"], "disable_memoryview_promotion": false, "disabled_error_codes": ["name-defined"], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/mypy/typeshed/stdlib/importlib/readers.pyi", "plugin_data": null, "size": 2729, "suppressed": [], "version_id": "1.17.0"}