{"data_mtime": 1753080772, "dep_lines": [1, 2, 3, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 30, 30, 30], "dependencies": ["sys", "types", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "08d9b9d7bfd914bab6621d0622ff274447d3da9e", "id": "multiprocessing.queues", "ignore_all": true, "interface_hash": "f675d38f9d67b8ef41b679d2b84de30d4ac67085", "mtime": 1753079184, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": ["name-defined"], "disable_memoryview_promotion": false, "disabled_error_codes": ["name-defined"], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/mypy/typeshed/stdlib/multiprocessing/queues.pyi", "plugin_data": null, "size": 1375, "suppressed": [], "version_id": "1.17.0"}