{"data_mtime": 1753080772, "dep_lines": [2, 3, 1, 4, 5, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 30, 30, 30], "dependencies": ["collections.abc", "multiprocessing.context", "threading", "types", "typing_extensions", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "b4984ed072b33735041ce6f296fcbbc4c39a8a6b", "id": "multiprocessing.synchronize", "ignore_all": true, "interface_hash": "061ccc60ae19550b210c6b38544d4eb3614c4484", "mtime": 1753079184, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": ["name-defined"], "disable_memoryview_promotion": false, "disabled_error_codes": ["name-defined"], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/mypy/typeshed/stdlib/multiprocessing/synchronize.pyi", "plugin_data": null, "size": 2440, "suppressed": [], "version_id": "1.17.0"}