{".class": "MypyFile", "_fullname": "opa_addon_setup_rh", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "OPAAddonSetupHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opa_addon_setup_rh.OPAAddonSetupHandler", "name": "OPAAddonSetupHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "opa_addon_setup_rh.OPAAddonSetupHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "opa_addon_setup_rh", "mro": ["opa_addon_setup_rh.OPAAddonSetupHandler", "builtins.object"], "names": {".class": "SymbolTable", "_get_current_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_addon_setup_rh.OPAAddonSetupHandler._get_current_config", "name": "_get_current_config", "type": null}}, "_save_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_addon_setup_rh.OPAAddonSetupHandler._save_config", "name": "_save_config", "type": null}}, "_update_inputs_conf": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_addon_setup_rh.OPAAddonSetupHandler._update_inputs_conf", "name": "_update_inputs_conf", "type": null}}, "_validate_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_addon_setup_rh.OPAAddonSetupHandler._validate_config", "name": "_validate_config", "type": null}}, "handleEdit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "confInfo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_addon_setup_rh.OPAAddonSetupHandler.handleEdit", "name": "handleEdit", "type": null}}, "handleList": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "confInfo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_addon_setup_rh.OPAAddonSetupHandler.handleList", "name": "handleList", "type": null}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_addon_setup_rh.OPAAddonSetupHandler.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opa_addon_setup_rh.OPAAddonSetupHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opa_addon_setup_rh.OPAAddonSetupHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opa_addon_setup_rh.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opa_addon_setup_rh.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opa_addon_setup_rh.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opa_addon_setup_rh.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opa_addon_setup_rh.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opa_addon_setup_rh.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "admin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "opa_addon_setup_rh.admin", "name": "admin", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "opa_addon_setup_rh.admin", "source_any": null, "type_of_any": 3}}}, "e": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opa_addon_setup_rh.e", "name": "e", "setter_type": null, "type": {".class": "DeletedType", "source": "e"}}}, "en": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "opa_addon_setup_rh.en", "name": "en", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "opa_addon_setup_rh.en", "source_any": null, "type_of_any": 3}}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "make_splunkhome_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "opa_addon_setup_rh.make_splunkhome_path", "name": "make_splunkhome_path", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "opa_addon_setup_rh.make_splunkhome_path", "source_any": null, "type_of_any": 3}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "urlparse": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlparse", "kind": "Gdef"}}, "path": "bin/opa_addon_setup_rh.py"}