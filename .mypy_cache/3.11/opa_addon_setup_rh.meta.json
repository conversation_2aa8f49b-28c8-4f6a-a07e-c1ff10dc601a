{"data_mtime": 1753080772, "dep_lines": [16, 13, 14, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 24, 22, 23, 22], "dep_prios": [5, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10, 10, 20], "dependencies": ["urllib.parse", "logging", "os", "sys", "builtins", "_frozen_importlib", "_typeshed", "abc", "genericpath", "posixpath", "types", "typing", "typing_extensions", "urllib"], "hash": "00a1654b411cad8bd2318a7ae705c8a96eaad5a5", "id": "opa_addon_setup_rh", "ignore_all": false, "interface_hash": "247721a3668ae58304c6c6a230520b3eb776aebc", "mtime": 1753080707, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": ["name-defined"], "disable_memoryview_promotion": false, "disabled_error_codes": ["name-defined"], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "bin/opa_addon_setup_rh.py", "plugin_data": null, "size": 18271, "suppressed": ["splunk.appserver.mrsparkle.lib.util", "splunk.admin", "splunk.entity", "splunk"], "version_id": "1.17.0"}