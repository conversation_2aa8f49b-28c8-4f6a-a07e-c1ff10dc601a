{".class": "MypyFile", "_fullname": "opa_decision_logs", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseHTTPRequestHandler": {".class": "SymbolTableNode", "cross_ref": "http.server.BaseHTTPRequestHandler", "kind": "Gdef"}, "EventWriter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "opa_decision_logs.EventWriter", "name": "EventWriter", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "opa_decision_logs.EventWriter", "source_any": null, "type_of_any": 3}}}, "HTTPServer": {".class": "SymbolTableNode", "cross_ref": "http.server.HTTPServer", "kind": "Gdef"}, "OPADecisionLogsInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opa_decision_logs.OPADecisionLogsInput", "name": "OPADecisionLogsInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "opa_decision_logs.OPADecisionLogsInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "opa_decision_logs", "mro": ["opa_decision_logs.OPADecisionLogsInput", "builtins.object"], "names": {".class": "SymbolTable", "get_scheme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_decision_logs.OPADecisionLogsInput.get_scheme", "name": "get_scheme", "type": null}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opa_decision_logs.OPADecisionLogsInput.logger", "name": "logger", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup_logging": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "log_level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_decision_logs.OPADecisionLogsInput.setup_logging", "name": "setup_logging", "type": null}}, "stream_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "inputs", "ew"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_decision_logs.OPADecisionLogsInput.stream_events", "name": "stream_events", "type": null}}, "validate_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "validation_definition"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_decision_logs.OPADecisionLogsInput.validate_input", "name": "validate_input", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opa_decision_logs.OPADecisionLogsInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opa_decision_logs.OPADecisionLogsInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OPAHTTPServer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opa_decision_logs.OPAHTTPServer", "name": "OPAHTTPServer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opa_decision_logs.OPAHTTPServer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "opa_decision_logs", "mro": ["opa_decision_logs.OPAHTTPServer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "config", "event_writer", "logger"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_decision_logs.OPAHTTPServer.__init__", "name": "__init__", "type": null}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opa_decision_logs.OPAHTTPServer.config", "name": "config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "create_request_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_decision_logs.OPAHTTPServer.create_request_handler", "name": "create_request_handler", "type": null}}, "event_queue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opa_decision_logs.OPAHTTPServer.event_queue", "name": "event_queue", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "event_writer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opa_decision_logs.OPAHTTPServer.event_writer", "name": "event_writer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "health_check_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_decision_logs.OPAHTTPServer.health_check_loop", "name": "health_check_loop", "type": null}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opa_decision_logs.OPAHTTPServer.logger", "name": "logger", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "process_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_decision_logs.OPAHTTPServer.process_events", "name": "process_events", "type": null}}, "running": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opa_decision_logs.OPAHTTPServer.running", "name": "running", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "server": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opa_decision_logs.OPAHTTPServer.server", "name": "server", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "signal_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "signum", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_decision_logs.OPAHTTPServer.signal_handler", "name": "signal_handler", "type": null}}, "start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_decision_logs.OPAHTTPServer.start", "name": "start", "type": null}}, "stats": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opa_decision_logs.OPAHTTPServer.stats", "name": "stats", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "stop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_decision_logs.OPAHTTPServer.stop", "name": "stop", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opa_decision_logs.OPAHTTPServer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opa_decision_logs.OPAHTTPServer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OPARequestHandler@346": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.server.BaseHTTPRequestHandler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opa_decision_logs.OPARequestHandler@346", "name": "OPARequestHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opa_decision_logs.OPARequestHandler@346", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "opa_decision_logs", "mro": ["opa_decision_logs.OPARequestHandler@346", "http.server.BaseHTTPRequestHandler", "socketserver.StreamRequestHandler", "socketserver.BaseRequestHandler", "builtins.object"], "names": {".class": "SymbolTable", "do_GET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_decision_logs.OPARequestHandler@346.do_GET", "name": "do_GET", "type": null}}, "do_POST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_decision_logs.OPARequestHandler@346.do_POST", "name": "do_POST", "type": null}}, "enrich_decision_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "decision_log"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_decision_logs.OPARequestHandler@346.enrich_decision_log", "name": "enrich_decision_log", "type": null}}, "log_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "format", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_decision_logs.OPARequestHandler@346.log_message", "name": "log_message", "type": null}}, "process_decision_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "decision_log"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_decision_logs.OPARequestHandler@346.process_decision_log", "name": "process_decision_log", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "<EMAIL>", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opa_decision_logs.OPARequestHandler@346", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opa_decision_logs.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opa_decision_logs.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opa_decision_logs.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opa_decision_logs.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opa_decision_logs.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opa_decision_logs.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "e": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opa_decision_logs.e", "name": "e", "setter_type": null, "type": {".class": "DeletedType", "source": "e"}}}, "gzip": {".class": "SymbolTableNode", "cross_ref": "gzip", "kind": "Gdef"}, "hashlib": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "queue": {".class": "SymbolTableNode", "cross_ref": "queue", "kind": "Gdef"}, "signal": {".class": "SymbolTableNode", "cross_ref": "signal", "kind": "Gdef"}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "timezone": {".class": "SymbolTableNode", "cross_ref": "datetime.timezone", "kind": "Gdef"}}, "path": "bin/opa_decision_logs.py"}