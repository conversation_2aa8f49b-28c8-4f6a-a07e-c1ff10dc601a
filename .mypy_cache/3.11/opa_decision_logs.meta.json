{"data_mtime": 1753080772, "dep_lines": [24, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 31, 30], "dep_prios": [5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["http.server", "gzip", "<PERSON><PERSON><PERSON>", "json", "logging", "os", "queue", "signal", "ssl", "sys", "threading", "time", "datetime", "builtins", "_frozen_importlib", "_hashlib", "_queue", "_socket", "_ssl", "_typeshed", "abc", "enum", "genericpath", "http", "json.decoder", "json.encoder", "posixpath", "socket", "socketserver", "types", "typing", "typing_extensions"], "hash": "53594ef99a6eb53e4d5f886079fb5fae30bee096", "id": "opa_decision_logs", "ignore_all": false, "interface_hash": "87fe175221a6f2cc9d963cf278123a4b47aafd3a", "mtime": 1753080707, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": ["name-defined"], "disable_memoryview_promotion": false, "disabled_error_codes": ["name-defined"], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "bin/opa_decision_logs.py", "plugin_data": null, "size": 21283, "suppressed": ["splunklib.modularinput.event_writer", "splunklib.modularinput"], "version_id": "1.17.0"}