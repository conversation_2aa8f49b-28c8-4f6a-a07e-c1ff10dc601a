{".class": "MypyFile", "_fullname": "opa_metrics_collector", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "EventWriter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "opa_metrics_collector.EventWriter", "name": "EventWriter", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "opa_metrics_collector.EventWriter", "source_any": null, "type_of_any": 3}}}, "OPAMetricsCollector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opa_metrics_collector.OPAMetricsCollector", "name": "OPAMetricsCollector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opa_metrics_collector.OPAMetricsCollector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "opa_metrics_collector", "mro": ["opa_metrics_collector.OPAMetricsCollector", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "config", "event_writer", "logger"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_metrics_collector.OPAMetricsCollector.__init__", "name": "__init__", "type": null}}, "_collect_prometheus_from_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "metrics_url", "endpoint", "timestamp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_metrics_collector.OPAMetricsCollector._collect_prometheus_from_url", "name": "_collect_prometheus_from_url", "type": null}}, "collect_bundle_statistics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "endpoint", "timestamp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_metrics_collector.OPAMetricsCollector.collect_bundle_statistics", "name": "collect_bundle_statistics", "type": null}}, "collect_endpoint_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "endpoint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_metrics_collector.OPAMetricsCollector.collect_endpoint_metrics", "name": "collect_endpoint_metrics", "type": null}}, "collect_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_metrics_collector.OPAMetricsCollector.collect_metrics", "name": "collect_metrics", "type": null}}, "collect_prometheus_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "endpoint", "timestamp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_metrics_collector.OPAMetricsCollector.collect_prometheus_metrics", "name": "collect_prometheus_metrics", "type": null}}, "collect_query_statistics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "endpoint", "timestamp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_metrics_collector.OPAMetricsCollector.collect_query_statistics", "name": "collect_query_statistics", "type": null}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opa_metrics_collector.OPAMetricsCollector.config", "name": "config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "event_writer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opa_metrics_collector.OPAMetricsCollector.event_writer", "name": "event_writer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opa_metrics_collector.OPAMetricsCollector.logger", "name": "logger", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "metric_filters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opa_metrics_collector.OPAMetricsCollector.metric_filters", "name": "metric_filters", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "parse_prometheus_labels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "labels_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_metrics_collector.OPAMetricsCollector.parse_prometheus_labels", "name": "parse_prometheus_labels", "type": null}}, "parse_prometheus_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metrics_text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_metrics_collector.OPAMetricsCollector.parse_prometheus_metrics", "name": "parse_prometheus_metrics", "type": null}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_metrics_collector.OPAMetricsCollector.run", "name": "run", "type": null}}, "send_metrics_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metrics_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_metrics_collector.OPAMetricsCollector.send_metrics_event", "name": "send_metrics_event", "type": null}}, "session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opa_metrics_collector.OPAMetricsCollector.session", "name": "session", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opa_metrics_collector.OPAMetricsCollector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opa_metrics_collector.OPAMetricsCollector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OPAMetricsCollectorInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opa_metrics_collector.OPAMetricsCollectorInput", "name": "OPAMetricsCollectorInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "opa_metrics_collector.OPAMetricsCollectorInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "opa_metrics_collector", "mro": ["opa_metrics_collector.OPAMetricsCollectorInput", "builtins.object"], "names": {".class": "SymbolTable", "get_scheme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_metrics_collector.OPAMetricsCollectorInput.get_scheme", "name": "get_scheme", "type": null}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opa_metrics_collector.OPAMetricsCollectorInput.logger", "name": "logger", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup_logging": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "log_level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_metrics_collector.OPAMetricsCollectorInput.setup_logging", "name": "setup_logging", "type": null}}, "stream_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "inputs", "ew"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_metrics_collector.OPAMetricsCollectorInput.stream_events", "name": "stream_events", "type": null}}, "test_opa_connectivity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "endpoints", "timeout", "auth_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_metrics_collector.OPAMetricsCollectorInput.test_opa_connectivity", "name": "test_opa_connectivity", "type": null}}, "validate_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "validation_definition"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opa_metrics_collector.OPAMetricsCollectorInput.validate_input", "name": "validate_input", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opa_metrics_collector.OPAMetricsCollectorInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opa_metrics_collector.OPAMetricsCollectorInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opa_metrics_collector.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opa_metrics_collector.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opa_metrics_collector.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opa_metrics_collector.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opa_metrics_collector.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opa_metrics_collector.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "concurrent": {".class": "SymbolTableNode", "cross_ref": "concurrent", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "e": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opa_metrics_collector.e", "name": "e", "setter_type": null, "type": {".class": "DeletedType", "source": "e"}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "requests": {".class": "SymbolTableNode", "cross_ref": "requests", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "timezone": {".class": "SymbolTableNode", "cross_ref": "datetime.timezone", "kind": "Gdef"}, "urljoin": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urljoin", "kind": "Gdef"}, "urlparse": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlparse", "kind": "Gdef"}}, "path": "bin/opa_metrics_collector.py"}