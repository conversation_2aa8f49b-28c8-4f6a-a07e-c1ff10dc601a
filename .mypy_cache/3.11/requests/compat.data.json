{".class": "MypyFile", "_fullname": "requests.compat", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "collections.OrderedDict", "kind": "Gdef"}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.compat.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.compat.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.compat.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.compat.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.compat.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.compat.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "basestring": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.compat.basestring", "name": "basestring", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "builtin_str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "requests.compat.builtin_str", "line": 24, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.str"}}, "bytes": {".class": "SymbolTableNode", "cross_ref": "builtins.bytes", "kind": "Gdef"}, "getproxies": {".class": "SymbolTableNode", "cross_ref": "urllib.request.getproxies", "kind": "Gdef"}, "has_simplejson": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.compat.has_simplejson", "name": "<PERSON>_<PERSON><PERSON><PERSON>", "setter_type": null, "type": "builtins.bool"}}, "integer_types": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.compat.integer_types", "name": "integer_types", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "is_py2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.compat.is_py2", "name": "is_py2", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}}}, "is_py3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.compat.is_py3", "name": "is_py3", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.bool", "value": true}}}, "is_urllib3_1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.compat.is_urllib3_1", "name": "is_urllib3_1", "setter_type": null, "type": "builtins.bool"}}, "numeric_types": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.compat.numeric_types", "name": "numeric_types", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "parse_http_list": {".class": "SymbolTableNode", "cross_ref": "urllib.request.parse_http_list", "kind": "Gdef"}, "proxy_bypass": {".class": "SymbolTableNode", "cross_ref": "urllib.request.proxy_bypass", "kind": "Gdef"}, "quote": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.quote", "kind": "Gdef"}, "quote_plus": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.quote_plus", "kind": "Gdef"}, "str": {".class": "SymbolTableNode", "cross_ref": "builtins.str", "kind": "Gdef"}, "unquote": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.unquote", "kind": "Gdef"}, "unquote_plus": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.unquote_plus", "kind": "Gdef"}, "urldefrag": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urldefrag", "kind": "Gdef"}, "urlencode": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlencode", "kind": "Gdef"}, "urljoin": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urljoin", "kind": "Gdef"}, "urlparse": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlparse", "kind": "Gdef"}, "urlsplit": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlsplit", "kind": "Gdef"}, "urlunparse": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlunparse", "kind": "Gdef"}}, "path": "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/requests-stubs/compat.pyi"}