{"data_mtime": 1753080772, "dep_lines": [3, 8, 8, 8, 8, 9, 11, 11, 11, 11, 11, 11, 12, 14, 1, 2, 4, 5, 6, 8, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 10, 5, 10, 5, 10, 10, 10, 10, 5, 5, 10, 5, 5, 5, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "urllib3.exceptions", "urllib3.fields", "urllib3.filepost", "urllib3.util", "urllib3.response", "requests.auth", "requests.cookies", "requests.exceptions", "requests.hooks", "requests.status_codes", "requests.utils", "requests.adapters", "requests.structures", "datetime", "_typeshed", "json", "typing", "typing_extensions", "urllib3", "requests", "builtins", "_frozen_importlib", "_io", "abc", "http", "http.cookiejar", "io", "json.decoder", "types", "urllib3.connectionpool", "urllib3.util.url"], "hash": "2ad4181833691953d73286c0ba65344f390a25ac", "id": "requests.models", "ignore_all": true, "interface_hash": "0d1fdbc8982b79cdf18d78415ec1095a39d0adf8", "mtime": 1753080752, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": ["name-defined"], "disable_memoryview_promotion": false, "disabled_error_codes": ["name-defined"], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/requests-stubs/models.pyi", "plugin_data": null, "size": 5616, "suppressed": [], "version_id": "1.17.0"}