{".class": "MypyFile", "_fullname": "socket", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AF_APPLETALK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_APPLETALK", "name": "AF_APPLETALK", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_DECnet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.AF_DECnet", "name": "AF_DECnet", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 12}}}, "AF_INET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_INET", "name": "AF_INET", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_INET6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_INET6", "name": "AF_INET6", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_IPX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_IPX", "name": "AF_IPX", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_LINK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_LINK", "name": "AF_LINK", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_ROUTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_ROUTE", "name": "AF_ROUTE", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_SNA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_SNA", "name": "AF_SNA", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_SYSTEM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_SYSTEM", "name": "AF_SYSTEM", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_UNIX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_UNIX", "name": "AF_UNIX", "setter_type": null, "type": "socket.AddressFamily"}}, "AF_UNSPEC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AF_UNSPEC", "name": "AF_UNSPEC", "setter_type": null, "type": "socket.AddressFamily"}}, "AI_ADDRCONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_ADDRCONFIG", "name": "AI_ADDRCONFIG", "setter_type": null, "type": "socket.AddressInfo"}}, "AI_ALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_ALL", "name": "AI_ALL", "setter_type": null, "type": "socket.AddressInfo"}}, "AI_CANONNAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_CANONNAME", "name": "AI_CANONNAME", "setter_type": null, "type": "socket.AddressInfo"}}, "AI_DEFAULT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_DEFAULT", "name": "AI_DEFAULT", "setter_type": null, "type": "socket.AddressInfo"}}, "AI_MASK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_MASK", "name": "AI_MASK", "setter_type": null, "type": "socket.AddressInfo"}}, "AI_NUMERICHOST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_NUMERICHOST", "name": "AI_NUMERICHOST", "setter_type": null, "type": "socket.AddressInfo"}}, "AI_NUMERICSERV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_NUMERICSERV", "name": "AI_NUMERICSERV", "setter_type": null, "type": "socket.AddressInfo"}}, "AI_PASSIVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_PASSIVE", "name": "AI_PASSIVE", "setter_type": null, "type": "socket.AddressInfo"}}, "AI_V4MAPPED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_V4MAPPED", "name": "AI_V4MAPPED", "setter_type": null, "type": "socket.AddressInfo"}}, "AI_V4MAPPED_CFG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.AI_V4MAPPED_CFG", "name": "AI_V4MAPPED_CFG", "setter_type": null, "type": "socket.AddressInfo"}}, "AddressFamily": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.AddressFamily", "name": "AddressFamily", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "socket.AddressFamily", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "socket", "mro": ["socket.AddressFamily", "enum.IntEnum", "builtins.int", "enum.ReprEnum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "AF_APPLETALK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_APPLETALK", "name": "AF_APPLETALK", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, "type_ref": "builtins.int"}}}, "AF_INET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_INET", "name": "AF_INET", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "AF_INET6": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_INET6", "name": "AF_INET6", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 10}, "type_ref": "builtins.int"}}}, "AF_IPX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_IPX", "name": "AF_IPX", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "AF_LINK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_LINK", "name": "AF_LINK", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 33}, "type_ref": "builtins.int"}}}, "AF_ROUTE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_ROUTE", "name": "AF_ROUTE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16}, "type_ref": "builtins.int"}}}, "AF_SNA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_SNA", "name": "AF_SNA", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 22}, "type_ref": "builtins.int"}}}, "AF_SYSTEM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_SYSTEM", "name": "AF_SYSTEM", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 32}, "type_ref": "builtins.int"}}}, "AF_UNIX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_UNIX", "name": "AF_UNIX", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "AF_UNSPEC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressFamily.AF_UNSPEC", "name": "AF_UNSPEC", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.AddressFamily.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.AddressFamily", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AddressInfo": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntFlag"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.AddressInfo", "name": "AddressInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "socket.AddressInfo", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "socket", "mro": ["socket.AddressInfo", "enum.IntFlag", "builtins.int", "enum.ReprEnum", "enum.Flag", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "AI_ADDRCONFIG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_ADDRCONFIG", "name": "AI_ADDRCONFIG", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 32}, "type_ref": "builtins.int"}}}, "AI_ALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_ALL", "name": "AI_ALL", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16}, "type_ref": "builtins.int"}}}, "AI_CANONNAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_CANONNAME", "name": "AI_CANONNAME", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "AI_DEFAULT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_DEFAULT", "name": "AI_DEFAULT", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1536}, "type_ref": "builtins.int"}}}, "AI_MASK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_MASK", "name": "AI_MASK", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 5127}, "type_ref": "builtins.int"}}}, "AI_NUMERICHOST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_NUMERICHOST", "name": "AI_NUMERICHOST", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "AI_NUMERICSERV": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_NUMERICSERV", "name": "AI_NUMERICSERV", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1024}, "type_ref": "builtins.int"}}}, "AI_PASSIVE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_PASSIVE", "name": "AI_PASSIVE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "AI_V4MAPPED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_V4MAPPED", "name": "AI_V4MAPPED", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}, "AI_V4MAPPED_CFG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.AddressInfo.AI_V4MAPPED_CFG", "name": "AI_V4MAPPED_CFG", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 512}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.AddressInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.AddressInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BufferedRWPair": {".class": "SymbolTableNode", "cross_ref": "_io.BufferedRWPair", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BufferedReader": {".class": "SymbolTableNode", "cross_ref": "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BufferedWriter": {".class": "SymbolTableNode", "cross_ref": "_io.BufferedWriter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CAPI": {".class": "SymbolTableNode", "cross_ref": "_socket.CAPI", "kind": "Gdef"}, "CMSG_LEN": {".class": "SymbolTableNode", "cross_ref": "_socket.CMSG_LEN", "kind": "Gdef"}, "CMSG_SPACE": {".class": "SymbolTableNode", "cross_ref": "_socket.CMSG_SPACE", "kind": "Gdef"}, "EAGAIN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.EAGAIN", "name": "EAGAIN", "setter_type": null, "type": "builtins.int"}}, "EAI_ADDRFAMILY": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_ADDRFAMILY", "kind": "Gdef"}, "EAI_AGAIN": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_AGAIN", "kind": "Gdef"}, "EAI_BADFLAGS": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_BADFLAGS", "kind": "Gdef"}, "EAI_BADHINTS": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_BADHINTS", "kind": "Gdef"}, "EAI_FAIL": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_FAIL", "kind": "Gdef"}, "EAI_FAMILY": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_FAMILY", "kind": "Gdef"}, "EAI_MAX": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_MAX", "kind": "Gdef"}, "EAI_MEMORY": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_MEMORY", "kind": "Gdef"}, "EAI_NODATA": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_NODATA", "kind": "Gdef"}, "EAI_NONAME": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_NONAME", "kind": "Gdef"}, "EAI_OVERFLOW": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_OVERFLOW", "kind": "Gdef"}, "EAI_PROTOCOL": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_PROTOCOL", "kind": "Gdef"}, "EAI_SERVICE": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_SERVICE", "kind": "Gdef"}, "EAI_SOCKTYPE": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_SOCKTYPE", "kind": "Gdef"}, "EAI_SYSTEM": {".class": "SymbolTableNode", "cross_ref": "_socket.EAI_SYSTEM", "kind": "Gdef"}, "EBADF": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.EBADF", "name": "EBADF", "setter_type": null, "type": "builtins.int"}}, "EWOULDBLOCK": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.EWOULDBLOCK", "name": "EWOULDBLOCK", "setter_type": null, "type": "builtins.int"}}, "INADDR_ALLHOSTS_GROUP": {".class": "SymbolTableNode", "cross_ref": "_socket.INADDR_ALLHOSTS_GROUP", "kind": "Gdef"}, "INADDR_ANY": {".class": "SymbolTableNode", "cross_ref": "_socket.INADDR_ANY", "kind": "Gdef"}, "INADDR_BROADCAST": {".class": "SymbolTableNode", "cross_ref": "_socket.INADDR_BROADCAST", "kind": "Gdef"}, "INADDR_LOOPBACK": {".class": "SymbolTableNode", "cross_ref": "_socket.INADDR_LOOPBACK", "kind": "Gdef"}, "INADDR_MAX_LOCAL_GROUP": {".class": "SymbolTableNode", "cross_ref": "_socket.INADDR_MAX_LOCAL_GROUP", "kind": "Gdef"}, "INADDR_NONE": {".class": "SymbolTableNode", "cross_ref": "_socket.INADDR_NONE", "kind": "Gdef"}, "INADDR_UNSPEC_GROUP": {".class": "SymbolTableNode", "cross_ref": "_socket.INADDR_UNSPEC_GROUP", "kind": "Gdef"}, "IOBase": {".class": "SymbolTableNode", "cross_ref": "io.IOBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IPPORT_RESERVED": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPORT_RESERVED", "kind": "Gdef"}, "IPPORT_USERRESERVED": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPORT_USERRESERVED", "kind": "Gdef"}, "IPPROTO_AH": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_AH", "kind": "Gdef"}, "IPPROTO_DSTOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_DSTOPTS", "kind": "Gdef"}, "IPPROTO_EGP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_EGP", "kind": "Gdef"}, "IPPROTO_EON": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_EON", "kind": "Gdef"}, "IPPROTO_ESP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_ESP", "kind": "Gdef"}, "IPPROTO_FRAGMENT": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_FRAGMENT", "kind": "Gdef"}, "IPPROTO_GGP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_GGP", "kind": "Gdef"}, "IPPROTO_GRE": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_GRE", "kind": "Gdef"}, "IPPROTO_HELLO": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_HELLO", "kind": "Gdef"}, "IPPROTO_HOPOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_HOPOPTS", "kind": "Gdef"}, "IPPROTO_ICMP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_ICMP", "kind": "Gdef"}, "IPPROTO_ICMPV6": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_ICMPV6", "kind": "Gdef"}, "IPPROTO_IDP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_IDP", "kind": "Gdef"}, "IPPROTO_IGMP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_IGMP", "kind": "Gdef"}, "IPPROTO_IP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_IP", "kind": "Gdef"}, "IPPROTO_IPCOMP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_IPCOMP", "kind": "Gdef"}, "IPPROTO_IPIP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_IPIP", "kind": "Gdef"}, "IPPROTO_IPV4": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_IPV4", "kind": "Gdef"}, "IPPROTO_IPV6": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_IPV6", "kind": "Gdef"}, "IPPROTO_MAX": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_MAX", "kind": "Gdef"}, "IPPROTO_ND": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_ND", "kind": "Gdef"}, "IPPROTO_NONE": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_NONE", "kind": "Gdef"}, "IPPROTO_PIM": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_PIM", "kind": "Gdef"}, "IPPROTO_PUP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_PUP", "kind": "Gdef"}, "IPPROTO_RAW": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_RAW", "kind": "Gdef"}, "IPPROTO_ROUTING": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_ROUTING", "kind": "Gdef"}, "IPPROTO_RSVP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_RSVP", "kind": "Gdef"}, "IPPROTO_SCTP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_SCTP", "kind": "Gdef"}, "IPPROTO_TCP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_TCP", "kind": "Gdef"}, "IPPROTO_TP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_TP", "kind": "Gdef"}, "IPPROTO_UDP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_UDP", "kind": "Gdef"}, "IPPROTO_XTP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPPROTO_XTP", "kind": "Gdef"}, "IPV6_CHECKSUM": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_CHECKSUM", "kind": "Gdef"}, "IPV6_DONTFRAG": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_DONTFRAG", "kind": "Gdef"}, "IPV6_DSTOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_DSTOPTS", "kind": "Gdef"}, "IPV6_HOPLIMIT": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_HOPLIMIT", "kind": "Gdef"}, "IPV6_HOPOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_HOPOPTS", "kind": "Gdef"}, "IPV6_JOIN_GROUP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_JOIN_GROUP", "kind": "Gdef"}, "IPV6_LEAVE_GROUP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_LEAVE_GROUP", "kind": "Gdef"}, "IPV6_MULTICAST_HOPS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_MULTICAST_HOPS", "kind": "Gdef"}, "IPV6_MULTICAST_IF": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_MULTICAST_IF", "kind": "Gdef"}, "IPV6_MULTICAST_LOOP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_MULTICAST_LOOP", "kind": "Gdef"}, "IPV6_NEXTHOP": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_NEXTHOP", "kind": "Gdef"}, "IPV6_PATHMTU": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_PATHMTU", "kind": "Gdef"}, "IPV6_PKTINFO": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_PKTINFO", "kind": "Gdef"}, "IPV6_RECVDSTOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RECVDSTOPTS", "kind": "Gdef"}, "IPV6_RECVHOPLIMIT": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RECVHOPLIMIT", "kind": "Gdef"}, "IPV6_RECVHOPOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RECVHOPOPTS", "kind": "Gdef"}, "IPV6_RECVPATHMTU": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RECVPATHMTU", "kind": "Gdef"}, "IPV6_RECVPKTINFO": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RECVPKTINFO", "kind": "Gdef"}, "IPV6_RECVRTHDR": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RECVRTHDR", "kind": "Gdef"}, "IPV6_RECVTCLASS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RECVTCLASS", "kind": "Gdef"}, "IPV6_RTHDR": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RTHDR", "kind": "Gdef"}, "IPV6_RTHDRDSTOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RTHDRDSTOPTS", "kind": "Gdef"}, "IPV6_RTHDR_TYPE_0": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_RTHDR_TYPE_0", "kind": "Gdef"}, "IPV6_TCLASS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_TCLASS", "kind": "Gdef"}, "IPV6_UNICAST_HOPS": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_UNICAST_HOPS", "kind": "Gdef"}, "IPV6_USE_MIN_MTU": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_USE_MIN_MTU", "kind": "Gdef"}, "IPV6_V6ONLY": {".class": "SymbolTableNode", "cross_ref": "_socket.IPV6_V6ONLY", "kind": "Gdef"}, "IP_ADD_MEMBERSHIP": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_ADD_MEMBERSHIP", "kind": "Gdef"}, "IP_DEFAULT_MULTICAST_LOOP": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_DEFAULT_MULTICAST_LOOP", "kind": "Gdef"}, "IP_DEFAULT_MULTICAST_TTL": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_DEFAULT_MULTICAST_TTL", "kind": "Gdef"}, "IP_DROP_MEMBERSHIP": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_DROP_MEMBERSHIP", "kind": "Gdef"}, "IP_HDRINCL": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_HDRINCL", "kind": "Gdef"}, "IP_MAX_MEMBERSHIPS": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_MAX_MEMBERSHIPS", "kind": "Gdef"}, "IP_MULTICAST_IF": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_MULTICAST_IF", "kind": "Gdef"}, "IP_MULTICAST_LOOP": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_MULTICAST_LOOP", "kind": "Gdef"}, "IP_MULTICAST_TTL": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_MULTICAST_TTL", "kind": "Gdef"}, "IP_OPTIONS": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_OPTIONS", "kind": "Gdef"}, "IP_RECVDSTADDR": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_RECVDSTADDR", "kind": "Gdef"}, "IP_RECVOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_RECVOPTS", "kind": "Gdef"}, "IP_RECVRETOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_RECVRETOPTS", "kind": "Gdef"}, "IP_RECVTOS": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_RECVTOS", "kind": "Gdef"}, "IP_RETOPTS": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_RETOPTS", "kind": "Gdef"}, "IP_TOS": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_TOS", "kind": "Gdef"}, "IP_TTL": {".class": "SymbolTableNode", "cross_ref": "_socket.IP_TTL", "kind": "Gdef"}, "IntEnum": {".class": "SymbolTableNode", "cross_ref": "enum.IntEnum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IntFlag": {".class": "SymbolTableNode", "cross_ref": "enum.IntFlag", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LOCAL_PEERCRED": {".class": "SymbolTableNode", "cross_ref": "_socket.LOCAL_PEERCRED", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MSG_CTRUNC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_CTRUNC", "name": "MSG_CTRUNC", "setter_type": null, "type": "socket.MsgFlag"}}, "MSG_DONTROUTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_DONTROUTE", "name": "MSG_DONTROUTE", "setter_type": null, "type": "socket.MsgFlag"}}, "MSG_DONTWAIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_DONTWAIT", "name": "MSG_DONTWAIT", "setter_type": null, "type": "socket.MsgFlag"}}, "MSG_EOF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_EOF", "name": "MSG_EOF", "setter_type": null, "type": "socket.MsgFlag"}}, "MSG_EOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_EOR", "name": "MSG_EOR", "setter_type": null, "type": "socket.MsgFlag"}}, "MSG_NOSIGNAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_NOSIGNAL", "name": "MSG_NOSIGNAL", "setter_type": null, "type": "socket.MsgFlag"}}, "MSG_OOB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_OOB", "name": "MSG_OOB", "setter_type": null, "type": "socket.MsgFlag"}}, "MSG_PEEK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_PEEK", "name": "MSG_PEEK", "setter_type": null, "type": "socket.MsgFlag"}}, "MSG_TRUNC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_TRUNC", "name": "MSG_TRUNC", "setter_type": null, "type": "socket.MsgFlag"}}, "MSG_WAITALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.MSG_WAITALL", "name": "MSG_WAITALL", "setter_type": null, "type": "socket.MsgFlag"}}, "MsgFlag": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntFlag"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.MsgFlag", "name": "MsgFlag", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "socket.MsgFlag", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "socket", "mro": ["socket.MsgFlag", "enum.IntFlag", "builtins.int", "enum.ReprEnum", "enum.Flag", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "MSG_CTRUNC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_CTRUNC", "name": "MSG_CTRUNC", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}, "MSG_DONTROUTE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_DONTROUTE", "name": "MSG_DONTROUTE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "MSG_DONTWAIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_DONTWAIT", "name": "MSG_DONTWAIT", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 64}, "type_ref": "builtins.int"}}}, "MSG_EOF": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_EOF", "name": "MSG_EOF", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 256}, "type_ref": "builtins.int"}}}, "MSG_EOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_EOR", "name": "MSG_EOR", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 128}, "type_ref": "builtins.int"}}}, "MSG_NOSIGNAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_NOSIGNAL", "name": "MSG_NOSIGNAL", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16384}, "type_ref": "builtins.int"}}}, "MSG_OOB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_OOB", "name": "MSG_OOB", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "MSG_PEEK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_PEEK", "name": "MSG_PEEK", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "MSG_TRUNC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_TRUNC", "name": "MSG_TRUNC", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 32}, "type_ref": "builtins.int"}}}, "MSG_WAITALL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.MsgFlag.MSG_WAITALL", "name": "MSG_WAITALL", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 256}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.MsgFlag.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.MsgFlag", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NI_DGRAM": {".class": "SymbolTableNode", "cross_ref": "_socket.NI_DGRAM", "kind": "Gdef"}, "NI_MAXHOST": {".class": "SymbolTableNode", "cross_ref": "_socket.NI_MAXHOST", "kind": "Gdef"}, "NI_MAXSERV": {".class": "SymbolTableNode", "cross_ref": "_socket.NI_MAXSERV", "kind": "Gdef"}, "NI_NAMEREQD": {".class": "SymbolTableNode", "cross_ref": "_socket.NI_NAMEREQD", "kind": "Gdef"}, "NI_NOFQDN": {".class": "SymbolTableNode", "cross_ref": "_socket.NI_NOFQDN", "kind": "Gdef"}, "NI_NUMERICHOST": {".class": "SymbolTableNode", "cross_ref": "_socket.NI_NUMERICHOST", "kind": "Gdef"}, "NI_NUMERICSERV": {".class": "SymbolTableNode", "cross_ref": "_socket.NI_NUMERICSERV", "kind": "Gdef"}, "PF_SYSTEM": {".class": "SymbolTableNode", "cross_ref": "_socket.PF_SYSTEM", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RawIOBase": {".class": "SymbolTableNode", "cross_ref": "io.RawIOBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SCM_CREDS": {".class": "SymbolTableNode", "cross_ref": "_socket.SCM_CREDS", "kind": "Gdef"}, "SCM_RIGHTS": {".class": "SymbolTableNode", "cross_ref": "_socket.SCM_RIGHTS", "kind": "Gdef"}, "SHUT_RD": {".class": "SymbolTableNode", "cross_ref": "_socket.SHUT_RD", "kind": "Gdef"}, "SHUT_RDWR": {".class": "SymbolTableNode", "cross_ref": "_socket.SHUT_RDWR", "kind": "Gdef"}, "SHUT_WR": {".class": "SymbolTableNode", "cross_ref": "_socket.SHUT_WR", "kind": "Gdef"}, "SOCK_DGRAM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.SOCK_DGRAM", "name": "SOCK_DGRAM", "setter_type": null, "type": "socket.SocketKind"}}, "SOCK_RAW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.SOCK_RAW", "name": "SOCK_RAW", "setter_type": null, "type": "socket.SocketKind"}}, "SOCK_RDM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.SOCK_RDM", "name": "SOCK_RDM", "setter_type": null, "type": "socket.SocketKind"}}, "SOCK_SEQPACKET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.SOCK_SEQPACKET", "name": "SOCK_SEQPACKET", "setter_type": null, "type": "socket.SocketKind"}}, "SOCK_STREAM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.SOCK_STREAM", "name": "SOCK_STREAM", "setter_type": null, "type": "socket.SocketKind"}}, "SOL_IP": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_IP", "kind": "Gdef"}, "SOL_SOCKET": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_SOCKET", "kind": "Gdef"}, "SOL_TCP": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_TCP", "kind": "Gdef"}, "SOL_UDP": {".class": "SymbolTableNode", "cross_ref": "_socket.SOL_UDP", "kind": "Gdef"}, "SOMAXCONN": {".class": "SymbolTableNode", "cross_ref": "_socket.SOMAXCONN", "kind": "Gdef"}, "SO_ACCEPTCONN": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_ACCEPTCONN", "kind": "Gdef"}, "SO_BROADCAST": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_BROADCAST", "kind": "Gdef"}, "SO_DEBUG": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_DEBUG", "kind": "Gdef"}, "SO_DONTROUTE": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_DONTROUTE", "kind": "Gdef"}, "SO_ERROR": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_ERROR", "kind": "Gdef"}, "SO_KEEPALIVE": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_KEEPALIVE", "kind": "Gdef"}, "SO_LINGER": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_LINGER", "kind": "Gdef"}, "SO_OOBINLINE": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_OOBINLINE", "kind": "Gdef"}, "SO_RCVBUF": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_RCVBUF", "kind": "Gdef"}, "SO_RCVLOWAT": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_RCVLOWAT", "kind": "Gdef"}, "SO_RCVTIMEO": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_RCVTIMEO", "kind": "Gdef"}, "SO_REUSEADDR": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_REUSEADDR", "kind": "Gdef"}, "SO_REUSEPORT": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_REUSEPORT", "kind": "Gdef"}, "SO_SNDBUF": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_SNDBUF", "kind": "Gdef"}, "SO_SNDLOWAT": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_SNDLOWAT", "kind": "Gdef"}, "SO_SNDTIMEO": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_SNDTIMEO", "kind": "Gdef"}, "SO_TYPE": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_TYPE", "kind": "Gdef"}, "SO_USELOOPBACK": {".class": "SymbolTableNode", "cross_ref": "_socket.SO_USELOOPBACK", "kind": "Gdef"}, "SYSPROTO_CONTROL": {".class": "SymbolTableNode", "cross_ref": "_socket.SYSPROTO_CONTROL", "kind": "Gdef"}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SocketIO": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["io.RawIOBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.SocketIO", "name": "SocketIO", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "socket.SocketIO", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "socket", "mro": ["socket.SocketIO", "io.RawIOBase", "_io._RawIOBase", "io.IOBase", "_io._IOBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "sock", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "socket.SocketIO.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "sock", "mode"], "arg_types": ["socket.SocketIO", "socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SocketIO", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "socket.SocketIO.mode", "name": "mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.SocketIO"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mode of SocketIO", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "socket.SocketIO.mode", "name": "mode", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.SocketIO"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mode of SocketIO", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "socket.SocketIO.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.SocketIO"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "name of SocketIO", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "socket.SocketIO.name", "name": "name", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.SocketIO"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "name of SocketIO", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "readinto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "socket.SocketIO.readinto", "name": "readinto", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "b"], "arg_types": ["socket.SocketIO", "typing_extensions.Buffer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "readinto of SocketIO", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "socket.SocketIO.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "b"], "arg_types": ["socket.SocketIO", "typing_extensions.Buffer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "write of SocketIO", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.SocketIO.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.SocketIO", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SocketKind": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.SocketKind", "name": "SocketKind", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "socket.SocketKind", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "socket", "mro": ["socket.SocketKind", "enum.IntEnum", "builtins.int", "enum.ReprEnum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "SOCK_DGRAM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.SocketKind.SOCK_DGRAM", "name": "SOCK_DGRAM", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "SOCK_RAW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.SocketKind.SOCK_RAW", "name": "SOCK_RAW", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "SOCK_RDM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.SocketKind.SOCK_RDM", "name": "SOCK_RDM", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "SOCK_SEQPACKET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.SocketKind.SOCK_SEQPACKET", "name": "SOCK_SEQPACKET", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, "type_ref": "builtins.int"}}}, "SOCK_STREAM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "socket.SocketKind.SOCK_STREAM", "name": "SOCK_STREAM", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.SocketKind.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.SocketKind", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SocketType": {".class": "SymbolTableNode", "cross_ref": "_socket.SocketType", "kind": "Gdef"}, "SupportsIndex": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsIndex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TCP_CONNECTION_INFO": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_CONNECTION_INFO", "kind": "Gdef"}, "TCP_FASTOPEN": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_FASTOPEN", "kind": "Gdef"}, "TCP_KEEPALIVE": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_KEEPALIVE", "kind": "Gdef"}, "TCP_KEEPCNT": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_KEEPCNT", "kind": "Gdef"}, "TCP_KEEPINTVL": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_KEEPINTVL", "kind": "Gdef"}, "TCP_MAXSEG": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_MAXSEG", "kind": "Gdef"}, "TCP_NODELAY": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_NODELAY", "kind": "Gdef"}, "TCP_NOTSENT_LOWAT": {".class": "SymbolTableNode", "cross_ref": "_socket.TCP_NOTSENT_LOWAT", "kind": "Gdef"}, "TextIOWrapper": {".class": "SymbolTableNode", "cross_ref": "_io.TextIOWrapper", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Unused": {".class": "SymbolTableNode", "cross_ref": "_typeshed.Unused", "kind": "Gdef", "module_hidden": true, "module_public": false}, "WriteableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.WriteableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Address": {".class": "SymbolTableNode", "cross_ref": "_socket._Address", "kind": "Gdef", "module_public": false}, "_RetAddress": {".class": "SymbolTableNode", "cross_ref": "_socket._RetAddress", "kind": "Gdef", "module_public": false}, "_SendableFile": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket._SendableFile", "name": "_SendableFile", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "socket._SendableFile", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "socket", "mro": ["socket._SendableFile", "builtins.object"], "names": {".class": "SymbolTable", "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "socket._SendableFile.read", "name": "read", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["socket._SendableFile", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read of _SendableFile", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "seek": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "socket._SendableFile.seek", "name": "seek", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["socket._SendableFile", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "seek of _SendableFile", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket._SendableFile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket._SendableFile", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "socket.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "socket.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_socket": {".class": "SymbolTableNode", "cross_ref": "_socket", "kind": "Gdef", "module_hidden": true, "module_public": false}, "close": {".class": "SymbolTableNode", "cross_ref": "_socket.close", "kind": "Gdef"}, "create_connection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5], "arg_names": ["address", "timeout", "source_address", "all_errors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.create_connection", "name": "create_connection", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["address", "timeout", "source_address", "all_errors"], "arg_types": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_connection", "ret_type": "socket.socket", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_server": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["address", "family", "backlog", "reuse_port", "dualstack_ipv6"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.create_server", "name": "create_server", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["address", "family", "backlog", "reuse_port", "dualstack_ipv6"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_socket._Address"}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_server", "ret_type": "socket.socket", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dup": {".class": "SymbolTableNode", "cross_ref": "_socket.dup", "kind": "Gdef"}, "error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "socket.error", "line": 1066, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.OSError"}}, "fromfd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["fd", "family", "type", "proto"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.fromfd", "name": "fromfd", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["fd", "family", "type", "proto"], "arg_types": ["typing.SupportsIndex", {".class": "UnionType", "items": ["socket.AddressFamily", "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["socket.SocketKind", "builtins.int"], "uses_pep604_syntax": true}, "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fromfd", "ret_type": "socket.socket", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gaierror": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.OSError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.gaierror", "name": "gaier<PERSON>r", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "socket.gaierror", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "socket", "mro": ["socket.gaierror", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.gaierror.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.gaierror", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "getaddrinfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["host", "port", "family", "type", "proto", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.getaddrinfo", "name": "getaddrinfo", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["host", "port", "family", "type", "proto", "flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", "builtins.str", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getaddrinfo", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["socket.AddressFamily", "socket.SocketKind", "builtins.int", "builtins.str", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getdefaulttimeout": {".class": "SymbolTableNode", "cross_ref": "_socket.getdefaulttimeout", "kind": "Gdef"}, "getfqdn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.getfqdn", "name": "getfqdn", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["name"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "getfqdn", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gethostbyaddr": {".class": "SymbolTableNode", "cross_ref": "_socket.gethostbyaddr", "kind": "Gdef"}, "gethostbyname": {".class": "SymbolTableNode", "cross_ref": "_socket.gethostbyname", "kind": "Gdef"}, "gethostbyname_ex": {".class": "SymbolTableNode", "cross_ref": "_socket.gethostbyname_ex", "kind": "Gdef"}, "gethostname": {".class": "SymbolTableNode", "cross_ref": "_socket.gethostname", "kind": "Gdef"}, "getnameinfo": {".class": "SymbolTableNode", "cross_ref": "_socket.getnameinfo", "kind": "Gdef"}, "getprotobyname": {".class": "SymbolTableNode", "cross_ref": "_socket.getprotobyname", "kind": "Gdef"}, "getservbyname": {".class": "SymbolTableNode", "cross_ref": "_socket.getservbyname", "kind": "Gdef"}, "getservbyport": {".class": "SymbolTableNode", "cross_ref": "_socket.getservbyport", "kind": "Gdef"}, "has_dualstack_ipv6": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.has_dualstack_ipv6", "name": "has_dualstack_ipv6", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "has_dualstack_ipv6", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_ipv6": {".class": "SymbolTableNode", "cross_ref": "_socket.has_ipv6", "kind": "Gdef"}, "herror": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.OSError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.herror", "name": "herror", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "socket.herror", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "socket", "mro": ["socket.herror", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.herror.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.herror", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "htonl": {".class": "SymbolTableNode", "cross_ref": "_socket.htonl", "kind": "Gdef"}, "htons": {".class": "SymbolTableNode", "cross_ref": "_socket.htons", "kind": "Gdef"}, "if_indextoname": {".class": "SymbolTableNode", "cross_ref": "_socket.if_indextoname", "kind": "Gdef"}, "if_nameindex": {".class": "SymbolTableNode", "cross_ref": "_socket.if_nameindex", "kind": "Gdef"}, "if_nametoindex": {".class": "SymbolTableNode", "cross_ref": "_socket.if_nametoindex", "kind": "Gdef"}, "inet_aton": {".class": "SymbolTableNode", "cross_ref": "_socket.inet_aton", "kind": "Gdef"}, "inet_ntoa": {".class": "SymbolTableNode", "cross_ref": "_socket.inet_ntoa", "kind": "Gdef"}, "inet_ntop": {".class": "SymbolTableNode", "cross_ref": "_socket.inet_ntop", "kind": "Gdef"}, "inet_pton": {".class": "SymbolTableNode", "cross_ref": "_socket.inet_pton", "kind": "Gdef"}, "ntohl": {".class": "SymbolTableNode", "cross_ref": "_socket.ntohl", "kind": "Gdef"}, "ntohs": {".class": "SymbolTableNode", "cross_ref": "_socket.ntohs", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "recv_fds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["sock", "bufsize", "maxfds", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.recv_fds", "name": "recv_fds", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["sock", "bufsize", "maxfds", "flags"], "arg_types": ["socket.socket", "builtins.int", "builtins.int", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "recv_fds", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_fds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["sock", "buffers", "fds", "flags", "address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.send_fds", "name": "send_fds", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["sock", "buffers", "fds", "flags", "address"], "arg_types": ["socket.socket", {".class": "Instance", "args": ["typing_extensions.Buffer"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.object", "builtins.object"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send_fds", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setdefaulttimeout": {".class": "SymbolTableNode", "cross_ref": "_socket.setdefaulttimeout", "kind": "Gdef"}, "sethostname": {".class": "SymbolTableNode", "cross_ref": "_socket.sethostname", "kind": "Gdef"}, "socket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_socket.socket"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "socket.socket", "name": "socket", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "socket.socket", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "socket", "mro": ["socket.socket", "_socket.socket", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.socket.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.socket", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__enter__ of socket", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.socket", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.socket", "values": [], "variance": 0}]}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "socket.socket.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, null], "arg_types": ["socket.socket", "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__exit__ of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "family", "type", "proto", "fileno"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "socket.socket.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "family", "type", "proto", "fileno"], "arg_types": ["socket.socket", {".class": "UnionType", "items": ["socket.AddressFamily", "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["socket.SocketKind", "builtins.int"], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "accept": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "socket.socket.accept", "name": "accept", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "accept of socket", "ret_type": {".class": "TupleType", "implicit": false, "items": ["socket.socket", {".class": "TypeAliasType", "args": [], "type_ref": "_socket._RetAddress"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.socket.dup", "name": "dup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.socket", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "dup of socket", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.socket", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.socket", "values": [], "variance": 0}]}}}, "family": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "socket.socket.family", "name": "family", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "family of socket", "ret_type": "socket.AddressFamily", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "socket.socket.family", "name": "family", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "family of socket", "ret_type": "socket.AddressFamily", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_inheritable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "socket.socket.get_inheritable", "name": "get_inheritable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_inheritable of socket", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "makefile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "socket.socket.makefile", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "socket.SocketIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "socket.socket.makefile", "name": "makefile", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "socket.SocketIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": {".class": "Instance", "args": ["_io._BufferedReaderStream"], "extra_attrs": null, "type_ref": "_io.BufferedRWPair"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "socket.socket.makefile", "name": "makefile", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": {".class": "Instance", "args": ["_io._BufferedReaderStream"], "extra_attrs": null, "type_ref": "_io.BufferedRWPair"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": {".class": "Instance", "args": ["_io._BufferedReaderStream"], "extra_attrs": null, "type_ref": "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "socket.socket.makefile", "name": "makefile", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": {".class": "Instance", "args": ["_io._BufferedReaderStream"], "extra_attrs": null, "type_ref": "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "_io.BufferedWriter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "socket.socket.makefile", "name": "makefile", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "_io.BufferedWriter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "io.IOBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "socket.socket.makefile", "name": "makefile", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "io.IOBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "socket.socket.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ""}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": {".class": "Instance", "args": ["_io._W<PERSON><PERSON><PERSON>er"], "extra_attrs": null, "type_ref": "_io.TextIOWrapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "socket.socket.makefile", "name": "makefile", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ""}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": {".class": "Instance", "args": ["_io._W<PERSON><PERSON><PERSON>er"], "extra_attrs": null, "type_ref": "_io.TextIOWrapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "socket.SocketIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": {".class": "Instance", "args": ["_io._BufferedReaderStream"], "extra_attrs": null, "type_ref": "_io.BufferedRWPair"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": {".class": "Instance", "args": ["_io._BufferedReaderStream"], "extra_attrs": null, "type_ref": "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": -1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "_io.BufferedWriter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "b"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "br"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rwb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rbw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wbr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "brw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bwr"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": "io.IOBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rw"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": ""}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "makefile of socket", "ret_type": {".class": "Instance", "args": ["_io._W<PERSON><PERSON><PERSON>er"], "extra_attrs": null, "type_ref": "_io.TextIOWrapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "sendfile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "file", "offset", "count"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "socket.socket.sendfile", "name": "sendfile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "file", "offset", "count"], "arg_types": ["socket.socket", "socket._SendableFile", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sendfile of socket", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_inheritable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "inheritable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "socket.socket.set_inheritable", "name": "set_inheritable", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "inheritable"], "arg_types": ["socket.socket", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_inheritable of socket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "socket.socket.type", "name": "type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "type of socket", "ret_type": "socket.SocketKind", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "socket.socket.type", "name": "type", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["socket.socket"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "type of socket", "ret_type": "socket.SocketKind", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "socket.socket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "socket.socket", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "socketpair": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1], "arg_names": ["family", "type", "proto"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "socket.socketpair", "name": "socketpair", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["family", "type", "proto"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", "socket.AddressFamily", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["_socket.socket", "builtins.int"], "uses_pep604_syntax": true}, "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "socketpair", "ret_type": {".class": "TupleType", "implicit": false, "items": ["socket.socket", "socket.socket"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "timeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "socket.timeout", "line": 1072, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.TimeoutError"}}}, "path": "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/mypy/typeshed/stdlib/socket.pyi"}