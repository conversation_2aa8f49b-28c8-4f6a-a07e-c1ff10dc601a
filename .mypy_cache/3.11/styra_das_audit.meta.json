{"data_mtime": 1753080772, "dep_lines": [13, 14, 15, 16, 17, 18, 19, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 28, 27], "dep_prios": [10, 10, 10, 10, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["<PERSON><PERSON><PERSON>", "json", "logging", "os", "sys", "time", "datetime", "requests", "builtins", "_frozen_importlib", "_hashlib", "_io", "_typeshed", "abc", "genericpath", "http", "http.cookiejar", "io", "json.decoder", "json.encoder", "posixpath", "requests.api", "requests.auth", "requests.exceptions", "requests.models", "requests.sessions", "types", "typing", "typing_extensions"], "hash": "eaf3b57361af599c9a306c4d888d07da0d51ca4e", "id": "styra_das_audit", "ignore_all": false, "interface_hash": "e8d8dd9a60f8b76548a4113b44e3ad9b09398c7d", "mtime": 1753080706, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": ["name-defined"], "disable_memoryview_promotion": false, "disabled_error_codes": ["name-defined"], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "bin/styra_das_audit.py", "plugin_data": null, "size": 22977, "suppressed": ["splunklib.modularinput.event_writer", "splunklib.modularinput"], "version_id": "1.17.0"}