{"data_mtime": 1753080772, "dep_lines": [62, 63, 14, 19, 46, 48, 49, 50, 66, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 46, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 25, 23, 30], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 25, 5, 10, 10, 10, 10, 10, 5, 10, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10, 10], "dependencies": ["urllib3.util.response", "urllib3.util.retry", "http.client", "urllib3._base_connection", "urllib3.util", "urllib3._collections", "urllib3.connection", "urllib3.exceptions", "urllib3.connectionpool", "__future__", "collections", "io", "json", "logging", "re", "socket", "sys", "typing", "warnings", "zlib", "contextlib", "urllib3", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "email", "email.message", "enum", "http", "types", "typing_extensions", "urllib3._request_methods"], "hash": "5be274e23b8b5e396f60f5f1276c97f52da55b2e", "id": "urllib3.response", "ignore_all": true, "interface_hash": "1443eaa7854370367df2b0c3bdce314d393cc180", "mtime": 1748371165, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": ["name-defined"], "disable_memoryview_promotion": false, "disabled_error_codes": ["name-defined"], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/response.py", "plugin_data": null, "size": 45190, "suppressed": ["brotli", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zstandard"], "version_id": "1.17.0"}