{"data_mtime": 1753080772, "dep_lines": [7, 6, 12, 1, 3, 4, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 5, 10, 10, 5, 30, 30, 30, 30], "dependencies": ["urllib3.util.timeout", "urllib3.exceptions", "urllib3._base_connection", "__future__", "socket", "typing", "builtins", "_frozen_importlib", "_socket", "abc", "enum"], "hash": "f4235cec4a7e67717e649a33df5a2f4f6abfbbb9", "id": "urllib3.util.connection", "ignore_all": true, "interface_hash": "6982c0eeaff0e62494d26918e268fbf418fa1165", "mtime": 1748371165, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": ["name-defined"], "disable_memoryview_promotion": false, "disabled_error_codes": ["name-defined"], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/util/connection.py", "plugin_data": null, "size": 4444, "suppressed": [], "version_id": "1.17.0"}