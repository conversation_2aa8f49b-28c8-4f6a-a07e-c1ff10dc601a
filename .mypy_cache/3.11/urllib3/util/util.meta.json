{"data_mtime": 1753080772, "dep_lines": [1, 3, 4, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 30, 30], "dependencies": ["__future__", "typing", "types", "builtins", "_frozen_importlib", "abc"], "hash": "ac7a579ce7e37ce8bf215f97b9bfb80b149b0f68", "id": "urllib3.util.util", "ignore_all": true, "interface_hash": "c278d53c2f96f3bb4c28ed7694444238821cbbfc", "mtime": 1748371165, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": ["name-defined"], "disable_memoryview_promotion": false, "disabled_error_codes": ["name-defined"], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/util/util.py", "plugin_data": null, "size": 1146, "suppressed": [], "version_id": "1.17.0"}