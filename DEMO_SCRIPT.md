# Demo Script - OPA Policy Audit & Compliance Add-on

## 🎬 Video Demo Script (5-7 minutes)

This script provides a structured walkthrough for demonstrating the OPA Policy Audit & Compliance Add-on's key features and capabilities.

## 📋 Demo Preparation

### Pre-Demo Setup
1. **Splunk Environment**: Clean Splunk instance with add-on installed
2. **Sample Data**: Pre-loaded with diverse OPA decision logs
3. **Mock Services**: OPA health endpoint and Styra DAS mock running
4. **Dashboards**: Key dashboards bookmarked and ready
5. **Searches**: Saved searches prepared for quick access

### Demo Data Preparation Script
```bash
#!/bin/bash
# File: prepare_demo_data.sh

echo "Preparing demo data for OPA Policy Audit Add-on..."

# 1. Load sample decision logs
for i in {1..50}; do
  # Mix of allowed and denied decisions
  result=$([ $((RANDOM % 3)) -eq 0 ] && echo "false" || echo "true")
  user="user$((RANDOM % 10 + 1))"
  method=$([ $((RANDOM % 2)) -eq 0 ] && echo "GET" || echo "POST")
  
  curl -s -X POST http://localhost:8088/opadecisions \
    -H "Content-Type: application/json" \
    -d "{
      \"decision_id\": \"demo-$i\",
      \"timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)\",
      \"path\": \"data/authz/$([ $result == 'true' ] && echo 'allow' || echo 'admin')\",
      \"result\": $result,
      \"input\": {
        \"user\": \"$user\",
        \"method\": \"$method\",
        \"path\": \"/api/$([ $result == 'true' ] && echo 'public' || echo 'admin')/data\",
        \"client_ip\": \"192.168.1.$((RANDOM % 254 + 1))\"
      },
      \"metrics\": {
        \"timer_rego_query_eval_ns\": $((RANDOM % 5000000 + 500000)),
        \"timer_server_handler_ns\": $((RANDOM % 8000000 + 1000000))
      },
      \"requested_by\": \"service-$((RANDOM % 5 + 1))\",
      \"revision\": \"v1.0.$((RANDOM % 10))\"
    }"
  
  sleep 0.1
done

echo "Demo data preparation completed!"
```

## 🎯 Demo Flow

### Opening (30 seconds)
**Script**: 
"Welcome to the demonstration of the OPA Policy Audit & Compliance Add-on for Splunk. This comprehensive solution bridges the gap between Open Policy Agent policy enforcement and enterprise security monitoring, providing real-time visibility into policy decisions, compliance tracking, and security analytics."

**Screen**: 
- Show Splunk main interface
- Navigate to Apps and highlight the OPA add-on
- Brief overview of the app icon and description

### Section 1: Problem Statement (45 seconds)
**Script**: 
"Organizations using Open Policy Agent face critical challenges: limited visibility into policy decisions, compliance blind spots, and difficulty tracking security violations. Our add-on solves these problems by providing comprehensive monitoring, automated compliance reporting, and advanced security analytics."

**Screen**: 
- Show a simple diagram or slide explaining:
  - OPA instances scattered across infrastructure
  - Policy decisions happening in isolation
  - Need for centralized monitoring

### Section 2: Real-time Data Ingestion (60 seconds)
**Script**: 
"Let's start by demonstrating real-time data ingestion. The add-on includes an HTTP listener that receives OPA decision logs in real-time. Watch as we send live policy decisions to Splunk."

**Demo Steps**:
1. **Show the HTTP listener configuration**:
   ```splunk
   # Navigate to Settings > Data Inputs > OPA Decision Logs
   # Show the configured input with port 8088
   ```

2. **Send live data**:
   ```bash
   # Run in terminal (visible on screen)
   curl -X POST http://localhost:8088/opadecisions \
     -H "Content-Type: application/json" \
     -d '{
       "decision_id": "live-demo-001",
       "timestamp": "2024-01-15T10:30:00.123Z",
       "path": "data/authz/admin",
       "result": false,
       "input": {
         "user": "demo.user",
         "method": "DELETE",
         "path": "/api/admin/users"
       }
     }'
   ```

3. **Show data appearing in Splunk**:
   ```splunk
   index=opa_audit sourcetype="opa:decision" decision_id="live-demo-001"
   ```

**Screen**: 
- Split screen: terminal on left, Splunk search on right
- Show the curl command executing
- Immediately refresh Splunk search to show the new event
- Highlight key extracted fields

### Section 3: Security Monitoring Dashboard (90 seconds)
**Script**: 
"Now let's explore our comprehensive security monitoring capabilities. The add-on provides real-time dashboards for security operations teams to monitor policy violations, user behavior, and potential threats."

**Demo Steps**:
1. **Navigate to Security Dashboard**:
   - Go to the main security monitoring dashboard
   - Show overview metrics: total decisions, violation rate, top users

2. **Policy Violations Panel**:
   ```splunk
   index=opa_audit sourcetype="opa:decision" result="false"
   | stats count by input_user, path, input_method
   | sort -count
   ```
   
3. **Real-time Alerting**:
   - Show saved search for high-risk violations
   - Demonstrate alert configuration
   - Show email/webhook notification setup

4. **User Behavior Analytics**:
   ```splunk
   index=opa_audit sourcetype="opa:decision"
   | stats count as total_requests,
           sum(eval(if(result="false", 1, 0))) as denied_requests
           by input_user
   | eval denial_rate=round((denied_requests/total_requests)*100, 2)
   | sort -denial_rate
   ```

**Screen**: 
- Show interactive dashboard with:
  - Real-time violation counts
  - Geographic risk map (if available)
  - Top violating users
  - Policy performance metrics
  - Trend analysis charts

### Section 4: Compliance Management (75 seconds)
**Script**: 
"Compliance is critical for enterprise organizations. Our add-on provides automated compliance tracking and reporting for major regulatory frameworks including SOX, PCI DSS, and HIPAA."

**Demo Steps**:
1. **Compliance Dashboard**:
   - Navigate to compliance overview dashboard
   - Show compliance score and status indicators

2. **Audit Trail Generation**:
   ```splunk
   index=opa_audit
   | eval compliance_event=case(
       result="false" AND match(path, "admin|sensitive"), "violation",
       sourcetype="styra:das:policy:audit", "policy_change",
       1=1, "normal"
     )
   | stats count by compliance_event, _time
   | timechart span=1h count by compliance_event
   ```

3. **Automated Reporting**:
   - Show scheduled compliance reports
   - Demonstrate PDF generation
   - Show email delivery configuration

4. **Risk Scoring**:
   ```splunk
   index=opa_audit sourcetype="opa:decision" result="false"
   | lookup policy_criticality_lookup path OUTPUT criticality
   | eval risk_score=case(
       criticality="high", 10,
       criticality="medium", 5,
       1=1, 1
     )
   | stats sum(risk_score) as total_risk by input_user
   | sort -total_risk
   ```

**Screen**: 
- Compliance dashboard showing:
  - Overall compliance percentage
  - Regulatory framework status
  - Recent violations timeline
  - Risk score trends
  - Automated report schedule

### Section 5: Performance Monitoring (60 seconds)
**Script**: 
"Beyond security, the add-on provides comprehensive performance monitoring for your OPA infrastructure, helping you optimize policy evaluation and ensure system reliability."

**Demo Steps**:
1. **Performance Dashboard**:
   - Show OPA instance health overview
   - Display response time metrics
   - Show throughput statistics

2. **Health Monitoring**:
   ```splunk
   index=opa_audit sourcetype="opa:health"
   | stats latest(status) as current_status,
           avg(response_time_ms) as avg_response_time
           by opa_endpoint
   | eval health_status=case(
       current_status="healthy" AND avg_response_time<100, "excellent",
       current_status="healthy", "good",
       1=1, "attention_needed"
     )
   ```

3. **Performance Trends**:
   ```splunk
   index=opa_audit sourcetype="opa:decision"
   | eval slow_query=if(query_duration_ms>100, 1, 0)
   | timechart span=5m
       avg(query_duration_ms) as avg_duration,
       sum(slow_query) as slow_queries
   ```

**Screen**: 
- Performance dashboard with:
  - OPA instance status indicators
  - Response time charts
  - Query performance metrics
  - Capacity utilization graphs
  - Alert thresholds

### Section 6: CIM Compliance & Integration (45 seconds)
**Script**: 
"The add-on is fully compliant with Splunk's Common Information Model, enabling seamless integration with existing security tools and Enterprise Security."

**Demo Steps**:
1. **Data Model Acceleration**:
   ```splunk
   | datamodel Authentication_OPA Authentication search
   | head 10
   | table _time user src dest action signature
   ```

2. **Enterprise Security Integration**:
   - Show how OPA data appears in ES dashboards
   - Demonstrate correlation with other security events
   - Show notable events generation

3. **SOAR Integration**:
   - Show webhook configuration for automated response
   - Demonstrate incident creation workflow

**Screen**: 
- Split view showing:
  - Data model browser with OPA models
  - Enterprise Security dashboard with OPA events
  - Sample correlation search results

### Section 7: Advanced Analytics (60 seconds)
**Script**: 
"The add-on includes advanced analytics capabilities, including machine learning-based anomaly detection and predictive compliance scoring."

**Demo Steps**:
1. **Anomaly Detection**:
   ```splunk
   index=opa_audit sourcetype="opa:decision"
   | bucket _time span=1h
   | stats count by _time, input_user
   | outlier action=RM
   | where isOutlier=1
   | sort -_time
   ```

2. **Behavioral Analysis**:
   ```splunk
   index=opa_audit sourcetype="opa:decision"
   | eval hour=strftime(_time, "%H")
   | stats count by input_user, hour
   | eventstats avg(count) as avg_count by hour
   | eval anomaly_score=abs(count-avg_count)/avg_count
   | where anomaly_score>0.5
   ```

3. **Predictive Analytics**:
   - Show trend analysis for compliance scores
   - Demonstrate forecasting capabilities
   - Show risk prediction models

**Screen**: 
- Advanced analytics dashboard with:
  - Anomaly detection results
  - User behavior heatmaps
  - Predictive compliance trends
  - Risk forecasting charts

### Section 8: Configuration & Deployment (30 seconds)
**Script**: 
"The add-on is designed for easy deployment and configuration. It supports multiple installation methods and provides comprehensive configuration options for different environments."

**Demo Steps**:
1. **Show Configuration Interface**:
   - Navigate to add-on configuration page
   - Show input configuration options
   - Demonstrate credential management

2. **Deployment Options**:
   - Show package structure
   - Mention Splunk Cloud compatibility
   - Highlight AppInspect compliance

**Screen**: 
- Configuration interface showing:
  - Input configuration forms
  - Credential management
  - Health check status
  - Deployment options

### Closing (30 seconds)
**Script**: 
"The OPA Policy Audit & Compliance Add-on transforms how organizations monitor and manage their authorization infrastructure. With real-time visibility, automated compliance, and advanced analytics, it empowers security teams to proactively protect their environments while ensuring regulatory compliance. Thank you for watching this demonstration."

**Screen**: 
- Summary slide showing key benefits:
  - Real-time policy monitoring
  - Automated compliance reporting
  - Advanced security analytics
  - CIM compliance
  - Easy deployment

## 🎥 Technical Demo Tips

### Screen Recording Setup
1. **Resolution**: 1920x1080 minimum
2. **Frame Rate**: 30 FPS
3. **Audio**: Clear narration with minimal background noise
4. **Cursor**: Highlight cursor for visibility
5. **Zoom**: Use zoom for detailed views

### Splunk Interface Tips
1. **Clean Environment**: Remove unnecessary apps and data
2. **Fast Searches**: Use summary indexes for quick results
3. **Bookmarks**: Pre-bookmark all dashboards and searches
4. **Time Range**: Use recent time ranges for fast loading
5. **Data Volume**: Ensure sufficient demo data is loaded

### Presentation Flow
1. **Smooth Transitions**: Practice transitions between sections
2. **Timing**: Keep each section within time limits
3. **Error Handling**: Have backup plans for technical issues
4. **Engagement**: Use clear, confident narration
5. **Value Focus**: Emphasize business value throughout

## 📊 Demo Metrics to Highlight

### Security Metrics
- **Policy Violations**: 15% denial rate (realistic)
- **Top Violating Users**: Show 3-5 users with high violation counts
- **Geographic Risks**: Show violations from unusual locations
- **Time-based Patterns**: Show after-hours access attempts

### Performance Metrics
- **Response Times**: Average 50ms, some outliers at 200ms+
- **Throughput**: 1000+ decisions per minute
- **Health Status**: 95%+ uptime across instances
- **Query Performance**: 80% queries under 100ms

### Compliance Metrics
- **Overall Score**: 85% compliance (room for improvement)
- **Trend**: Improving over time
- **Violations**: Clear categorization by severity
- **Audit Readiness**: 100% audit trail coverage

## 🔧 Demo Environment Checklist

### Pre-Demo (1 hour before)
- [ ] Splunk environment running and accessible
- [ ] Add-on installed and configured
- [ ] Demo data loaded (50+ events)
- [ ] Mock services running (OPA health, Styra DAS)
- [ ] Dashboards loading quickly
- [ ] Saved searches tested
- [ ] Network connectivity verified
- [ ] Screen recording software tested

### During Demo
- [ ] Clear, confident narration
- [ ] Smooth transitions between sections
- [ ] Highlight key features and benefits
- [ ] Show real data and realistic scenarios
- [ ] Demonstrate business value
- [ ] Handle any technical issues gracefully

### Post-Demo
- [ ] Save recording in multiple formats
- [ ] Create shorter highlight clips
- [ ] Add captions/subtitles if needed
- [ ] Prepare for questions and follow-up

---

*This demo script provides a comprehensive walkthrough of the OPA Policy Audit & Compliance Add-on, showcasing its key features and business value in a structured, engaging format.*