# 🚀 Deployment Guide - OPA Policy Audit & Compliance Add-on

## Overview
This guide provides step-by-step instructions for deploying the OPA Policy Audit & Compliance Add-on across different environments.

## 📋 Prerequisites

### System Requirements
- **Splunk Enterprise**: 8.0+ or Splunk Cloud
- **Python**: 3.7+ (included with <PERSON><PERSON>lunk)
- **Memory**: Minimum 512MB available
- **Disk Space**: 100MB for app + log storage
- **Network**: HTTPS access to OPA instances and Styra DAS

### Required Permissions
- Splunk admin access for app installation
- Network access to OPA instances
- API credentials for Styra DAS (if applicable)
- SSL certificates (for secure connections)

## 🏗️ Deployment Architecture

### Single Instance Deployment
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   OPA Instance  │────│  Splunk Server  │────│   Styra DAS     │
│                 │    │   + OPA Add-on  │    │                 │
│ Decision Logs   │    │                 │    │ Policy Audits   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Distributed Deployment
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   OPA Cluster   │────│ Heavy Forwarder │────│ Indexer Cluster │
│                 │    │   + OPA Add-on  │    │                 │
│ Multiple Nodes  │    │                 │    │ Search Heads    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐
                       │   Styra DAS     │
                       │                 │
                       │ Policy Audits   │
                       └─────────────────┘
```

## 🔧 Installation Methods

### Method 1: Splunk Web Interface

1. **Download the App Package**
   ```bash
   # Create the .spl package
   python setup.py splunk_package
   ```

2. **Install via Splunk Web**
   - Navigate to Apps → Manage Apps
   - Click "Install app from file"
   - Upload `opa_policy_audit_addon-1.0.0.spl`
   - Click "Upload" and follow prompts

3. **Restart Splunk**
   ```bash
   $SPLUNK_HOME/bin/splunk restart
   ```

### Method 2: Command Line Installation

1. **Extract to Apps Directory**
   ```bash
   cd $SPLUNK_HOME/etc/apps/
   tar -xzf /path/to/opa_policy_audit_addon-1.0.0.spl
   chown -R splunk:splunk opa_policy_audit_addon/
   ```

2. **Install Dependencies**
   ```bash
   cd opa_policy_audit_addon/
   $SPLUNK_HOME/bin/splunk cmd python -m pip install -r requirements.txt
   ```

3. **Restart Splunk**
   ```bash
   $SPLUNK_HOME/bin/splunk restart
   ```

### Method 3: Deployment Server

1. **Prepare Deployment Package**
   ```bash
   # Create deployment-ready package
   mkdir -p $SPLUNK_HOME/etc/deployment-apps/opa_policy_audit_addon/
   cp -r . $SPLUNK_HOME/etc/deployment-apps/opa_policy_audit_addon/
   ```

2. **Configure Server Classes**
   ```ini
   # serverclass.conf
   [serverClass:opa_monitoring]
   whitelist.0 = *forwarder*
   
   [serverClass:opa_monitoring:app:opa_policy_audit_addon]
   restartSplunkd = true
   stateOnClient = enabled
   ```

3. **Deploy to Clients**
   ```bash
   $SPLUNK_HOME/bin/splunk reload deploy-server
   ```

## ⚙️ Configuration

### 1. OPA Decision Logs Input

**Via Splunk Web:**
1. Navigate to Settings → Data Inputs
2. Click "OPA Decision Logs HTTP Listener"
3. Click "New" to create input
4. Configure parameters:

```
Name: opa_decisions_prod
HTTP Port: 8088
HTTP Path: /opadecisions
SSL Enabled: Yes
SSL Certificate: /opt/splunk/etc/certs/server.pem
SSL Private Key: /opt/splunk/etc/certs/server.key
Max Content Length: 10485760
Max Connections: 100
Index: opa_decisions
Source Type: opa:decision:log
```

**Via Configuration File:**
```ini
# inputs.conf
[opa_decision_logs://opa_decisions_prod]
http_port = 8088
http_path = /opadecisions
ssl_enabled = 1
ssl_cert_path = /opt/splunk/etc/certs/server.pem
ssl_key_path = /opt/splunk/etc/certs/server.key
max_content_length = 10485760
max_connections = 100
index = opa_decisions
sourcetype = opa:decision:log
disabled = 0
```

### 2. Styra DAS Audit Input

**Via Splunk Web:**
1. Navigate to Settings → Data Inputs
2. Click "Styra DAS Policy Audit Logs"
3. Click "New" to create input
4. Configure parameters:

```
Name: styra_das_audit_prod
API Endpoint: https://tenant.styra.com/v1/systems/systemid/audits
API Token: [Secure Token]
Polling Interval: 300
Max Events Per Request: 1000
Timeout: 30
Index: styra_audits
Source Type: styra:das:audit
```

**Via Configuration File:**
```ini
# inputs.conf
[styra_das_audit://styra_das_audit_prod]
api_endpoint = https://tenant.styra.com/v1/systems/systemid/audits
api_token = $encrypted_token$
polling_interval = 300
max_events_per_request = 1000
timeout = 30
index = styra_audits
sourcetype = styra:das:audit
disabled = 0
```

### 3. Index Configuration

```ini
# indexes.conf
[opa_decisions]
homePath = $SPLUNK_DB/opa_decisions/db
coldPath = $SPLUNK_DB/opa_decisions/colddb
thawedPath = $SPLUNK_DB/opa_decisions/thaweddb
maxDataSize = auto_high_volume
maxHotBuckets = 10
maxWarmDBCount = 300
maxTotalDataSizeMB = 500000

[styra_audits]
homePath = $SPLUNK_DB/styra_audits/db
coldPath = $SPLUNK_DB/styra_audits/colddb
thawedPath = $SPLUNK_DB/styra_audits/thaweddb
maxDataSize = auto_high_volume
maxHotBuckets = 10
maxWarmDBCount = 300
maxTotalDataSizeMB = 500000
```

## 🌍 Environment-Specific Deployments

### Development Environment

**Characteristics:**
- Single Splunk instance
- Mock OPA services
- Debug logging enabled
- Minimal security

**Configuration:**
```ini
# Development settings
[opa_decision_logs://dev_input]
http_port = 8088
ssl_enabled = 0
log_level = DEBUG
index = opa_dev
```

### Staging Environment

**Characteristics:**
- Production-like setup
- Real OPA instances (non-prod)
- Performance monitoring
- Security enabled

**Configuration:**
```ini
# Staging settings
[opa_decision_logs://staging_input]
http_port = 8088
ssl_enabled = 1
ssl_cert_path = /opt/splunk/etc/certs/staging.pem
ssl_key_path = /opt/splunk/etc/certs/staging.key
log_level = INFO
index = opa_staging
```

### Production Environment

**Characteristics:**
- High availability
- Full security
- Monitoring and alerting
- Backup and recovery

**Configuration:**
```ini
# Production settings
[opa_decision_logs://prod_input]
http_port = 8088
ssl_enabled = 1
ssl_cert_path = /opt/splunk/etc/certs/production.pem
ssl_key_path = /opt/splunk/etc/certs/production.key
log_level = WARN
index = opa_decisions
max_connections = 1000
```

## 🔒 Security Configuration

### SSL/TLS Setup

1. **Generate Certificates**
   ```bash
   # Self-signed certificate (development)
   openssl req -x509 -newkey rsa:4096 -keyout server.key -out server.pem -days 365 -nodes
   
   # Move to Splunk certs directory
   mv server.* $SPLUNK_HOME/etc/certs/
   chown splunk:splunk $SPLUNK_HOME/etc/certs/server.*
   chmod 600 $SPLUNK_HOME/etc/certs/server.key
   ```

2. **Configure Certificate Authority**
   ```ini
   # server.conf
   [sslConfig]
   serverCert = $SPLUNK_HOME/etc/certs/server.pem
   sslPassword = password
   caCertFile = $SPLUNK_HOME/etc/certs/ca.pem
   ```

### Credential Management

1. **Store API Tokens Securely**
   ```bash
   # Using Splunk CLI
   $SPLUNK_HOME/bin/splunk add credential \
     -name styra_api_token \
     -user admin \
     -password "your-api-token-here"
   ```

2. **Reference in Configuration**
   ```ini
   # Use encrypted credential reference
   api_token = $encrypted:styra_api_token$
   ```

## 📊 Monitoring & Validation

### Health Checks

1. **Verify App Installation**
   ```bash
   $SPLUNK_HOME/bin/splunk display app opa_policy_audit_addon
   ```

2. **Check Input Status**
   ```bash
   $SPLUNK_HOME/bin/splunk list inputstatus
   ```

3. **Monitor Logs**
   ```bash
   tail -f $SPLUNK_HOME/var/log/splunk/splunkd.log | grep -i opa
   ```

### Performance Monitoring

**Key Metrics to Monitor:**
- Event ingestion rate
- Processing latency
- Memory usage
- CPU utilization
- Network bandwidth

**Splunk Searches:**
```spl
# Event ingestion rate
index=_internal source=*metrics.log component=Metrics group=per_index_thruput series=opa_decisions
| timechart span=1m avg(kb) as "KB/min"

# Input performance
index=_internal source=*metrics.log component=Metrics group=modular_inputs
| search data.input_name="opa_decision_logs*"
| timechart span=5m avg(data.events_processed) as "Events/5min"
```

## 🚨 Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Check port usage
   netstat -tulpn | grep :8088
   
   # Change port in configuration
   http_port = 8089
   ```

2. **SSL Certificate Issues**
   ```bash
   # Verify certificate
   openssl x509 -in server.pem -text -noout
   
   # Test SSL connection
   openssl s_client -connect localhost:8088
   ```

3. **Permission Errors**
   ```bash
   # Fix ownership
   chown -R splunk:splunk $SPLUNK_HOME/etc/apps/opa_policy_audit_addon/
   
   # Fix permissions
   chmod -R 755 $SPLUNK_HOME/etc/apps/opa_policy_audit_addon/
   ```

### Log Analysis

**Important Log Files:**
- `$SPLUNK_HOME/var/log/splunk/splunkd.log` - Main Splunk log
- `$SPLUNK_HOME/var/log/splunk/python.log` - Python script logs
- `$SPLUNK_HOME/var/log/splunk/web_service.log` - Web interface logs

**Log Search Patterns:**
```bash
# OPA-related errors
grep -i "opa\|error\|exception" $SPLUNK_HOME/var/log/splunk/splunkd.log

# Modular input logs
grep "ModularInputs" $SPLUNK_HOME/var/log/splunk/splunkd.log
```

## 🔄 Rollback Procedures

### Emergency Rollback

1. **Disable Inputs**
   ```bash
   $SPLUNK_HOME/bin/splunk disable input opa_decision_logs
   $SPLUNK_HOME/bin/splunk disable input styra_das_audit
   ```

2. **Remove App**
   ```bash
   $SPLUNK_HOME/bin/splunk remove app opa_policy_audit_addon
   ```

3. **Restore Previous Version**
   ```bash
   cd $SPLUNK_HOME/etc/apps/
   tar -xzf opa_policy_audit_addon-backup.tar.gz
   $SPLUNK_HOME/bin/splunk restart
   ```

### Graceful Rollback

1. **Stop Data Collection**
   - Disable inputs via Splunk Web
   - Wait for current processing to complete

2. **Backup Current Data**
   ```bash
   $SPLUNK_HOME/bin/splunk export index opa_decisions -output backup.csv
   ```

3. **Deploy Previous Version**
   - Follow standard deployment process
   - Restore configurations
   - Re-enable inputs

## 📞 Support & Maintenance

### Regular Maintenance Tasks

1. **Weekly**
   - Review error logs
   - Check disk space
   - Validate data ingestion

2. **Monthly**
   - Update certificates (if needed)
   - Review performance metrics
   - Update documentation

3. **Quarterly**
   - Security review
   - Capacity planning
   - Feature updates

### Support Contacts

- **Technical Issues**: GitHub Issues
- **Security Concerns**: <EMAIL>
- **General Questions**: Community Forum
- **Enterprise Support**: Available for customers

---

**Document Version**: 1.0.0  
**Last Updated**: $(date +%Y-%m-%d)  
**Next Review**: Quarterly