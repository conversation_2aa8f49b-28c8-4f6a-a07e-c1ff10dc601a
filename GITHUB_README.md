# OPA Policy Audit & Compliance Add-on for Splunk

[![Splunk AppInspect](https://img.shields.io/badge/Splunk-AppInspect%20Ready-blue)](https://dev.splunk.com/enterprise/docs/developapps/testvalidate/appinspect/)
[![License](https://img.shields.io/badge/License-Apache%202.0-green.svg)](https://opensource.org/licenses/Apache-2.0)
[![Python](https://img.shields.io/badge/Python-3.7%2B-blue)](https://www.python.org/downloads/)
[![Splunk](https://img.shields.io/badge/Splunk-8.0%2B-orange)](https://www.splunk.com/)

## 🚀 Overview

The **OPA Policy Audit & Compliance Add-on** is a comprehensive Splunk Technology Add-on (TA) that bridges the gap between Open Policy Agent (OPA) policy enforcement and enterprise security monitoring. This add-on enables organizations to gain unprecedented visibility into their policy decisions, compliance posture, and security events through real-time data ingestion and advanced analytics.

### 🎯 Key Features

- **Real-time Decision Log Ingestion**: High-performance HTTP listener for OPA decision logs
- **Styra DAS Integration**: Seamless integration with Styra Declarative Authorization Service
- **Health & Performance Monitoring**: Comprehensive OPA instance monitoring
- **CIM Compliance**: Full Common Information Model compliance for enterprise integration
- **Advanced Analytics**: ML-powered anomaly detection and risk scoring
- **Enterprise Security Integration**: Native integration with Splunk ES and SOAR platforms

## 📋 Table of Contents

- [Problem Statement](#-problem-statement)
- [Solution Benefits](#-solution-benefits)
- [Target Users](#-target-users)
- [Quick Start](#-quick-start)
- [Installation](#-installation)
- [Configuration](#-configuration)
- [Testing](#-testing)
- [API Endpoints](#-api-endpoints)
- [Data Sources](#-data-sources)
- [Architecture](#-architecture)
- [Compliance](#-compliance)
- [Troubleshooting](#-troubleshooting)
- [Contributing](#-contributing)
- [License](#-license)

## 🎯 Problem Statement

Modern enterprises face critical challenges in policy governance and compliance:

### Security Blind Spots
- **Lack of Visibility**: Organizations cannot see what policies are being evaluated or their outcomes
- **Compliance Gaps**: No centralized view of policy compliance across distributed systems
- **Incident Response**: Difficulty correlating policy violations with security incidents

### Operational Challenges
- **Performance Issues**: No insight into policy evaluation performance and bottlenecks
- **Troubleshooting**: Complex debugging of policy decisions across microservices
- **Capacity Planning**: Inability to predict and plan for policy infrastructure scaling

### Governance Requirements
- **Audit Trails**: Regulatory requirements for complete audit trails of access decisions
- **Risk Management**: Need for real-time risk assessment based on policy violations
- **Change Management**: Tracking and analyzing the impact of policy changes

## 💡 Solution Benefits

### For Security Teams
- **🔍 Enhanced Visibility**: Real-time monitoring of all policy decisions across your infrastructure
- **🚨 Proactive Alerting**: Immediate notification of policy violations and anomalous behavior
- **📊 Risk Assessment**: Automated risk scoring based on policy violations and user behavior
- **🔗 Incident Correlation**: Link policy violations to security incidents for faster response

### For Compliance Teams
- **📋 Audit Readiness**: Complete audit trails with detailed policy decision logs
- **📈 Compliance Dashboards**: Real-time compliance posture monitoring
- **📝 Automated Reporting**: Scheduled compliance reports for regulatory requirements
- **🎯 Policy Effectiveness**: Measure and improve policy effectiveness over time

### For Operations Teams
- **⚡ Performance Monitoring**: Track policy evaluation performance and identify bottlenecks
- **🔧 Troubleshooting**: Detailed logs for debugging policy decisions
- **📊 Capacity Planning**: Data-driven insights for infrastructure scaling
- **🏥 Health Monitoring**: Comprehensive monitoring of OPA instance health

### For DevOps Teams
- **🔄 CI/CD Integration**: Monitor policy changes and their impact in real-time
- **🐛 Debugging**: Detailed decision logs for troubleshooting application issues
- **📈 Performance Optimization**: Identify and optimize slow policy evaluations
- **🔒 Security Integration**: Seamless integration with existing security toolchains

## 👥 Target Users

### Primary Users
- **Security Analysts**: Monitor policy violations and investigate security incidents
- **Compliance Officers**: Ensure regulatory compliance and generate audit reports
- **DevOps Engineers**: Monitor application policy decisions and troubleshoot issues
- **Platform Engineers**: Manage OPA infrastructure and ensure optimal performance

### Secondary Users
- **Security Architects**: Design and implement policy-based security controls
- **Auditors**: Review policy compliance and access patterns
- **Risk Managers**: Assess and manage policy-related risks
- **IT Operations**: Monitor overall system health and performance

## 🚀 Quick Start

### Prerequisites
- Splunk Enterprise 8.0+ or Splunk Cloud
- Python 3.7+
- OPA instances with decision logging enabled
- Network connectivity between Splunk and OPA instances

### 5-Minute Setup

1. **Download and Install**
   ```bash
   # Download the add-on
   git clone https://github.com/your-org/opa-splunk-addon.git
   cd opa-splunk-addon
   
   # Install in Splunk
   cp -r . $SPLUNK_HOME/etc/apps/opa_policy_audit/
   ```

2. **Configure OPA Decision Logging**
   ```yaml
   # Add to your OPA configuration
   decision_logs:
     service: splunk_addon
     reporting:
       min_delay_seconds: 1
       max_delay_seconds: 5
   
   services:
     splunk_addon:
       url: http://your-splunk-server:8088/services/collector/event
       headers:
         Authorization: "Splunk your-hec-token"
   ```

3. **Start Data Collection**
   ```bash
   # Restart Splunk
   $SPLUNK_HOME/bin/splunk restart
   
   # Verify data ingestion
   # In Splunk Search: index=opa_audit sourcetype=opa:decision
   ```

4. **Access Dashboards**
   - Navigate to the "OPA Policy Audit" app in Splunk
   - View real-time policy decisions and compliance status

## 📦 Installation

### Method 1: Splunk Web Interface

1. **Download the Add-on**
   - Download the latest release from GitHub
   - Extract the archive

2. **Install via Splunk Web**
   - Go to Apps → Manage Apps
   - Click "Install app from file"
   - Upload the add-on package
   - Restart Splunk

### Method 2: Command Line Installation

```bash
# Clone the repository
git clone https://github.com/your-org/opa-splunk-addon.git
cd opa-splunk-addon

# Copy to Splunk apps directory
sudo cp -r . $SPLUNK_HOME/etc/apps/opa_policy_audit/

# Set proper permissions
sudo chown -R splunk:splunk $SPLUNK_HOME/etc/apps/opa_policy_audit/

# Restart Splunk
sudo $SPLUNK_HOME/bin/splunk restart
```

### Method 3: Deployment Server

```bash
# Copy to deployment server
cp -r . $SPLUNK_HOME/etc/deployment-apps/opa_policy_audit/

# Reload deployment server
$SPLUNK_HOME/bin/splunk reload deploy-server
```

## ⚙️ Configuration

### 1. HTTP Decision Log Listener

```ini
# inputs.conf
[opa_decision_logs://default]
interval = 60
index = opa_audit
sourcetype = opa:decision
http_port = 8088
ssl_enabled = 1
ssl_cert_path = /opt/splunk/etc/certs/server.crt
ssl_key_path = /opt/splunk/etc/certs/server.key
max_connections = 100
request_timeout = 30
log_level = INFO
disabled = 0
```

### 2. Styra DAS Integration

```ini
# inputs.conf
[styra_das_audit://default]
interval = 300
index = opa_audit
sourcetype = styra:das:policy:audit
api_endpoint = https://your-tenant.styra.com
api_token = your-api-token
system_id = your-system-id
max_events_per_request = 1000
request_timeout = 60
disabled = 0
```

### 3. Health Monitoring

```ini
# inputs.conf
[opa_health_monitor://default]
interval = 60
index = opa_audit
sourcetype = opa:health
opa_endpoints = http://opa1:8181,http://opa2:8181,http://opa3:8181
health_check_timeout = 10
metrics_enabled = 1
disabled = 0
```

### 4. Index Configuration

```ini
# indexes.conf
[opa_audit]
homePath = $SPLUNK_DB/opa_audit/db
coldPath = $SPLUNK_DB/opa_audit/colddb
thawedPath = $SPLUNK_DB/opa_audit/thaweddb
maxDataSize = auto_high_volume
maxHotBuckets = 10
maxWarmDBCount = 300
frozenTimePeriodInSecs = 63072000  # 2 years
compressRawdata = true
```

## 🧪 Testing

### Automated Test Suite

```bash
# Run all tests
cd tests/
python run_tests.py

# Run specific test module
python -m pytest test_opa_decision_logs.py -v

# Run with coverage
python -m pytest --cov=../bin/ --cov-report=html
```

### Manual Testing

#### 1. Test HTTP Decision Log Ingestion

```bash
# Send test decision log
curl -X POST http://localhost:8088/services/collector/event \
  -H "Authorization: Splunk your-hec-token" \
  -H "Content-Type: application/json" \
  -d '{
    "event": {
      "decision_id": "test-001",
      "timestamp": "2024-01-15T10:30:00Z",
      "path": "data.authz.allow",
      "result": true,
      "input": {
        "user": "alice",
        "action": "read",
        "resource": "/api/users"
      },
      "metrics": {
        "timer_rego_query_eval_ns": 1500000
      }
    }
  }'

# Verify in Splunk
# Search: index=opa_audit sourcetype=opa:decision decision_id="test-001"
```

#### 2. Test Styra DAS Integration

```python
# Mock Styra DAS server for testing
python tests/mock_styra_server.py

# Configure add-on to use mock server
# Update inputs.conf with mock server URL

# Verify data ingestion
# Search: index=opa_audit sourcetype=styra:das:policy:audit
```

#### 3. Test Health Monitoring

```python
# Start mock OPA server
python tests/mock_opa_server.py

# Verify health data
# Search: index=opa_audit sourcetype=opa:health
```

### Performance Testing

```python
# Load test script
python tests/load_test.py --events-per-second 100 --duration 300

# Monitor performance
# Search: index=_internal source=*opa_policy_audit* | stats avg(duration) by component
```

### Expected Test Results

- **Functional Tests**: All tests should pass with 100% success rate
- **Integration Tests**: Data should appear in Splunk within 30 seconds
- **Performance Tests**: Should handle 1000+ events/second with <2 second latency
- **Load Tests**: Should maintain performance under sustained load

## 🌐 API Endpoints

### OPA Decision Logs API

**Endpoint**: `POST /services/collector/event`
**Purpose**: Receive OPA decision logs via HTTP Event Collector

```json
{
  "event": {
    "decision_id": "uuid-string",
    "timestamp": "2024-01-15T10:30:00Z",
    "path": "data.authz.allow",
    "result": true,
    "input": {
      "user": "alice",
      "action": "read",
      "resource": "/api/users",
      "context": {
        "ip": "*************",
        "user_agent": "curl/7.68.0"
      }
    },
    "metrics": {
      "timer_rego_query_eval_ns": 1500000,
      "timer_rego_module_compile_ns": 500000
    }
  }
}
```

### Styra DAS API

**Endpoint**: `GET /v1/systems/{system_id}/audits`
**Purpose**: Fetch policy audit events from Styra DAS

**Authentication**: Bearer token
**Rate Limit**: 100 requests/minute

### OPA Health API

**Endpoints**:
- `GET /health` - Basic health check
- `GET /metrics` - Prometheus metrics
- `GET /v1/status` - Detailed status information

## 📊 Data Sources

### 1. OPA Decision Logs

**Source**: OPA instances with decision logging enabled
**Format**: JSON over HTTP
**Volume**: 1000+ events/second (typical enterprise)
**Retention**: 2 years (configurable)

**Sample Event**:
```json
{
  "decision_id": "4ca636c1-55e4-417a-b1d8-4aceb67960d1",
  "timestamp": "2024-01-15T10:30:00.123456Z",
  "path": "data.authz.allow",
  "result": false,
  "input": {
    "user": "bob",
    "action": "delete",
    "resource": "/api/admin/users",
    "context": {
      "ip": "*********",
      "user_agent": "Mozilla/5.0...",
      "time_of_day": "22:30"
    }
  },
  "metrics": {
    "timer_rego_query_eval_ns": 2500000,
    "timer_rego_module_compile_ns": 750000
  },
  "erased": [],
  "masked": []
}
```

### 2. Styra DAS Policy Audits

**Source**: Styra DAS API
**Format**: JSON via REST API
**Volume**: 100-1000 events/day (typical)
**Polling**: Every 5 minutes

**Sample Event**:
```json
{
  "id": "audit-001",
  "timestamp": "2024-01-15T09:15:00Z",
  "event_type": "policy_change",
  "user": "<EMAIL>",
  "system_id": "my-system",
  "policy_path": "policies/rbac.rego",
  "action": "update",
  "details": {
    "changes": [
      {
        "type": "rule_added",
        "rule": "allow { input.user.role == \"admin\" }"
      }
    ]
  }
}
```

### 3. OPA Health Metrics

**Source**: OPA health and metrics endpoints
**Format**: JSON via HTTP polling
**Volume**: Continuous monitoring
**Frequency**: Every 60 seconds

**Sample Event**:
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "instance": "opa-prod-01",
  "endpoint": "http://opa-prod-01:8181",
  "status": "healthy",
  "response_time_ms": 15,
  "metrics": {
    "go_memstats_alloc_bytes": 8388608,
    "go_memstats_sys_bytes": 16777216,
    "http_request_duration_seconds": 0.002,
    "opa_decision_evaluation_duration_ns": 1500000
  }
}
```

## 🏗️ Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   OPA Cluster   │───▶│  Splunk Add-on  │───▶│ Splunk Platform │
│                 │    │                 │    │                 │
│ • Decision Logs │    │ • HTTP Listener │    │ • Indexing      │
│ • Health Data   │    │ • Data Processing│    │ • Search        │
│ • Metrics       │    │ • Field Extract │    │ • Dashboards    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Styra DAS     │───▶│  API Client     │───▶│ Analytics       │
│                 │    │                 │    │                 │
│ • Policy Audits │    │ • Authentication│    │ • CIM Compliance│
│ • Change Events │    │ • Rate Limiting │    │ • ML Analytics  │
│ • Bundle Info   │    │ • Error Handling│    │ • Alerting      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Component Details

- **HTTP Listener**: High-performance HTTP server for real-time decision log ingestion
- **API Client**: REST client for Styra DAS integration with authentication and rate limiting
- **Health Monitor**: Periodic health checking of OPA instances
- **Data Processor**: Event parsing, validation, and enrichment
- **Field Extractor**: Optimized field extraction using compiled regex patterns
- **CIM Mapper**: Common Information Model compliance mapping

### Data Flow

1. **Ingestion**: OPA sends decision logs to HTTP listener
2. **Processing**: Events are parsed, validated, and enriched
3. **Indexing**: Processed events are indexed in Splunk
4. **Analytics**: Real-time dashboards and alerting
5. **Integration**: Enterprise Security and SOAR integration

## ✅ Compliance

### Splunk AppInspect

This add-on is designed to pass all Splunk AppInspect checks:

- ✅ **Security**: No hardcoded credentials, proper input validation
- ✅ **Performance**: Optimized for high-volume data ingestion
- ✅ **Packaging**: Proper app structure and metadata
- ✅ **Documentation**: Comprehensive documentation and examples
- ✅ **Testing**: Automated test suite with >90% coverage

### CIM Compliance

Full Common Information Model compliance for:

- **Authentication**: User authentication events
- **Change Analysis**: Policy and configuration changes
- **Application State**: Application health and performance
- **Alerts**: Security alerts and violations
- **Performance**: System performance metrics

### Security Best Practices

- **Encryption**: TLS 1.2+ for all communications
- **Authentication**: Secure credential management
- **Validation**: Comprehensive input validation
- **Logging**: Detailed audit trails
- **Access Control**: Role-based access control

## 🔧 Troubleshooting

### Common Issues

#### 1. No Data Appearing in Splunk

**Symptoms**: No events in `index=opa_audit`

**Solutions**:
```bash
# Check HTTP listener status
index=_internal source=*opa_decision_logs* | head 10

# Verify OPA configuration
curl -X POST http://localhost:8088/test

# Check network connectivity
telnet splunk-server 8088
```

#### 2. High Memory Usage

**Symptoms**: Splunk consuming excessive memory

**Solutions**:
```ini
# Reduce batch size in inputs.conf
max_events_per_batch = 100

# Increase processing interval
interval = 120

# Enable compression
compression = gzip
```

#### 3. Slow Search Performance

**Symptoms**: Dashboard queries taking >10 seconds

**Solutions**:
```bash
# Accelerate data models
| datamodel Authentication_OPA search | head 1000

# Optimize time ranges
earliest=-1h latest=now

# Use summary indexing
| collect index=summary_opa
```

### Log Locations

- **Add-on Logs**: `$SPLUNK_HOME/var/log/splunk/opa_policy_audit.log`
- **HTTP Listener**: `$SPLUNK_HOME/var/log/splunk/splunkd.log`
- **Modular Inputs**: `$SPLUNK_HOME/var/log/splunk/splunkd.log`

### Debug Mode

```ini
# Enable debug logging in inputs.conf
log_level = DEBUG

# Restart Splunk
$SPLUNK_HOME/bin/splunk restart

# Monitor logs
tail -f $SPLUNK_HOME/var/log/splunk/opa_policy_audit.log
```

## 🤝 Contributing

### Development Setup

```bash
# Clone repository
git clone https://github.com/your-org/opa-splunk-addon.git
cd opa-splunk-addon

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Run tests
python -m pytest tests/ -v
```

### Code Standards

- **Python**: PEP 8 compliance
- **Documentation**: Comprehensive docstrings
- **Testing**: >90% test coverage
- **Security**: No hardcoded secrets
- **Performance**: Optimized for high-volume data

### Pull Request Process

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.

## 📞 Support

- **Documentation**: [Full Documentation](docs/)
- **Issues**: [GitHub Issues](https://github.com/your-org/opa-splunk-addon/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/opa-splunk-addon/discussions)
- **Email**: <EMAIL>

## 🙏 Acknowledgments

- Open Policy Agent community
- Styra team for DAS API support
- Splunk developer community
- Contributors and beta testers

---

**Built with ❤️ for the OPA and Splunk communities**