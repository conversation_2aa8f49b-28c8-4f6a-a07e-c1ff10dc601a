# Production Deployment Guide - OPA Policy Audit & Compliance Add-on

## 🚀 Production Readiness Overview

This guide provides comprehensive instructions for deploying the OPA Policy Audit & Compliance Add-on in production environments, ensuring optimal performance, security, and reliability.

## 📋 Pre-Deployment Checklist

### Environment Requirements

- [ ] **Splunk Enterprise 8.0+** or **Splunk Cloud** available
- [ ] **Python 3.7+** installed on Splunk instances
- [ ] **Network connectivity** between Splunk and OPA instances
- [ ] **SSL certificates** available for secure communications
- [ ] **Sufficient storage** for expected data volume (see capacity planning)
- [ ] **Backup and recovery** procedures in place

### Security Requirements

- [ ] **TLS 1.2+** enabled for all communications
- [ ] **API credentials** securely stored in Splunk credential store
- [ ] **Network segmentation** configured (firewall rules)
- [ ] **Access controls** defined (RBAC)
- [ ] **Audit logging** enabled for all administrative actions
- [ ] **Vulnerability scanning** completed

### Performance Requirements

- [ ] **Capacity planning** completed based on expected load
- [ ] **Index sizing** calculated for data retention requirements
- [ ] **Hardware resources** allocated (CPU, memory, storage)
- [ ] **Network bandwidth** sufficient for data volume
- [ ] **Load balancing** configured for high availability

## 🏗️ Deployment Architecture

### Single Instance Deployment

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   OPA Cluster   │───▶│ Splunk Instance │───▶│   End Users     │
│                 │    │                 │    │                 │
│ • Decision Logs │    │ • Add-on        │    │ • Dashboards    │
│ • Health Data   │    │ • Indexing      │    │ • Searches      │
│ • Metrics       │    │ • Processing    │    │ • Alerts        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

**Use Case**: Small to medium deployments (<1000 events/second)

**Configuration**:
```ini
# Single instance configuration
[opa_decision_logs://production]
http_port = 8088
ssl_enabled = 1
max_connections = 200
thread_pool_size = 20
request_timeout = 30
```

### Distributed Deployment

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   OPA Cluster   │───▶│ Heavy Forwarders│───▶│ Indexer Cluster │
│                 │    │                 │    │                 │
│ • Decision Logs │    │ • Add-on        │    │ • Data Storage  │
│ • Health Data   │    │ • Processing    │    │ • Replication   │
│ • Metrics       │    │ • Load Balance  │    │ • Search Head   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Styra DAS     │───▶│ Search Heads    │───▶│   End Users     │
│                 │    │                 │    │                 │
│ • Policy Audits │    │ • Dashboards    │    │ • Analysts      │
│ • Change Events │    │ • Correlation   │    │ • Operators     │
│ • Bundle Info   │    │ • Analytics     │    │ • Executives    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

**Use Case**: Large deployments (>1000 events/second)

**Configuration**:
```ini
# Distributed deployment configuration
[opa_decision_logs://heavy_forwarder]
http_port = 8088
ssl_enabled = 1
max_connections = 500
thread_pool_size = 50
batch_size = 1000
compression = gzip
load_balancer = round_robin
```

### Cloud Deployment

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   OPA (Cloud)   │───▶│ Splunk Cloud    │───▶│ Cloud Users     │
│                 │    │                 │    │                 │
│ • Auto-scaling  │    │ • Managed       │    │ • Web Interface │
│ • Multi-region  │    │ • Auto-updates  │    │ • Mobile Apps   │
│ • HA/DR         │    │ • Compliance    │    │ • APIs          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

**Use Case**: Cloud-native deployments with managed services

## 📦 Installation Procedures

### Method 1: Production Installation via Deployment Server

```bash
#!/bin/bash
# production_install.sh

set -e

SPLUNK_HOME="/opt/splunk"
APP_NAME="opa_policy_audit"
DEPLOYMENT_SERVER="deployment-server.company.com"

echo "Starting production installation..."

# 1. Download and verify add-on package
wget https://github.com/company/opa-splunk-addon/releases/latest/download/opa_policy_audit.tar.gz
sha256sum -c opa_policy_audit.tar.gz.sha256

# 2. Extract to deployment server
sudo tar -xzf opa_policy_audit.tar.gz -C $SPLUNK_HOME/etc/deployment-apps/

# 3. Set proper permissions
sudo chown -R splunk:splunk $SPLUNK_HOME/etc/deployment-apps/$APP_NAME
sudo chmod -R 755 $SPLUNK_HOME/etc/deployment-apps/$APP_NAME

# 4. Validate configuration
sudo -u splunk $SPLUNK_HOME/bin/splunk btool inputs list --app=$APP_NAME

# 5. Deploy to forwarders
sudo -u splunk $SPLUNK_HOME/bin/splunk reload deploy-server

echo "Production installation completed successfully"
```

### Method 2: Direct Installation with Configuration Management

```yaml
# ansible-playbook.yml
---
- name: Deploy OPA Policy Audit Add-on
  hosts: splunk_servers
  become: yes
  vars:
    splunk_home: /opt/splunk
    app_name: opa_policy_audit
    
  tasks:
    - name: Download add-on package
      get_url:
        url: "https://github.com/company/opa-splunk-addon/releases/latest/download/opa_policy_audit.tar.gz"
        dest: "/tmp/opa_policy_audit.tar.gz"
        mode: '0644'
        
    - name: Extract add-on
      unarchive:
        src: "/tmp/opa_policy_audit.tar.gz"
        dest: "{{ splunk_home }}/etc/apps/"
        owner: splunk
        group: splunk
        remote_src: yes
        
    - name: Configure inputs
      template:
        src: inputs.conf.j2
        dest: "{{ splunk_home }}/etc/apps/{{ app_name }}/local/inputs.conf"
        owner: splunk
        group: splunk
        mode: '0644'
      notify: restart splunk
      
    - name: Validate configuration
      command: "{{ splunk_home }}/bin/splunk btool inputs list --app={{ app_name }}"
      become_user: splunk
      register: config_validation
      
    - name: Display validation results
      debug:
        var: config_validation.stdout
        
  handlers:
    - name: restart splunk
      systemd:
        name: splunk
        state: restarted
```

## ⚙️ Production Configuration

### Environment-Specific Configurations

#### Production Environment

```ini
# inputs.conf - Production
[opa_decision_logs://production]
interval = 60
index = opa_audit
sourcetype = opa:decision
http_port = 8088
ssl_enabled = 1
ssl_cert_path = /opt/splunk/etc/certs/production/server.crt
ssl_key_path = /opt/splunk/etc/certs/production/server.key
ssl_ca_path = /opt/splunk/etc/certs/production/ca.crt
max_connections = 500
thread_pool_size = 50
request_timeout = 30
log_level = INFO
health_check_enabled = 1
metrics_collection_enabled = 1
rate_limit_enabled = 1
rate_limit_requests_per_minute = 10000
disabled = 0

[styra_das_audit://production]
interval = 300
index = opa_audit
sourcetype = styra:das:policy:audit
api_endpoint = https://company.styra.com
api_token_name = styra_das_token
system_id = production-system
max_events_per_request = 1000
request_timeout = 60
retry_attempts = 3
retry_delay = 5
ssl_verify = 1
disabled = 0

[opa_health_monitor://production]
interval = 60
index = opa_audit
sourcetype = opa:health
opa_endpoints = https://opa-prod-01.company.com:8181,https://opa-prod-02.company.com:8181,https://opa-prod-03.company.com:8181
health_check_timeout = 10
metrics_enabled = 1
ssl_verify = 1
failover_enabled = 1
disabled = 0
```

#### Staging Environment

```ini
# inputs.conf - Staging
[opa_decision_logs://staging]
interval = 60
index = opa_audit_staging
sourcetype = opa:decision
http_port = 8089
ssl_enabled = 1
ssl_cert_path = /opt/splunk/etc/certs/staging/server.crt
ssl_key_path = /opt/splunk/etc/certs/staging/server.key
max_connections = 100
thread_pool_size = 20
log_level = DEBUG
disabled = 0
```

### Index Configuration

```ini
# indexes.conf - Production
[opa_audit]
homePath = $SPLUNK_DB/opa_audit/db
coldPath = $SPLUNK_DB/opa_audit/colddb
thawedPath = $SPLUNK_DB/opa_audit/thaweddb

# Performance optimization
maxDataSize = auto_high_volume
maxHotBuckets = 20
maxWarmDBCount = 500

# Retention policy (2 years for compliance)
frozenTimePeriodInSecs = 63072000

# Storage optimization
compressRawdata = true
maxMemMB = 20
maxConcurrentOptimizes = 6

# Replication for HA
repFactor = auto

# Search optimization
maxBloomBackfillBucketAge = 30d
bloomfilterTotalSizeKB = 128
```

### Security Configuration

```ini
# authentication.conf
[authentication]
authType = LDAP
authSettings = ldap_settings

[ldap_settings]
host = ldap.company.com
port = 636
SSLEnabled = 1
bindDN = cn=splunk,ou=service,dc=company,dc=com
bindDNpassword = $encrypted_password$
userBaseDN = ou=users,dc=company,dc=com
groupBaseDN = ou=groups,dc=company,dc=com

# authorize.conf
[role_opa_admin]
importRoles = user
srchIndexesAllowed = opa_audit
srchIndexesDefault = opa_audit
capabilities = edit_inputs, edit_props, restart_splunkd

[role_opa_analyst]
importRoles = user
srchIndexesAllowed = opa_audit
srchIndexesDefault = opa_audit
capabilities = search
```

## 📊 Capacity Planning

### Data Volume Estimation

```python
# capacity_calculator.py

def calculate_daily_volume(events_per_second, avg_event_size_kb):
    """
    Calculate daily data volume for capacity planning
    """
    seconds_per_day = 86400
    daily_events = events_per_second * seconds_per_day
    daily_volume_gb = (daily_events * avg_event_size_kb) / (1024 * 1024)
    
    return {
        'daily_events': daily_events,
        'daily_volume_gb': daily_volume_gb,
        'monthly_volume_gb': daily_volume_gb * 30,
        'yearly_volume_gb': daily_volume_gb * 365
    }

# Example calculations
scenarios = {
    'small': {'eps': 100, 'size_kb': 2},
    'medium': {'eps': 1000, 'size_kb': 2},
    'large': {'eps': 5000, 'size_kb': 2},
    'enterprise': {'eps': 10000, 'size_kb': 2}
}

for scenario, params in scenarios.items():
    result = calculate_daily_volume(params['eps'], params['size_kb'])
    print(f"{scenario.upper()} DEPLOYMENT:")
    print(f"  Daily Events: {result['daily_events']:,}")
    print(f"  Daily Volume: {result['daily_volume_gb']:.2f} GB")
    print(f"  Monthly Volume: {result['monthly_volume_gb']:.2f} GB")
    print(f"  Yearly Volume: {result['yearly_volume_gb']:.2f} GB")
    print()
```

**Output**:
```
SMALL DEPLOYMENT:
  Daily Events: 8,640,000
  Daily Volume: 16.48 GB
  Monthly Volume: 494.38 GB
  Yearly Volume: 6,014.06 GB

MEDIUM DEPLOYMENT:
  Daily Events: 86,400,000
  Daily Volume: 164.79 GB
  Monthly Volume: 4,943.75 GB
  Yearly Volume: 60,140.63 GB

LARGE DEPLOYMENT:
  Daily Events: 432,000,000
  Daily Volume: 823.97 GB
  Monthly Volume: 24,718.75 GB
  Yearly Volume: 300,703.13 GB

ENTERPRISE DEPLOYMENT:
  Daily Events: 864,000,000
  Daily Volume: 1,647.95 GB
  Monthly Volume: 49,437.50 GB
  Yearly Volume: 601,406.25 GB
```

### Hardware Requirements

| Deployment Size | Events/Second | Daily Volume | CPU Cores | Memory | Storage (1 Year) |
|----------------|---------------|--------------|-----------|--------|------------------|
| Small | 100 | 16 GB | 4 | 16 GB | 6 TB |
| Medium | 1,000 | 165 GB | 8 | 32 GB | 60 TB |
| Large | 5,000 | 824 GB | 16 | 64 GB | 300 TB |
| Enterprise | 10,000 | 1,648 GB | 32 | 128 GB | 600 TB |

### Network Requirements

```python
# network_calculator.py

def calculate_network_bandwidth(events_per_second, avg_event_size_kb, overhead_factor=1.2):
    """
    Calculate required network bandwidth with overhead
    """
    bits_per_byte = 8
    kb_to_bits = 1024 * bits_per_byte
    
    raw_bandwidth_bps = events_per_second * avg_event_size_kb * kb_to_bits
    required_bandwidth_bps = raw_bandwidth_bps * overhead_factor
    required_bandwidth_mbps = required_bandwidth_bps / (1024 * 1024)
    
    return {
        'raw_bandwidth_mbps': raw_bandwidth_bps / (1024 * 1024),
        'required_bandwidth_mbps': required_bandwidth_mbps,
        'recommended_bandwidth_mbps': required_bandwidth_mbps * 2  # 100% headroom
    }

# Calculate for different scenarios
for scenario, params in scenarios.items():
    result = calculate_network_bandwidth(params['eps'], params['size_kb'])
    print(f"{scenario.upper()} DEPLOYMENT:")
    print(f"  Raw Bandwidth: {result['raw_bandwidth_mbps']:.2f} Mbps")
    print(f"  Required Bandwidth: {result['required_bandwidth_mbps']:.2f} Mbps")
    print(f"  Recommended Bandwidth: {result['recommended_bandwidth_mbps']:.2f} Mbps")
    print()
```

## 🔒 Security Hardening

### SSL/TLS Configuration

```bash
#!/bin/bash
# ssl_setup.sh

CERT_DIR="/opt/splunk/etc/certs/production"
KEY_SIZE=4096
VALIDITY_DAYS=365

# Create certificate directory
sudo mkdir -p $CERT_DIR
sudo chown splunk:splunk $CERT_DIR
sudo chmod 700 $CERT_DIR

# Generate private key
sudo -u splunk openssl genrsa -out $CERT_DIR/server.key $KEY_SIZE

# Generate certificate signing request
sudo -u splunk openssl req -new -key $CERT_DIR/server.key -out $CERT_DIR/server.csr -config <(
cat <<EOF
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = US
ST = State
L = City
O = Company
OU = IT Department
CN = splunk.company.com

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = splunk.company.com
DNS.2 = *.splunk.company.com
IP.1 = **********
EOF
)

# Generate self-signed certificate (replace with CA-signed in production)
sudo -u splunk openssl x509 -req -in $CERT_DIR/server.csr -signkey $CERT_DIR/server.key -out $CERT_DIR/server.crt -days $VALIDITY_DAYS -extensions v3_req -extfile <(
cat <<EOF
[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = splunk.company.com
DNS.2 = *.splunk.company.com
IP.1 = **********
EOF
)

# Set proper permissions
sudo chmod 600 $CERT_DIR/server.key
sudo chmod 644 $CERT_DIR/server.crt

echo "SSL certificates generated successfully"
```

### Firewall Configuration

```bash
#!/bin/bash
# firewall_setup.sh

# Allow Splunk management port
sudo ufw allow 8089/tcp comment "Splunk Management"

# Allow OPA decision log ingestion
sudo ufw allow 8088/tcp comment "OPA Decision Logs"

# Allow Splunk web interface
sudo ufw allow 8000/tcp comment "Splunk Web"

# Allow Splunk forwarder communication
sudo ufw allow 9997/tcp comment "Splunk Forwarder"

# Deny all other traffic by default
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Enable firewall
sudo ufw --force enable

echo "Firewall configured successfully"
```

### Credential Management

```python
# credential_setup.py
import splunklib.client as client
import getpass

def setup_credentials():
    """
    Setup secure credential storage for production
    """
    # Connect to Splunk
    service = client.connect(
        host='localhost',
        port=8089,
        username='admin',
        password=getpass.getpass('Splunk admin password: ')
    )
    
    # Create credential storage entries
    credentials = {
        'styra_das_token': getpass.getpass('Styra DAS API token: '),
        'opa_health_auth': getpass.getpass('OPA health check auth: '),
        'webhook_secret': getpass.getpass('Webhook secret: ')
    }
    
    for name, password in credentials.items():
        try:
            service.storage_passwords.create(password, name, realm='opa_policy_audit')
            print(f"✅ Credential '{name}' stored successfully")
        except Exception as e:
            print(f"❌ Failed to store credential '{name}': {e}")
    
    print("\n🔒 All credentials stored securely in Splunk")

if __name__ == '__main__':
    setup_credentials()
```

## 📈 Monitoring and Alerting

### Health Monitoring

```ini
# savedsearches.conf
[OPA Add-on Health Check]
search = index=_internal source=*opa_policy_audit* | stats count by component, status | where status!="healthy"
dispatch.earliest_time = -5m
dispatch.latest_time = now
cron_schedule = */5 * * * *
action.email = 1
action.email.to = <EMAIL>
action.email.subject = OPA Add-on Health Alert
action.email.message.alert = The OPA Policy Audit add-on has detected unhealthy components
is_scheduled = 1

[High Volume Alert]
search = index=opa_audit | bucket _time span=1m | stats count by _time | where count > 2000
dispatch.earliest_time = -5m
dispatch.latest_time = now
cron_schedule = */5 * * * *
action.webhook = 1
action.webhook.param.url = https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
is_scheduled = 1

[Policy Violation Spike]
search = index=opa_audit sourcetype=opa:decision result=false | bucket _time span=5m | stats count by _time | where count > 100
dispatch.earliest_time = -10m
dispatch.latest_time = now
cron_schedule = */5 * * * *
action.script = 1
action.script.filename = policy_violation_alert.py
is_scheduled = 1
```

### Performance Monitoring

```python
# performance_monitor.py
import splunklib.client as client
import time
import json

class PerformanceMonitor:
    def __init__(self, splunk_service):
        self.service = splunk_service
        self.metrics = {}
    
    def collect_metrics(self):
        """Collect performance metrics"""
        searches = {
            'ingestion_rate': 'index=opa_audit | bucket _time span=1m | stats count by _time | eval rate=count/60 | stats avg(rate) as avg_eps',
            'processing_latency': 'index=_internal source=*opa_policy_audit* "processing_time" | stats avg(processing_time) as avg_latency_ms',
            'error_rate': 'index=_internal source=*opa_policy_audit* level=ERROR | bucket _time span=5m | stats count by _time | eval error_rate=count/300',
            'memory_usage': 'index=_internal source=*resource_usage* component=opa_policy_audit | stats avg(mem_used) as avg_memory_mb'
        }
        
        for metric_name, search_query in searches.items():
            try:
                job = self.service.jobs.create(search_query, earliest_time='-5m', latest_time='now')
                
                # Wait for job completion
                while not job.is_done():
                    time.sleep(0.1)
                
                # Get results
                results = job.results()
                for result in results:
                    self.metrics[metric_name] = float(list(result.values())[0])
                    
            except Exception as e:
                print(f"Error collecting metric {metric_name}: {e}")
    
    def check_thresholds(self):
        """Check if metrics exceed thresholds"""
        thresholds = {
            'ingestion_rate': {'min': 50, 'max': 2000},
            'processing_latency': {'max': 1000},
            'error_rate': {'max': 0.01},
            'memory_usage': {'max': 512}
        }
        
        alerts = []
        
        for metric, value in self.metrics.items():
            threshold = thresholds.get(metric, {})
            
            if 'min' in threshold and value < threshold['min']:
                alerts.append(f"{metric} below minimum threshold: {value} < {threshold['min']}")
            
            if 'max' in threshold and value > threshold['max']:
                alerts.append(f"{metric} above maximum threshold: {value} > {threshold['max']}")
        
        return alerts
    
    def generate_report(self):
        """Generate performance report"""
        self.collect_metrics()
        alerts = self.check_thresholds()
        
        report = {
            'timestamp': time.time(),
            'metrics': self.metrics,
            'alerts': alerts,
            'status': 'healthy' if not alerts else 'warning'
        }
        
        return json.dumps(report, indent=2)

# Usage
if __name__ == '__main__':
    service = client.connect(host='localhost', port=8089, username='admin', password='password')
    monitor = PerformanceMonitor(service)
    print(monitor.generate_report())
```

## 🔄 Backup and Recovery

### Backup Procedures

```bash
#!/bin/bash
# backup_opa_addon.sh

SPLUNK_HOME="/opt/splunk"
APP_NAME="opa_policy_audit"
BACKUP_DIR="/backup/splunk"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR/$DATE

# Backup app configuration
echo "Backing up app configuration..."
tar -czf $BACKUP_DIR/$DATE/app_config.tar.gz -C $SPLUNK_HOME/etc/apps $APP_NAME

# Backup indexes
echo "Backing up indexes..."
$SPLUNK_HOME/bin/splunk offline
tar -czf $BACKUP_DIR/$DATE/opa_audit_index.tar.gz -C $SPLUNK_DB opa_audit
$SPLUNK_HOME/bin/splunk start

# Backup credentials
echo "Backing up credentials..."
$SPLUNK_HOME/bin/splunk export credentials -auth admin:password > $BACKUP_DIR/$DATE/credentials.xml

# Create backup manifest
echo "Creating backup manifest..."
cat > $BACKUP_DIR/$DATE/manifest.txt <<EOF
Backup Date: $(date)
Splunk Version: $($SPLUNK_HOME/bin/splunk version --accept-license)
App Version: $(grep version $SPLUNK_HOME/etc/apps/$APP_NAME/default/app.conf | cut -d= -f2 | tr -d ' ')
Files:
- app_config.tar.gz: Application configuration and scripts
- opa_audit_index.tar.gz: Index data
- credentials.xml: Stored credentials
EOF

echo "Backup completed: $BACKUP_DIR/$DATE"
```

### Recovery Procedures

```bash
#!/bin/bash
# restore_opa_addon.sh

BACKUP_DATE=$1
SPLUNK_HOME="/opt/splunk"
APP_NAME="opa_policy_audit"
BACKUP_DIR="/backup/splunk"

if [ -z "$BACKUP_DATE" ]; then
    echo "Usage: $0 <backup_date>"
    echo "Available backups:"
    ls -1 $BACKUP_DIR/
    exit 1
fi

if [ ! -d "$BACKUP_DIR/$BACKUP_DATE" ]; then
    echo "Backup directory not found: $BACKUP_DIR/$BACKUP_DATE"
    exit 1
fi

echo "Restoring from backup: $BACKUP_DATE"

# Stop Splunk
echo "Stopping Splunk..."
$SPLUNK_HOME/bin/splunk stop

# Restore app configuration
echo "Restoring app configuration..."
rm -rf $SPLUNK_HOME/etc/apps/$APP_NAME
tar -xzf $BACKUP_DIR/$BACKUP_DATE/app_config.tar.gz -C $SPLUNK_HOME/etc/apps/

# Restore index data
echo "Restoring index data..."
rm -rf $SPLUNK_DB/opa_audit
tar -xzf $BACKUP_DIR/$BACKUP_DATE/opa_audit_index.tar.gz -C $SPLUNK_DB/

# Restore credentials
echo "Restoring credentials..."
$SPLUNK_HOME/bin/splunk import credentials $BACKUP_DIR/$BACKUP_DATE/credentials.xml -auth admin:password

# Set proper permissions
chown -R splunk:splunk $SPLUNK_HOME/etc/apps/$APP_NAME
chown -R splunk:splunk $SPLUNK_DB/opa_audit

# Start Splunk
echo "Starting Splunk..."
$SPLUNK_HOME/bin/splunk start

echo "Recovery completed successfully"
```

## 🚀 Go-Live Checklist

### Pre-Go-Live Validation

- [ ] **Installation verified** on all target systems
- [ ] **Configuration validated** using btool
- [ ] **SSL certificates** installed and verified
- [ ] **Network connectivity** tested from all OPA instances
- [ ] **Credentials** stored securely in Splunk
- [ ] **Index creation** completed with proper sizing
- [ ] **Data models** accelerated and tested
- [ ] **Dashboards** loading within performance targets
- [ ] **Alerts** configured and tested
- [ ] **Backup procedures** tested and documented
- [ ] **Monitoring** configured for add-on health
- [ ] **Documentation** updated with production details
- [ ] **Team training** completed
- [ ] **Support procedures** established

### Go-Live Execution

```bash
#!/bin/bash
# go_live.sh

echo "=== OPA Policy Audit Add-on Go-Live ==="
echo "Date: $(date)"
echo "Environment: Production"
echo

# 1. Final configuration validation
echo "1. Validating configuration..."
/opt/splunk/bin/splunk btool inputs list --app=opa_policy_audit
if [ $? -eq 0 ]; then
    echo "✅ Configuration validation passed"
else
    echo "❌ Configuration validation failed"
    exit 1
fi

# 2. Enable inputs
echo "2. Enabling data inputs..."
/opt/splunk/bin/splunk enable input opa_decision_logs://production
/opt/splunk/bin/splunk enable input styra_das_audit://production
/opt/splunk/bin/splunk enable input opa_health_monitor://production

# 3. Start monitoring
echo "3. Starting monitoring..."
/opt/splunk/bin/splunk enable saved-search "OPA Add-on Health Check"
/opt/splunk/bin/splunk enable saved-search "High Volume Alert"
/opt/splunk/bin/splunk enable saved-search "Policy Violation Spike"

# 4. Verify data ingestion
echo "4. Verifying data ingestion..."
sleep 60
DATA_COUNT=$(/opt/splunk/bin/splunk search 'index=opa_audit earliest=-5m' -auth admin:password | grep -c "events")
if [ "$DATA_COUNT" -gt 0 ]; then
    echo "✅ Data ingestion verified"
else
    echo "⚠️  No data detected yet (may take a few minutes)"
fi

# 5. Final status check
echo "5. Final status check..."
/opt/splunk/bin/splunk status

echo
echo "=== Go-Live Completed ==="
echo "Monitor the system for the next 24 hours"
echo "Check dashboards: https://splunk.company.com:8000/en-US/app/opa_policy_audit"
echo "Support contact: <EMAIL>"
```

### Post-Go-Live Monitoring

**First 24 Hours**:
- [ ] Monitor data ingestion rates every hour
- [ ] Check for any error messages in logs
- [ ] Verify dashboard performance
- [ ] Confirm alert functionality
- [ ] Monitor system resource usage

**First Week**:
- [ ] Review performance metrics daily
- [ ] Analyze data quality and completeness
- [ ] Gather user feedback
- [ ] Fine-tune alert thresholds
- [ ] Document any issues and resolutions

**First Month**:
- [ ] Conduct capacity planning review
- [ ] Optimize search performance if needed
- [ ] Review and update documentation
- [ ] Plan for any necessary enhancements
- [ ] Conduct lessons learned session

---

**Production Deployment Status**: ✅ Ready for Go-Live  
**Security Validation**: ✅ Completed  
**Performance Testing**: ✅ Passed  
**Documentation**: ✅ Complete