# OPA Policy Audit & Compliance Add-on for Splunk

[![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)](https://github.com/your-org/opa-splunk-addon)
[![License](https://img.shields.io/badge/license-Apache%202.0-green.svg)](LICENSE)
[![Splunk Certified](https://img.shields.io/badge/Splunk-Certified-orange.svg)](https://splunkbase.splunk.com)
[![CIM Compliant](https://img.shields.io/badge/CIM-Compliant-brightgreen.svg)](https://docs.splunk.com/Documentation/CIM)

## Overview

The **OPA Policy Audit & Compliance Add-on** is a comprehensive Splunk solution designed to monitor, audit, and analyze Open Policy Agent (OPA) policy decisions and compliance across your infrastructure. This add-on provides real-time visibility into policy enforcement, security violations, and compliance posture through advanced dashboards, alerts, and CIM-compliant data models.

### Key Features

🔒 **Security Monitoring**
- Real-time policy violation detection
- Anomalous access pattern identification
- Privilege escalation monitoring
- Geographic risk analysis
- Threat actor profiling

📊 **Compliance Management**
- SOX, PCI DSS, HIPAA compliance tracking
- Automated compliance reporting
- Policy framework mapping
- Audit trail generation
- Risk score calculation

⚡ **Performance Analytics**
- OPA instance health monitoring
- Policy evaluation performance metrics
- Bundle status tracking
- Resource utilization analysis
- Capacity planning insights

🎯 **Advanced Analytics**
- Machine learning-based anomaly detection
- User behavior analytics (UBA)
- Time-series analysis
- Predictive compliance scoring
- Custom risk modeling

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   OPA Instances │    │   Styra DAS     │    │  External APIs  │
│                 │    │                 │    │                 │
│ • Decision Logs │    │ • Policy Audit  │    │ • GeoIP         │
│ • Health Status │    │ • Change Events │    │ • Threat Intel  │
│ • Metrics       │    │ • Bundle Info   │    │ • User Directory│
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          │                      │                      │
          ▼                      ▼                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Splunk Add-on                               │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │
│  │ Modular     │  │ Data Models │  │ Dashboards  │            │
│  │ Inputs      │  │ & CIM       │  │ & Alerts    │            │
│  │             │  │ Compliance  │  │             │            │
│  └─────────────┘  └─────────────┘  └─────────────┘            │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │
│  │ Lookups &   │  │ Saved       │  │ Workflow    │            │
│  │ Transforms  │  │ Searches    │  │ Actions     │            │
│  │             │  │ & Reports   │  │             │            │
│  └─────────────┘  └─────────────┘  └─────────────┘            │
└─────────────────────────────────────────────────────────────────┘
          │
          ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Splunk Core                               │
│                                                                 │
│  • Search & Analytics    • Machine Learning    • Reporting     │
│  • Alerting & Actions    • Data Visualization  • Integration   │
└─────────────────────────────────────────────────────────────────┘
```

## Installation

### Prerequisites

- Splunk Enterprise 8.0+ or Splunk Cloud
- Python 3.7+ (for modular inputs)
- Network connectivity to OPA instances
- Administrative access to Splunk

### Quick Install

1. **Download the Add-on**
   ```bash
   # From Splunkbase (recommended)
   # Or clone from repository
   git clone https://github.com/your-org/opa-splunk-addon.git
   ```

2. **Install Dependencies**
   ```bash
   cd opa-splunk-addon
   pip install -r requirements.txt
   ```

3. **Deploy to Splunk**
   ```bash
   # Copy to Splunk apps directory
   cp -r . $SPLUNK_HOME/etc/apps/opa_policy_audit_addon/
   
   # Restart Splunk
   $SPLUNK_HOME/bin/splunk restart
   ```

4. **Configure Data Inputs**
   - Navigate to **Settings > Data Inputs**
   - Configure OPA Decision Logs input
   - Set up Styra DAS integration (optional)
   - Configure health monitoring

### Advanced Installation

#### Using Splunk Deployment Server

1. **Prepare the App Package**
   ```bash
   tar -czf opa_policy_audit_addon.tar.gz opa_policy_audit_addon/
   ```

2. **Deploy via Deployment Server**
   ```bash
   # Copy to deployment apps
   cp opa_policy_audit_addon.tar.gz $SPLUNK_HOME/etc/deployment-apps/
   
   # Configure serverclass.conf
   # Reload deployment server
   $SPLUNK_HOME/bin/splunk reload deploy-server
   ```

#### Using Ansible

```yaml
- name: Deploy OPA Splunk Add-on
  splunk_app:
    name: opa_policy_audit_addon
    state: present
    source: /path/to/opa_policy_audit_addon.tar.gz
  notify: restart splunk
```

## Configuration

### Data Input Configuration

#### 1. OPA Decision Logs

```ini
[opa_decision_logs://default]
disabled = false
port = 8088
path = /opadecisions
ssl_enabled = false
max_content_length = 10485760
max_connections = 100
buffer_size = 8192
timeout = 30
health_check_enabled = true
health_check_interval = 60
log_level = INFO
```

#### 2. Styra DAS Integration

```ini
[styra_das_audit://default]
disabled = true
api_endpoint = https://your-tenant.styra.com/v1/audit
api_token = your_api_token_here
polling_interval = 300
max_retries = 3
timeout = 30
log_level = INFO
```

#### 3. OPA Health Monitoring

```ini
[opa_health_monitor://default]
disabled = false
opa_endpoints = http://localhost:8181
check_interval = 60
timeout = 10
check_bundles = true
check_plugins = true
check_metrics = true
health_threshold = 0.95
log_level = INFO
```

### Lookup Table Configuration

Update the lookup tables in the `lookups/` directory:

1. **policy_criticality.csv** - Map policies to criticality levels
2. **user_info.csv** - User context and risk scoring
3. **opa_instances.csv** - OPA instance metadata

### Index Configuration

Recommended index configuration:

```ini
# indexes.conf
[opa_audit]
homePath = $SPLUNK_DB/opa_audit/db
coldPath = $SPLUNK_DB/opa_audit/colddb
thawedPath = $SPLUNK_DB/opa_audit/thaweddb
maxDataSize = auto_high_volume
maxHotBuckets = 10
maxWarmDBCount = 300
maxTotalDataSizeMB = 500000
```

## Usage

### Dashboards

#### 1. OPA Policy Audit Overview
- **Path**: `Settings > User Interface > Views`
- **Features**: Policy decision trends, compliance status, risk analysis
- **Use Case**: Executive reporting and operational monitoring

#### 2. Security Dashboard
- **Path**: `Security > Dashboards > OPA Security`
- **Features**: Threat detection, anomaly analysis, incident response
- **Use Case**: SOC operations and security investigations

### Saved Searches and Alerts

#### Critical Policy Violations
```spl
| `opa_base_filter` 
| `opa_high_risk_filter` 
| eval risk_score=if(decision="deny" AND policy_criticality="critical", 100, 50)
| where risk_score > 80
| stats count by policy_path, input_user, risk_score
| sort -risk_score
```

#### Privilege Escalation Detection
```spl
| `opa_base_filter`
| `opa_privilege_escalation_filter`
| eval escalation_score=case(
    match(policy_path, "admin|root|sudo"), 90,
    match(input_resource, "sensitive|confidential"), 70,
    1=1, 30
)
| where escalation_score > 60
| stats count, values(policy_path) as policies by input_user
```

#### Compliance Reporting
```spl
| `opa_base_filter`
| `opa_compliance_filter`
| eval compliance_framework=case(
    match(policy_path, "sox"), "SOX",
    match(policy_path, "pci"), "PCI DSS",
    match(policy_path, "hipaa"), "HIPAA",
    1=1, "General"
)
| stats count(eval(decision="allow")) as allowed,
        count(eval(decision="deny")) as denied,
        count as total
        by compliance_framework
| eval compliance_rate=round((allowed/total)*100, 2)
```

### Workflow Actions

The add-on includes pre-configured workflow actions for:

- **Security Incident Response**: Investigate users, create tickets, escalate threats
- **Compliance Investigation**: Generate audit trails, compliance reports
- **Operational Response**: Check OPA health, restart instances, update bundles
- **Policy Management**: Review policies in Styra DAS, analyze impact

### Custom Macros

Use built-in macros for consistent searching:

```spl
# Base filters
`opa_base_filter`                    # Filter OPA decision logs
`opa_styra_filter`                   # Filter Styra DAS events
`opa_health_filter`                  # Filter health events

# Time-based filters
`opa_last_24h`                       # Last 24 hours
`opa_business_hours`                 # Business hours only
`opa_off_hours`                      # Off-hours activity

# Risk and security filters
`opa_high_risk_filter`               # High-risk events
`opa_security_events`                # Security-related events
`opa_anomalous_behavior`             # Anomalous patterns

# Performance and operational
`opa_performance_issues`             # Performance problems
`opa_error_events`                   # Error conditions
```

## Data Models

The add-on provides CIM-compliant data models:

### Authentication Data Model
- **Dataset**: `OPA_Authentication`
- **Fields**: `action`, `user`, `src`, `dest`, `result`, `signature`
- **Use Case**: Login analysis, authentication failures

### Change Analysis Data Model
- **Dataset**: `OPA_Changes`
- **Fields**: `change_type`, `object`, `user`, `result`, `command`
- **Use Case**: Policy changes, configuration modifications

### Application State Data Model
- **Dataset**: `OPA_Application_State`
- **Fields**: `status`, `version`, `performance_metrics`
- **Use Case**: Application health, performance monitoring

### Risk Data Model
- **Dataset**: `OPA_Risk`
- **Fields**: `risk_score`, `risk_object`, `risk_object_type`
- **Use Case**: Risk assessment, threat scoring

## Troubleshooting

### Common Issues

#### 1. No Data Ingestion

**Symptoms**: No events in `index=opa_audit`

**Solutions**:
```bash
# Check input status
splunk list inputstatus

# Verify OPA connectivity
curl -X POST http://localhost:8088/opadecisions \
  -H "Content-Type: application/json" \
  -d '{"decision_id":"test","result":true}'

# Check Splunk logs
tail -f $SPLUNK_HOME/var/log/splunk/splunkd.log | grep opa
```

#### 2. High Memory Usage

**Symptoms**: Python processes consuming excessive memory

**Solutions**:
```ini
# Reduce buffer sizes in inputs.conf
buffer_size = 4096
max_content_length = 5242880

# Implement data sampling
sampling_rate = 0.1
```

#### 3. SSL/TLS Issues

**Symptoms**: Connection errors with HTTPS endpoints

**Solutions**:
```ini
# Disable SSL verification (development only)
ssl_verify = false

# Specify custom CA bundle
ssl_ca_bundle = /path/to/ca-bundle.crt
```

### Performance Tuning

#### Indexing Optimization

```ini
# props.conf optimizations
[opa:decision:logs]
SHOULD_LINEMERGE = false
TRUNCATE = 0
TIME_PREFIX = "timestamp":

# Parallel processing
MAX_TIMESTAMP_LOOKAHEAD = 25
DATETIME_CONFIG = CURRENT
```

#### Search Optimization

```spl
# Use summary indexing for frequent searches
| `opa_base_filter`
| bucket _time span=1h
| stats count by _time, policy_path, decision
| collect index=summary source="opa_hourly_summary"
```

### Monitoring and Alerting

#### Health Check Search

```spl
| rest /services/data/inputs/opa_decision_logs
| eval status=if(disabled=0 AND eai:acl.can_write=1, "healthy", "unhealthy")
| stats count by status
| where status="unhealthy"
```

#### Performance Monitoring

```spl
| `opa_base_filter`
| eval processing_time=strptime(timestamp, "%Y-%m-%dT%H:%M:%S.%3NZ")-_time
| stats avg(processing_time) as avg_latency,
        max(processing_time) as max_latency,
        count as event_count
        by host
| where avg_latency > 5
```

## API Reference

### Modular Input APIs

#### OPA Decision Logs Endpoint

```http
POST /opadecisions
Content-Type: application/json

{
  "decision_id": "uuid",
  "timestamp": "2024-01-15T10:30:00Z",
  "input": {
    "user": "john.doe",
    "resource": "/api/users",
    "action": "read"
  },
  "result": true,
  "policy_path": "authz.allow",
  "metrics": {
    "timer_rego_query_eval_ns": 1234567
  }
}
```

#### Health Check Endpoint

```http
GET /health

Response:
{
  "status": "healthy",
  "version": "1.0.0",
  "uptime": 3600,
  "inputs": {
    "opa_decision_logs": "active",
    "styra_das_audit": "disabled",
    "opa_health_monitor": "active"
  }
}
```

### REST API Integration

```python
# Example: Custom data ingestion
import requests
import json

def send_to_splunk(event_data):
    url = "http://localhost:8088/opadecisions"
    headers = {"Content-Type": "application/json"}
    
    response = requests.post(url, 
                           data=json.dumps(event_data), 
                           headers=headers)
    
    return response.status_code == 200
```

## Security Considerations

### Authentication and Authorization

1. **API Token Management**
   - Store tokens in Splunk's credential store
   - Rotate tokens regularly
   - Use least-privilege access

2. **Network Security**
   - Enable SSL/TLS for all communications
   - Implement network segmentation
   - Use VPN for remote access

3. **Data Protection**
   - Encrypt sensitive data at rest
   - Implement data masking for PII
   - Regular security audits

### Compliance

- **SOX**: Financial data access controls
- **PCI DSS**: Payment card data protection
- **HIPAA**: Healthcare data privacy
- **GDPR**: Personal data protection
- **SOC 2**: Security and availability controls

## Contributing

### Development Setup

```bash
# Clone repository
git clone https://github.com/your-org/opa-splunk-addon.git
cd opa-splunk-addon

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Run tests
pytest tests/

# Code formatting
black .
flake8 .
```

### Contribution Guidelines

1. **Code Style**: Follow PEP 8 and use Black formatter
2. **Testing**: Write unit tests for all new features
3. **Documentation**: Update README and inline documentation
4. **Security**: Follow secure coding practices
5. **Performance**: Consider performance impact of changes

### Submitting Changes

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests and documentation
5. Submit a pull request

## Support

### Community Support

- **GitHub Issues**: [Report bugs and request features](https://github.com/your-org/opa-splunk-addon/issues)
- **Discussions**: [Community discussions](https://github.com/your-org/opa-splunk-addon/discussions)
- **Slack**: [#opa-splunk-addon](https://slack.openpolicyagent.org)

### Commercial Support

- **Professional Services**: Implementation and customization
- **Training**: Splunk and OPA best practices
- **24/7 Support**: Enterprise support packages

### Documentation

- **User Guide**: [Complete user documentation](https://docs.example.com/opa-splunk-addon)
- **API Reference**: [REST API documentation](https://api.example.com/opa-splunk-addon)
- **Video Tutorials**: [YouTube playlist](https://youtube.com/playlist?list=example)

## Changelog

### Version 1.0.0 (2024-01-15)

**New Features**:
- Initial release with OPA decision log ingestion
- Styra DAS integration for policy audit
- CIM-compliant data models
- Security and compliance dashboards
- Real-time alerting and workflow actions
- Performance monitoring and health checks

**Security Enhancements**:
- SSL/TLS support for all communications
- Token-based authentication
- Data encryption and masking

**Performance Improvements**:
- Optimized data parsing and indexing
- Configurable buffer sizes and timeouts
- Memory usage optimization

## License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- **Open Policy Agent Community**: For the amazing OPA project
- **Styra**: For Styra DAS integration support
- **Splunk Community**: For CIM compliance guidance
- **Contributors**: All the developers who made this possible

---

**Made with ❤️ by the OPA Community**

For questions, suggestions, or contributions, please reach out through our [GitHub repository](https://github.com/your-org/opa-splunk-addon) or [community channels](https://www.openpolicyagent.org/community/).