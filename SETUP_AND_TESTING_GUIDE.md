# Setup and Testing Guide - OPA Policy Audit & Compliance Add-on

## Quick Start for Judges

This guide provides step-by-step instructions for setting up and testing the OPA Policy Audit & Compliance Add-on in a Splunk environment.

## 📋 Prerequisites

### System Requirements
- **Splunk Enterprise**: 8.0+ or Splunk Cloud
- **Python**: 3.7+ (included with <PERSON>plunk)
- **Memory**: Minimum 512MB available
- **Disk Space**: 100MB for app + log storage
- **Network**: HTTP/HTTPS access for testing

### Required Access
- Splunk admin privileges
- Command line access to Splunk server
- Network connectivity for API testing

## 🚀 Installation Steps

### Step 1: Download and Install

#### Option A: Splunk Web Interface
```bash
# 1. Package the add-on (if needed)
cd /path/to/opa-addon
python setup.py splunk_package

# 2. Install via Splunk Web
# - Navigate to Apps → Manage Apps
# - Click "Install app from file"
# - Upload the .spl file
# - Restart Splunk
```

#### Option B: Command Line Installation
```bash
# 1. Copy to Splunk apps directory
cp -r /path/to/opa-addon $SPLUNK_HOME/etc/apps/opa_policy_audit_addon/

# 2. Set permissions
chown -R splunk:splunk $SPLUNK_HOME/etc/apps/opa_policy_audit_addon/

# 3. Install Python dependencies
cd $SPLUNK_HOME/etc/apps/opa_policy_audit_addon/
$SPLUNK_HOME/bin/splunk cmd python -m pip install -r requirements.txt

# 4. Restart Splunk
$SPLUNK_HOME/bin/splunk restart
```

### Step 2: Verify Installation

```bash
# Check if app is installed
$SPLUNK_HOME/bin/splunk display app opa_policy_audit_addon

# Verify inputs are configured
$SPLUNK_HOME/bin/splunk list inputstatus
```

## 🧪 Testing Scenarios

### Test 1: OPA Decision Log HTTP Listener

#### Setup
1. **Configure the HTTP listener** (already configured by default):
   ```ini
   # File: default/inputs.conf
   [opa_decision_logs://default]
   disabled = 0
   http_port = 8088
   http_path = /opadecisions
   index = opa_audit
   sourcetype = opa:decision
   ```

2. **Start the input** (automatic on Splunk restart)

#### Test Execution
```bash
# Send a test OPA decision log
curl -X POST http://localhost:8088/opadecisions \
  -H "Content-Type: application/json" \
  -d '{
    "decision_id": "test-001",
    "timestamp": "2024-01-15T10:30:00.123Z",
    "path": "data/authz/allow",
    "result": false,
    "input": {
      "user": "test.user",
      "method": "GET",
      "path": "/api/sensitive-data",
      "client_ip": "*************"
    },
    "metrics": {
      "timer_rego_query_eval_ns": 1500000,
      "timer_server_handler_ns": 2000000
    },
    "requested_by": "test-service",
    "revision": "v1.0.0"
  }'
```

#### Verification
```splunk
# Search in Splunk
index=opa_audit sourcetype="opa:decision" decision_id="test-001"

# Expected fields to be extracted:
# - decision_id, path, result, input_user, input_method, input_path
# - query_duration_ms, server_duration_ms, policy_name
# - decision_result (calculated field: "deny" for result=false)
```

### Test 2: Multiple Decision Logs with Different Results

```bash
# Test successful authorization
curl -X POST http://localhost:8088/opadecisions \
  -H "Content-Type: application/json" \
  -d '{
    "decision_id": "test-002",
    "timestamp": "2024-01-15T10:31:00.123Z",
    "path": "data/authz/allow",
    "result": true,
    "input": {
      "user": "admin.user",
      "method": "GET",
      "path": "/api/public-data",
      "client_ip": "*********"
    },
    "metrics": {
      "timer_rego_query_eval_ns": 800000,
      "timer_server_handler_ns": 1200000
    },
    "requested_by": "web-service",
    "revision": "v1.0.0"
  }'

# Test policy violation (high-risk scenario)
curl -X POST http://localhost:8088/opadecisions \
  -H "Content-Type: application/json" \
  -d '{
    "decision_id": "test-003",
    "timestamp": "2024-01-15T10:32:00.123Z",
    "path": "data/authz/admin_access",
    "result": false,
    "input": {
      "user": "suspicious.user",
      "method": "DELETE",
      "path": "/api/admin/users",
      "client_ip": "***********"
    },
    "metrics": {
      "timer_rego_query_eval_ns": 2500000,
      "timer_server_handler_ns": 3000000
    },
    "requested_by": "unknown-service",
    "revision": "v1.0.0"
  }'
```

### Test 3: Styra DAS Integration (Mock Test)

#### Setup Mock Styra DAS Server
```python
# Create a simple mock server for testing
# File: test_styra_mock.py
import json
from http.server import HTTPServer, BaseHTTPRequestHandler

class MockStyraHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if '/v1/systems/' in self.path and '/audits' in self.path:
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            mock_response = {
                "result": [
                    {
                        "id": "audit-001",
                        "timestamp": "2024-01-15T10:30:00.123Z",
                        "type": "policy.updated",
                        "user": {
                            "id": "user-123",
                            "name": "Policy Admin"
                        },
                        "object": {
                            "type": "policy",
                            "id": "policy-456",
                            "name": "authz-policy"
                        },
                        "action": "update",
                        "status": "success",
                        "system_id": "test-system",
                        "tenant": "test-tenant"
                    }
                ]
            }
            
            self.wfile.write(json.dumps(mock_response).encode())

if __name__ == '__main__':
    server = HTTPServer(('localhost', 9999), MockStyraHandler)
    print("Mock Styra DAS server running on http://localhost:9999")
    server.serve_forever()
```

#### Run Mock Server and Test
```bash
# 1. Start mock server
python test_styra_mock.py &

# 2. Configure Styra DAS input (modify inputs.conf)
[styra_das_audit://test]
disabled = 0
interval = 60
api_endpoint = http://localhost:9999/v1/systems/test-system/audits
api_token = test-token
index = opa_audit
sourcetype = styra:das:policy:audit

# 3. Restart Splunk input
$SPLUNK_HOME/bin/splunk restart splunkd
```

### Test 4: Health Monitoring

#### Setup Mock OPA Health Endpoint
```python
# File: test_opa_health_mock.py
import json
from http.server import HTTPServer, BaseHTTPRequestHandler

class MockOPAHealthHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            health_response = {
                "status": "healthy",
                "timestamp": "2024-01-15T10:30:00.123Z",
                "bundle_status": "active",
                "last_bundle_update": "2024-01-15T09:00:00.000Z"
            }
            
            self.wfile.write(json.dumps(health_response).encode())

if __name__ == '__main__':
    server = HTTPServer(('localhost', 8181), MockOPAHealthHandler)
    print("Mock OPA health server running on http://localhost:8181")
    server.serve_forever()
```

#### Test Health Monitoring
```bash
# 1. Start mock OPA server
python test_opa_health_mock.py &

# 2. Enable health monitoring (modify inputs.conf)
[opa_health_monitor://test]
disabled = 0
interval = 60
opa_endpoints = http://localhost:8181/health
index = opa_audit
sourcetype = opa:health

# 3. Restart Splunk
$SPLUNK_HOME/bin/splunk restart splunkd
```

## 🔍 Verification Searches

### Basic Data Ingestion
```splunk
# Check all OPA data
index=opa_audit | head 20

# Check decision logs specifically
index=opa_audit sourcetype="opa:decision" | head 10

# Check field extractions
index=opa_audit sourcetype="opa:decision" 
| table _time decision_id path result decision_result input_user policy_name query_duration_ms
```

### Security Analytics
```splunk
# Policy violations (denied access)
index=opa_audit sourcetype="opa:decision" result="false" 
| stats count by input_user, path, input_method
| sort -count

# Performance analysis
index=opa_audit sourcetype="opa:decision" 
| eval slow_query=if(query_duration_ms>100, "slow", "fast")
| stats avg(query_duration_ms) as avg_duration, max(query_duration_ms) as max_duration by slow_query

# User behavior analysis
index=opa_audit sourcetype="opa:decision" 
| stats count as total_requests, 
        sum(eval(if(result="false", 1, 0))) as denied_requests 
        by input_user
| eval denial_rate=round((denied_requests/total_requests)*100, 2)
| sort -denial_rate
```

### CIM Data Model Verification
```splunk
# Test Authentication data model
| datamodel Authentication_OPA Authentication search
| head 10

# Test Change Analysis data model
| datamodel Change_Analysis_OPA All_Changes search
| head 10

# Test Alerts data model
| datamodel Alerts_OPA Alerts search
| head 10
```

## 📊 Expected Results

### Test 1 Results
- **Events Indexed**: 1 event in `opa_audit` index
- **Sourcetype**: `opa:decision`
- **Extracted Fields**: `decision_id`, `path`, `result`, `input_user`, `policy_name`, `query_duration_ms`
- **Calculated Fields**: `decision_result="deny"`, `policy_name="allow"`

### Test 2 Results
- **Events Indexed**: 3 events total
- **Allow Event**: `decision_result="allow"` for test-002
- **Deny Events**: `decision_result="deny"` for test-001 and test-003
- **Performance Metrics**: Query durations properly calculated in milliseconds

### Test 3 Results (if Styra DAS configured)
- **Events Indexed**: Policy change events
- **Sourcetype**: `styra:das:policy:audit`
- **Fields**: `event_type`, `user`, `action`, `object_name`, `status`

### Test 4 Results (if Health Monitoring configured)
- **Events Indexed**: Health status events
- **Sourcetype**: `opa:health`
- **Fields**: `status`, `bundle_status`, `response_time_ms`

## 🐛 Troubleshooting

### Common Issues

#### 1. HTTP Listener Not Starting
```bash
# Check if port is available
netstat -an | grep 8088

# Check Splunk logs
tail -f $SPLUNK_HOME/var/log/splunk/splunkd.log | grep opa_decision_logs

# Check input status
$SPLUNK_HOME/bin/splunk list inputstatus | grep opa_decision_logs
```

#### 2. No Data Appearing in Index
```bash
# Check index exists
$SPLUNK_HOME/bin/splunk list index | grep opa_audit

# Check data ingestion
index=_internal source="*metrics.log" group=per_index_thruput series=opa_audit

# Check for parsing errors
index=_internal source="*splunkd.log" "opa_decision_logs" ERROR
```

#### 3. Field Extractions Not Working
```splunk
# Test field extraction manually
index=opa_audit sourcetype="opa:decision" | rex field=_raw "\"decision_id\":\"(?<test_decision_id>[^\"]+)\""

# Check props.conf is loaded
| rest /services/configs/conf-props | search title="opa:decision"
```

### Log Locations
- **Splunk Main Log**: `$SPLUNK_HOME/var/log/splunk/splunkd.log`
- **Input Logs**: Search for "opa_decision_logs" in splunkd.log
- **Python Errors**: `$SPLUNK_HOME/var/log/splunk/python.log`

## 📈 Performance Testing

### Load Testing Script
```bash
#!/bin/bash
# File: load_test.sh

echo "Starting load test..."
for i in {1..100}; do
  curl -X POST http://localhost:8088/opadecisions \
    -H "Content-Type: application/json" \
    -d "{
      \"decision_id\": \"load-test-$i\",
      \"timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)\",
      \"path\": \"data/authz/allow\",
      \"result\": $([ $((RANDOM % 2)) -eq 0 ] && echo true || echo false),
      \"input\": {
        \"user\": \"load-test-user-$((i % 10))\",
        \"method\": \"GET\",
        \"path\": \"/api/test-$i\"
      },
      \"metrics\": {
        \"timer_rego_query_eval_ns\": $((RANDOM * 1000000)),
        \"timer_server_handler_ns\": $((RANDOM * 2000000))
      }
    }" &
  
  if [ $((i % 10)) -eq 0 ]; then
    echo "Sent $i requests..."
    sleep 1
  fi
done

wait
echo "Load test completed. Sent 100 requests."
```

### Performance Verification
```splunk
# Check ingestion rate
index=opa_audit sourcetype="opa:decision" decision_id="load-test-*"
| bucket _time span=1m
| stats count by _time
| sort _time

# Verify all events received
index=opa_audit sourcetype="opa:decision" decision_id="load-test-*"
| stats count
# Should show 100 events
```

## ✅ Success Criteria

### Functional Tests
- [ ] HTTP listener accepts and processes OPA decision logs
- [ ] Field extractions work correctly for all test data
- [ ] Calculated fields are properly computed
- [ ] Data appears in correct index with correct sourcetype
- [ ] CIM data models are populated
- [ ] Search performance is acceptable

### Integration Tests
- [ ] Styra DAS integration works (if configured)
- [ ] Health monitoring functions (if configured)
- [ ] Multiple concurrent connections handled
- [ ] SSL/TLS configuration works (if enabled)

### Performance Tests
- [ ] Can handle 100+ events without errors
- [ ] Memory usage remains stable
- [ ] No significant performance degradation
- [ ] All events are indexed successfully

## 📞 Support

If you encounter issues during testing:

1. **Check the logs** in the locations specified above
2. **Verify configuration** files match the examples
3. **Test network connectivity** to mock servers
4. **Review Splunk permissions** for the add-on

---

*This testing guide provides comprehensive validation of the OPA Policy Audit & Compliance Add-on functionality and integration with Splunk.*