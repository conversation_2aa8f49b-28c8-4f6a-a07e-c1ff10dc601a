#!/usr/bin/env python3
"""
OPA Policy Audit & Compliance Add-on REST Handler

This module provides REST endpoints for configuration management,
allowing users to configure the add-on through the web interface.

Author: OPA Community
Version: 1.0.0
"""


import logging
import os

import sys
from urllib.parse import urlparse

# Add the lib directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "lib"))

try:
    import splunk.admin as admin
    import splunk.entity as en
    from splunk.appserver.mrsparkle.lib.util import make_splunkhome_path
except ImportError as e:
    print(f"Error importing Splunk libraries: {e}", file=sys.stderr)
    sys.exit(1)


class OPAAddonSetupHandler(admin.MConfigHandler):
    """
    REST handler for OPA add-on configuration management.

    Handles GET and POST requests for configuration settings,
    validates input parameters, and manages configuration persistence.
    """

    def setup(self):
        """
        Setup the configuration handler with supported arguments.
        """
        # OPA Decision Logs Configuration
        self.supportedArgs.addOptArg("opa_http_port")
        self.supportedArgs.addOptArg("opa_http_path")
        self.supportedArgs.addOptArg("opa_ssl_enabled")
        self.supportedArgs.addOptArg("opa_ssl_cert_path")
        self.supportedArgs.addOptArg("opa_ssl_key_path")
        self.supportedArgs.addOptArg("opa_max_content_length")
        self.supportedArgs.addOptArg("opa_max_connections")
        self.supportedArgs.addOptArg("opa_buffer_size")
        self.supportedArgs.addOptArg("opa_timeout")

        # Styra DAS Configuration
        self.supportedArgs.addOptArg("styra_enabled")
        self.supportedArgs.addOptArg("styra_api_endpoint")
        self.supportedArgs.addOptArg("styra_api_token")
        self.supportedArgs.addOptArg("styra_polling_interval")
        self.supportedArgs.addOptArg("styra_max_events_per_request")
        self.supportedArgs.addOptArg("styra_request_timeout")
        self.supportedArgs.addOptArg("styra_retry_attempts")

        # Health Monitoring Configuration
        self.supportedArgs.addOptArg("health_monitoring_enabled")
        self.supportedArgs.addOptArg("opa_endpoints")
        self.supportedArgs.addOptArg("health_check_interval")
        self.supportedArgs.addOptArg("health_timeout")

        # Data Configuration
        self.supportedArgs.addOptArg("target_index")
        self.supportedArgs.addOptArg("log_level")

        # Advanced Configuration
        self.supportedArgs.addOptArg("enable_metrics_collection")
        self.supportedArgs.addOptArg("metrics_polling_interval")
        self.supportedArgs.addOptArg("enable_performance_monitoring")

    def handleList(self, confInfo):
        """
        Handle GET requests to retrieve current configuration.
        """
        try:
            # Get current configuration from the configuration file
            config = self._get_current_config()

            # Create configuration entry
            confItem = confInfo["setup"]

            # Populate configuration values
            for key, value in config.items():
                confItem[key] = value

        except Exception as e:
            logging.error(f"Error retrieving configuration: {str(e)}")
            raise admin.ArgValidationException(
                f"Failed to retrieve configuration: {str(e)}"
            )

    def handleEdit(self, confInfo):
        """
        Handle POST requests to update configuration.
        """
        try:
            # Validate the configuration
            validated_config = self._validate_config(self.callerArgs.data)

            # Save the configuration
            self._save_config(validated_config)

            # Update inputs.conf with new settings
            self._update_inputs_conf(validated_config)

            # Return success response
            confItem = confInfo["setup"]
            confItem["status"] = "success"
            confItem["message"] = "Configuration saved successfully"

        except admin.ArgValidationException:
            raise
        except Exception as e:
            logging.error(f"Error saving configuration: {str(e)}")
            raise admin.ArgValidationException(
                f"Failed to save configuration: {str(e)}"
            )

    def _get_current_config(self):
        """
        Retrieve current configuration from the configuration file.
        """
        config_file = make_splunkhome_path(
            [
                "etc",
                "apps",
                "opa_policy_audit_addon",
                "local",
                "opa_addon_settings.conf",
            ]
        )

        default_config = {
            "opa_http_port": "8088",
            "opa_http_path": "/opadecisions",
            "opa_ssl_enabled": "0",
            "opa_ssl_cert_path": "",
            "opa_ssl_key_path": "",
            "opa_max_content_length": "10485760",
            "opa_max_connections": "100",
            "opa_buffer_size": "8192",
            "opa_timeout": "30",
            "styra_enabled": "0",
            "styra_api_endpoint": "",
            "styra_api_token": "",
            "styra_polling_interval": "300",
            "styra_max_events_per_request": "1000",
            "styra_request_timeout": "60",
            "styra_retry_attempts": "3",
            "health_monitoring_enabled": "1",
            "opa_endpoints": "http://localhost:8181",
            "health_check_interval": "60",
            "health_timeout": "10",
            "target_index": "opa_audit",
            "log_level": "INFO",
            "enable_metrics_collection": "1",
            "metrics_polling_interval": "120",
            "enable_performance_monitoring": "1",
        }

        try:
            if os.path.exists(config_file):
                # Read existing configuration
                entities = en.getEntities(
                    ["configs", "conf-opa_addon_settings"],
                    namespace="opa_policy_audit_addon",
                    owner="nobody",
                    sessionKey=self.getSessionKey(),
                )

                if "setup" in entities:
                    config = dict(entities["setup"])
                    # Merge with defaults for any missing keys
                    for key, default_value in default_config.items():
                        if key not in config:
                            config[key] = default_value
                    return config
        except Exception as e:
            logging.warning(f"Could not read existing configuration: {str(e)}")

        return default_config

    def _validate_config(self, config):
        """
        Validate configuration parameters.
        """
        validated = {}
        errors = []

        # Validate OPA HTTP port
        try:
            port = int(config.get("opa_http_port", 8088))
            if port < 1 or port > 65535:
                errors.append("OPA HTTP port must be between 1 and 65535")
            validated["opa_http_port"] = str(port)
        except (ValueError, TypeError):
            errors.append("OPA HTTP port must be a valid number")

        # Validate HTTP path
        http_path = config.get("opa_http_path", "/opadecisions")
        if not http_path.startswith("/"):
            errors.append("HTTP path must start with /")
        validated["opa_http_path"] = http_path

        # Validate SSL configuration
        ssl_enabled = config.get("opa_ssl_enabled", "0")
        validated["opa_ssl_enabled"] = (
            "1" if ssl_enabled in ("1", "true", "True", "on") else "0"
        )

        if validated["opa_ssl_enabled"] == "1":
            ssl_cert = config.get("opa_ssl_cert_path", "")
            ssl_key = config.get("opa_ssl_key_path", "")

            if not ssl_cert:
                errors.append("SSL certificate path is required when SSL is enabled")
            elif not os.path.exists(ssl_cert):
                errors.append(f"SSL certificate file not found: {ssl_cert}")

            if not ssl_key:
                errors.append("SSL private key path is required when SSL is enabled")
            elif not os.path.exists(ssl_key):
                errors.append(f"SSL private key file not found: {ssl_key}")

            validated["opa_ssl_cert_path"] = ssl_cert
            validated["opa_ssl_key_path"] = ssl_key
        else:
            validated["opa_ssl_cert_path"] = ""
            validated["opa_ssl_key_path"] = ""

        # Validate numeric parameters
        numeric_params = {
            "opa_max_content_length": (1024, 104857600, "Maximum content length"),
            "opa_max_connections": (1, 1000, "Maximum connections"),
            "opa_buffer_size": (1024, 1048576, "Buffer size"),
            "opa_timeout": (1, 300, "Timeout"),
            "styra_polling_interval": (60, 3600, "Styra polling interval"),
            "styra_max_events_per_request": (1, 10000, "Max events per request"),
            "styra_request_timeout": (5, 300, "Request timeout"),
            "styra_retry_attempts": (0, 10, "Retry attempts"),
            "health_check_interval": (30, 3600, "Health check interval"),
            "health_timeout": (1, 60, "Health timeout"),
            "metrics_polling_interval": (60, 3600, "Metrics polling interval"),
        }

        for param, (min_val, max_val, description) in numeric_params.items():
            try:
                value = int(config.get(param, 0))
                if value < min_val or value > max_val:
                    errors.append(
                        f"{description} must be between {min_val} and {max_val}"
                    )
                validated[param] = str(value)
            except (ValueError, TypeError):
                errors.append(f"{description} must be a valid number")

        # Validate Styra DAS configuration
        styra_enabled = config.get("styra_enabled", "0")
        validated["styra_enabled"] = (
            "1" if styra_enabled in ("1", "true", "True", "on") else "0"
        )

        if validated["styra_enabled"] == "1":
            api_endpoint = config.get("styra_api_endpoint", "")
            api_token = config.get("styra_api_token", "")

            if not api_endpoint:
                errors.append(
                    "Styra DAS API endpoint is required when Styra "
                    "integration is enabled"
                )
            else:
                try:
                    parsed_url = urlparse(api_endpoint)
                    if not parsed_url.scheme or not parsed_url.netloc:
                        errors.append("Invalid Styra DAS API endpoint URL format")
                    elif not parsed_url.scheme.startswith("http"):
                        errors.append("Styra DAS API endpoint must use HTTP or HTTPS")
                except Exception:
                    errors.append("Invalid Styra DAS API endpoint URL")

            if not api_token:
                errors.append(
                    "Styra DAS API token is required when Styra integration is enabled"
                )

            validated["styra_api_endpoint"] = api_endpoint
            validated["styra_api_token"] = api_token
        else:
            validated["styra_api_endpoint"] = ""
            validated["styra_api_token"] = ""

        # Validate OPA endpoints for health monitoring
        health_enabled = config.get("health_monitoring_enabled", "1")
        validated["health_monitoring_enabled"] = (
            "1" if health_enabled in ("1", "true", "True", "on") else "0"
        )

        if validated["health_monitoring_enabled"] == "1":
            endpoints_str = config.get("opa_endpoints", "http://localhost:8181")
            endpoints = [ep.strip() for ep in endpoints_str.split("\n") if ep.strip()]

            if not endpoints:
                errors.append(
                    "At least one OPA endpoint is required for health monitoring"
                )
            else:
                for endpoint in endpoints:
                    try:
                        parsed_url = urlparse(endpoint)
                        if not parsed_url.scheme or not parsed_url.netloc:
                            errors.append(
                                f"Invalid OPA endpoint URL format: {endpoint}"
                            )
                        elif not parsed_url.scheme.startswith("http"):
                            errors.append(
                                f"OPA endpoint must use HTTP or HTTPS: {endpoint}"
                            )
                    except Exception:
                        errors.append(f"Invalid OPA endpoint URL: {endpoint}")

            validated["opa_endpoints"] = endpoints_str
        else:
            validated["opa_endpoints"] = ""

        # Validate other parameters
        validated["target_index"] = config.get("target_index", "opa_audit")

        log_level = config.get("log_level", "INFO").upper()
        if log_level not in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
            errors.append("Invalid log level")
        validated["log_level"] = log_level

        # Boolean parameters
        boolean_params = ["enable_metrics_collection", "enable_performance_monitoring"]
        for param in boolean_params:
            value = config.get(param, "1")
            validated[param] = "1" if value in ("1", "true", "True", "on") else "0"

        if errors:
            raise admin.ArgValidationException("; ".join(errors))

        return validated

    def _save_config(self, config):
        """
        Save configuration to the configuration file.
        """
        try:
            # Create the configuration stanza
            postargs = {}
            for key, value in config.items():
                postargs[key] = value

            # Save to configuration file
            en.setEntity(
                "configs/conf-opa_addon_settings",
                "setup",
                postargs,
                namespace="opa_policy_audit_addon",
                owner="nobody",
                sessionKey=self.getSessionKey(),
            )

        except Exception as e:
            logging.error(f"Error saving configuration: {str(e)}")
            raise

    def _update_inputs_conf(self, config):
        """
        Update inputs.conf with new configuration settings.
        """
        try:
            # Update OPA decision logs input
            opa_input_args = {
                "http_port": config["opa_http_port"],
                "http_path": config["opa_http_path"],
                "ssl_enabled": config["opa_ssl_enabled"],
                "ssl_cert_path": config["opa_ssl_cert_path"],
                "ssl_key_path": config["opa_ssl_key_path"],
                "max_content_length": config["opa_max_content_length"],
                "max_connections": config["opa_max_connections"],
                "buffer_size": config["opa_buffer_size"],
                "timeout": config["opa_timeout"],
                "log_level": config["log_level"],
                "index": config["target_index"],
            }

            en.setEntity(
                "data/inputs/opa_decision_logs",
                "default",
                opa_input_args,
                namespace="opa_policy_audit_addon",
                owner="nobody",
                sessionKey=self.getSessionKey(),
            )

            # Update Styra DAS input if enabled
            if config["styra_enabled"] == "1":
                styra_input_args = {
                    "disabled": "0",
                    "api_endpoint": config["styra_api_endpoint"],
                    "api_token": config["styra_api_token"],
                    "interval": config["styra_polling_interval"],
                    "max_events_per_request": config["styra_max_events_per_request"],
                    "request_timeout": config["styra_request_timeout"],
                    "retry_attempts": config["styra_retry_attempts"],
                    "log_level": config["log_level"],
                    "index": config["target_index"],
                }

                en.setEntity(
                    "data/inputs/styra_das_audit",
                    "default",
                    styra_input_args,
                    namespace="opa_policy_audit_addon",
                    owner="nobody",
                    sessionKey=self.getSessionKey(),
                )
            else:
                # Disable Styra DAS input
                en.setEntity(
                    "data/inputs/styra_das_audit",
                    "default",
                    {"disabled": "1"},
                    namespace="opa_policy_audit_addon",
                    owner="nobody",
                    sessionKey=self.getSessionKey(),
                )

            # Update health monitoring input if enabled
            if config["health_monitoring_enabled"] == "1":
                health_input_args = {
                    "disabled": "0",
                    "opa_endpoints": config["opa_endpoints"],
                    "interval": config["health_check_interval"],
                    "request_timeout": config["health_timeout"],
                    "log_level": config["log_level"],
                    "index": config["target_index"],
                }

                en.setEntity(
                    "data/inputs/opa_health_monitor",
                    "default",
                    health_input_args,
                    namespace="opa_policy_audit_addon",
                    owner="nobody",
                    sessionKey=self.getSessionKey(),
                )
            else:
                # Disable health monitoring input
                en.setEntity(
                    "data/inputs/opa_health_monitor",
                    "default",
                    {"disabled": "1"},
                    namespace="opa_policy_audit_addon",
                    owner="nobody",
                    sessionKey=self.getSessionKey(),
                )

        except Exception as e:
            logging.error(f"Error updating inputs configuration: {str(e)}")
            # Don't raise here as the main config was saved successfully


# Initialize the handler
admin.init(OPAAddonSetupHandler, admin.CONTEXT_NONE)
