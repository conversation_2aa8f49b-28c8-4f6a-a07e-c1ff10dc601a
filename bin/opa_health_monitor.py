#!/usr/bin/env python3
"""
OPA Health Monitor Modular Input

This modular input monitors OPA instance health, bundle status,
and performance metrics for operational visibility.

Author: OPA Community
Version: 1.0.0
"""

import sys
import os
import json
import time
import logging
import requests
from datetime import datetime, timezone
import concurrent.futures
from urllib.parse import urljoin, urlparse

# Add the lib directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'lib'))

try:
    from splunklib.modularinput import *
    from splunklib.modularinput.event_writer import EventWriter
except ImportError as e:
    print(f"Error importing Splunk libraries: {e}", file=sys.stderr)
    sys.exit(1)

class OPAHealthMonitorInput(Script):
    """Modular input for OPA health monitoring"""
    
    def get_scheme(self):
        """Define the input scheme"""
        scheme = Scheme("OPA Health Monitor")
        scheme.description = "Monitor OPA instance health, bundle status, and performance metrics"
        scheme.use_external_validation = True
        scheme.use_single_instance = False
        
        # OPA Endpoints Configuration
        opa_endpoints_arg = Argument("opa_endpoints")
        opa_endpoints_arg.data_type = Argument.data_type_string
        opa_endpoints_arg.description = "Comma-separated list of OPA endpoints to monitor (e.g., http://opa1:8181,http://opa2:8181)"
        opa_endpoints_arg.required_on_create = True
        opa_endpoints_arg.required_on_edit = True
        scheme.add_argument(opa_endpoints_arg)
        
        # Request Configuration
        request_timeout_arg = Argument("request_timeout")
        request_timeout_arg.data_type = Argument.data_type_number
        request_timeout_arg.description = "HTTP request timeout in seconds"
        request_timeout_arg.required_on_create = False
        scheme.add_argument(request_timeout_arg)
        
        # Health Check Configuration
        health_threshold_arg = Argument("health_threshold")
        health_threshold_arg.data_type = Argument.data_type_number
        health_threshold_arg.description = "Number of consecutive failures before marking instance as unhealthy"
        health_threshold_arg.required_on_create = False
        scheme.add_argument(health_threshold_arg)
        
        # Monitoring Features
        check_bundles_arg = Argument("check_bundles")
        check_bundles_arg.data_type = Argument.data_type_boolean
        check_bundles_arg.description = "Enable bundle status monitoring"
        check_bundles_arg.required_on_create = False
        scheme.add_argument(check_bundles_arg)
        
        check_plugins_arg = Argument("check_plugins")
        check_plugins_arg.data_type = Argument.data_type_boolean
        check_plugins_arg.description = "Enable plugin status monitoring"
        check_plugins_arg.required_on_create = False
        scheme.add_argument(check_plugins_arg)
        
        check_metrics_arg = Argument("check_metrics")
        check_metrics_arg.data_type = Argument.data_type_boolean
        check_metrics_arg.description = "Enable performance metrics collection"
        check_metrics_arg.required_on_create = False
        scheme.add_argument(check_metrics_arg)
        
        # Authentication (if required)
        auth_token_arg = Argument("auth_token")
        auth_token_arg.data_type = Argument.data_type_string
        auth_token_arg.description = "Authentication token for OPA API (if required)"
        auth_token_arg.required_on_create = False
        scheme.add_argument(auth_token_arg)
        
        # Logging Configuration
        log_level_arg = Argument("log_level")
        log_level_arg.data_type = Argument.data_type_string
        log_level_arg.description = "Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)"
        log_level_arg.required_on_create = False
        scheme.add_argument(log_level_arg)
        
        return scheme
    
    def validate_input(self, validation_definition):
        """Validate the input configuration"""
        try:
            parameters = validation_definition.parameters
            
            # Validate OPA endpoints
            endpoints_str = parameters.get("opa_endpoints", "")
            if not endpoints_str:
                raise ValueError("OPA endpoints are required")
            
            endpoints = [ep.strip() for ep in endpoints_str.split(',') if ep.strip()]
            if not endpoints:
                raise ValueError("At least one OPA endpoint must be specified")
            
            for endpoint in endpoints:
                parsed_url = urlparse(endpoint)
                if not parsed_url.scheme or not parsed_url.netloc:
                    raise ValueError(f"Invalid endpoint URL format: {endpoint}")
                if not parsed_url.scheme.startswith('http'):
                    raise ValueError(f"Endpoint must use HTTP or HTTPS: {endpoint}")
            
            # Validate numeric parameters
            timeout = int(parameters.get("request_timeout", 10))
            if timeout < 1 or timeout > 300:
                raise ValueError("Request timeout must be between 1 and 300 seconds")
            
            health_threshold = int(parameters.get("health_threshold", 5))
            if health_threshold < 1 or health_threshold > 100:
                raise ValueError("Health threshold must be between 1 and 100")
            
            # Validate log level
            log_level = parameters.get("log_level", "INFO").upper()
            if log_level not in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
                raise ValueError("Invalid log level")
            
            # Test connectivity to OPA endpoints
            self.test_opa_connectivity(endpoints, timeout, parameters.get("auth_token", ""))
            
        except Exception as e:
            raise Exception(f"Validation failed: {str(e)}")
    
    def test_opa_connectivity(self, endpoints, timeout, auth_token):
        """Test connectivity to OPA endpoints"""
        headers = {'User-Agent': 'Splunk-OPA-Addon/1.0.0'}
        if auth_token:
            headers['Authorization'] = f'Bearer {auth_token}'
        
        failed_endpoints = []
        
        for endpoint in endpoints:
            try:
                health_url = urljoin(endpoint, '/health')
                response = requests.get(health_url, headers=headers, timeout=timeout)
                
                if response.status_code >= 400:
                    failed_endpoints.append(f"{endpoint} (HTTP {response.status_code})")
                    
            except requests.exceptions.RequestException as e:
                failed_endpoints.append(f"{endpoint} ({str(e)})")
        
        if failed_endpoints:
            raise ValueError(f"Cannot connect to OPA endpoints: {', '.join(failed_endpoints)}")
    
    def stream_events(self, inputs, ew):
        """Main execution method"""
        self.service.namespace['app'] = 'opa_policy_audit_addon'
        
        for input_name, input_item in inputs.inputs.items():
            try:
                # Extract configuration
                config = {
                    'opa_endpoints': [ep.strip() for ep in input_item.get('opa_endpoints', '').split(',') if ep.strip()],
                    'request_timeout': int(input_item.get('request_timeout', 10)),
                    'health_threshold': int(input_item.get('health_threshold', 5)),
                    'check_bundles': input_item.get('check_bundles', '1').lower() in ('1', 'true', 'yes'),
                    'check_plugins': input_item.get('check_plugins', '1').lower() in ('1', 'true', 'yes'),
                    'check_metrics': input_item.get('check_metrics', '1').lower() in ('1', 'true', 'yes'),
                    'auth_token': input_item.get('auth_token', ''),
                    'log_level': input_item.get('log_level', 'INFO').upper(),
                    'input_name': input_name,
                    'index': input_item.get('index', 'main'),
                    'sourcetype': input_item.get('sourcetype', 'opa:health'),
                    'host': input_item.get('host', 'opa-monitor')
                }
                
                # Setup logging
                self.setup_logging(config['log_level'])
                
                # Start monitoring
                monitor = OPAHealthMonitor(config, ew, self.logger)
                monitor.run()
                
            except Exception as e:
                ew.log(EventWriter.ERROR, f"Error starting OPA health monitor: {str(e)}")
                raise
    
    def setup_logging(self, log_level):
        """Setup logging configuration"""
        self.logger = logging.getLogger('opa_health_monitor')
        self.logger.setLevel(getattr(logging, log_level))
        
        if not self.logger.handlers:
            handler = logging.StreamHandler(sys.stderr)
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

class OPAHealthMonitor:
    """OPA health monitoring implementation"""
    
    def __init__(self, config, event_writer, logger):
        self.config = config
        self.event_writer = event_writer
        self.logger = logger
        self.session = requests.Session()
        self.endpoint_states = {}
        
        # Setup session headers
        headers = {'User-Agent': 'Splunk-OPA-Addon/1.0.0'}
        if config['auth_token']:
            headers['Authorization'] = f'Bearer {config["auth_token"]}'
        self.session.headers.update(headers)
        
        # Initialize endpoint states
        for endpoint in config['opa_endpoints']:
            self.endpoint_states[endpoint] = {
                'consecutive_failures': 0,
                'last_success': None,
                'last_failure': None,
                'status': 'unknown'
            }
    
    def run(self):
        """Main monitoring loop"""
        self.logger.info(f"Starting OPA health monitoring for {len(self.config['opa_endpoints'])} endpoints")
        
        try:
            while True:
                start_time = time.time()
                
                # Monitor all endpoints
                self.monitor_endpoints()
                
                # Log monitoring cycle completion
                elapsed_time = time.time() - start_time
                self.logger.debug(f"Monitoring cycle completed in {elapsed_time:.2f}s")
                
                # Sleep until next monitoring cycle (based on input interval)
                time.sleep(60)  # Default 60 seconds, will be overridden by Splunk's interval
                
        except KeyboardInterrupt:
            self.logger.info("Monitoring interrupted by user")
        except Exception as e:
            self.logger.error(f"Error in monitoring loop: {str(e)}")
            raise
    
    def monitor_endpoints(self):
        """Monitor all configured OPA endpoints"""
        # Use thread pool for concurrent monitoring
        with concurrent.futures.ThreadPoolExecutor(max_workers=min(10, len(self.config['opa_endpoints']))) as executor:
            future_to_endpoint = {
                executor.submit(self.monitor_endpoint, endpoint): endpoint 
                for endpoint in self.config['opa_endpoints']
            }
            
            for future in concurrent.futures.as_completed(future_to_endpoint):
                endpoint = future_to_endpoint[future]
                try:
                    future.result()
                except Exception as e:
                    self.logger.error(f"Error monitoring endpoint {endpoint}: {str(e)}")
    
    def monitor_endpoint(self, endpoint):
        """Monitor a single OPA endpoint"""
        try:
            # Collect health data
            health_data = self.collect_health_data(endpoint)
            
            # Update endpoint state
            self.update_endpoint_state(endpoint, health_data)
            
            # Send health event to Splunk
            self.send_health_event(endpoint, health_data)
            
        except Exception as e:
            self.logger.error(f"Error monitoring endpoint {endpoint}: {str(e)}")
            # Send error event
            error_data = {
                'endpoint': endpoint,
                'status': 'error',
                'error_message': str(e),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            self.update_endpoint_state(endpoint, error_data)
            self.send_health_event(endpoint, error_data)
    
    def collect_health_data(self, endpoint):
        """Collect comprehensive health data from OPA endpoint"""
        health_data = {
            'endpoint': endpoint,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'checks': {}
        }
        
        # Basic health check
        health_data['checks']['basic_health'] = self.check_basic_health(endpoint)
        
        # Bundle status check
        if self.config['check_bundles']:
            health_data['checks']['bundle_status'] = self.check_bundle_status(endpoint)
        
        # Plugin status check
        if self.config['check_plugins']:
            health_data['checks']['plugin_status'] = self.check_plugin_status(endpoint)
        
        # Metrics check
        if self.config['check_metrics']:
            health_data['checks']['metrics'] = self.check_metrics(endpoint)
        
        # Determine overall status
        health_data['status'] = self.determine_overall_status(health_data['checks'])
        
        return health_data
    
    def check_basic_health(self, endpoint):
        """Perform basic health check"""
        try:
            start_time = time.time()
            health_url = urljoin(endpoint, '/health')
            
            response = self.session.get(
                health_url,
                timeout=self.config['request_timeout']
            )
            
            response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            
            if response.status_code == 200:
                try:
                    health_info = response.json()
                except json.JSONDecodeError:
                    health_info = {}
                
                return {
                    'status': 'healthy',
                    'response_time_ms': round(response_time, 2),
                    'http_status': response.status_code,
                    'details': health_info
                }
            else:
                return {
                    'status': 'unhealthy',
                    'response_time_ms': round(response_time, 2),
                    'http_status': response.status_code,
                    'error': f"HTTP {response.status_code}: {response.text[:200]}"
                }
                
        except requests.exceptions.Timeout:
            return {
                'status': 'unhealthy',
                'error': 'Request timeout',
                'response_time_ms': self.config['request_timeout'] * 1000
            }
        except requests.exceptions.RequestException as e:
            return {
                'status': 'unhealthy',
                'error': str(e)
            }
    
    def check_bundle_status(self, endpoint):
        """Check bundle status"""
        try:
            bundles_url = urljoin(endpoint, '/v1/status/bundles')
            
            response = self.session.get(
                bundles_url,
                timeout=self.config['request_timeout']
            )
            
            if response.status_code == 200:
                bundle_status = response.json()
                
                # Analyze bundle health
                bundles = bundle_status.get('result', {})
                bundle_health = {
                    'status': 'healthy',
                    'bundle_count': len(bundles),
                    'bundles': {}
                }
                
                for bundle_name, bundle_info in bundles.items():
                    bundle_health['bundles'][bundle_name] = {
                        'active_revision': bundle_info.get('active_revision'),
                        'last_successful_download': bundle_info.get('last_successful_download'),
                        'last_successful_activation': bundle_info.get('last_successful_activation'),
                        'code': bundle_info.get('code')
                    }
                    
                    # Check for bundle errors
                    if bundle_info.get('code') and bundle_info['code'] != 'ok':
                        bundle_health['status'] = 'unhealthy'
                
                return bundle_health
            else:
                return {
                    'status': 'error',
                    'error': f"HTTP {response.status_code}: {response.text[:200]}"
                }
                
        except requests.exceptions.RequestException as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def check_plugin_status(self, endpoint):
        """Check plugin status"""
        try:
            plugins_url = urljoin(endpoint, '/v1/status/plugins')
            
            response = self.session.get(
                plugins_url,
                timeout=self.config['request_timeout']
            )
            
            if response.status_code == 200:
                plugin_status = response.json()
                
                plugins = plugin_status.get('result', {})
                plugin_health = {
                    'status': 'healthy',
                    'plugin_count': len(plugins),
                    'plugins': {}
                }
                
                for plugin_name, plugin_info in plugins.items():
                    plugin_health['plugins'][plugin_name] = {
                        'state': plugin_info.get('state'),
                        'last_successful_activation': plugin_info.get('last_successful_activation')
                    }
                    
                    # Check for plugin errors
                    if plugin_info.get('state') != 'OK':
                        plugin_health['status'] = 'unhealthy'
                
                return plugin_health
            else:
                return {
                    'status': 'error',
                    'error': f"HTTP {response.status_code}: {response.text[:200]}"
                }
                
        except requests.exceptions.RequestException as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def check_metrics(self, endpoint):
        """Check performance metrics"""
        try:
            metrics_url = urljoin(endpoint, '/metrics')
            
            response = self.session.get(
                metrics_url,
                timeout=self.config['request_timeout']
            )
            
            if response.status_code == 200:
                # Parse Prometheus-style metrics
                metrics_text = response.text
                metrics = self.parse_prometheus_metrics(metrics_text)
                
                return {
                    'status': 'healthy',
                    'metrics': metrics
                }
            else:
                return {
                    'status': 'error',
                    'error': f"HTTP {response.status_code}: {response.text[:200]}"
                }
                
        except requests.exceptions.RequestException as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def parse_prometheus_metrics(self, metrics_text):
        """Parse Prometheus-style metrics"""
        metrics = {}
        
        for line in metrics_text.split('\n'):
            line = line.strip()
            if line and not line.startswith('#'):
                try:
                    # Simple parsing for key metrics
                    if ' ' in line:
                        metric_part, value_part = line.rsplit(' ', 1)
                        if '{' in metric_part:
                            metric_name = metric_part.split('{')[0]
                        else:
                            metric_name = metric_part
                        
                        try:
                            value = float(value_part)
                            metrics[metric_name] = value
                        except ValueError:
                            continue
                except Exception:
                    continue
        
        return metrics
    
    def determine_overall_status(self, checks):
        """Determine overall health status from individual checks"""
        if not checks:
            return 'unknown'
        
        statuses = [check.get('status', 'unknown') for check in checks.values()]
        
        if 'unhealthy' in statuses:
            return 'unhealthy'
        elif 'error' in statuses:
            return 'error'
        elif all(status == 'healthy' for status in statuses):
            return 'healthy'
        else:
            return 'degraded'
    
    def update_endpoint_state(self, endpoint, health_data):
        """Update endpoint state tracking"""
        state = self.endpoint_states[endpoint]
        current_time = datetime.now(timezone.utc).isoformat()
        
        if health_data['status'] in ['healthy', 'degraded']:
            state['consecutive_failures'] = 0
            state['last_success'] = current_time
            state['status'] = health_data['status']
        else:
            state['consecutive_failures'] += 1
            state['last_failure'] = current_time
            
            if state['consecutive_failures'] >= self.config['health_threshold']:
                state['status'] = 'unhealthy'
            else:
                state['status'] = 'degraded'
    
    def send_health_event(self, endpoint, health_data):
        """Send health event to Splunk"""
        try:
            # Enrich with endpoint state
            enriched_data = health_data.copy()
            enriched_data['endpoint_state'] = self.endpoint_states[endpoint].copy()
            
            # Create Splunk event
            event = Event(
                data=json.dumps(enriched_data),
                time=time.time(),
                index=self.config['index'],
                sourcetype=self.config['sourcetype'],
                host=self.config['host'],
                source=f"opa_health_monitor:{self.config['input_name']}"
            )
            
            # Write event to Splunk
            self.event_writer.write_event(event)
            
        except Exception as e:
            self.logger.error(f"Error sending health event: {str(e)}")

if __name__ == "__main__":
    sys.exit(OPAHealthMonitorInput().run(sys.argv))