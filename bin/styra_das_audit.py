#!/usr/bin/env python3
"""
Styra DAS Policy Audit Logs Modular Input

This modular input polls the Styra DAS API for policy audit events
and forwards them to Splunk for indexing and analysis.

Author: OPA Community
Version: 1.0.0
"""


import hashlib
import json
import logging
import os
import sys
import time
from datetime import datetime, timedelta, timezone

import requests

# Add the lib directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "lib"))

try:
    from splunklib.modularinput import *
    from splunklib.modularinput.event_writer import EventWriter
except ImportError as e:
    print(f"Error importing Splunk libraries: {e}", file=sys.stderr)
    sys.exit(1)


class StyraDASAuditInput(Script):
    """Modular input for Styra DAS policy audit logs"""

    def get_scheme(self):
        """Define the input scheme"""
        scheme = Scheme("Styra DAS Policy Audit Logs")
        scheme.description = (
            "Poll Styra DAS API for policy audit events and system changes"
        )
        scheme.use_external_validation = True
        scheme.use_single_instance = False

        # API Configuration
        api_endpoint_arg = Argument("api_endpoint")
        api_endpoint_arg.data_type = Argument.data_type_string
        api_endpoint_arg.description = (
            "Styra DAS API endpoint URL "
            "(e.g., https://tenant.styra.com/v1/systems/systemid/audits)"
        )
        api_endpoint_arg.required_on_create = True
        api_endpoint_arg.required_on_edit = True
        scheme.add_argument(api_endpoint_arg)

        api_token_arg = Argument("api_token")
        api_token_arg.data_type = Argument.data_type_string
        api_token_arg.description = "Styra DAS API token for authentication"
        api_token_arg.required_on_create = True
        api_token_arg.required_on_edit = True
        scheme.add_argument(api_token_arg)

        # Polling Configuration
        max_events_per_request_arg = Argument("max_events_per_request")
        max_events_per_request_arg.data_type = Argument.data_type_number
        max_events_per_request_arg.description = (
            "Maximum number of events to fetch per API request"
        )
        max_events_per_request_arg.required_on_create = False
        scheme.add_argument(max_events_per_request_arg)

        request_timeout_arg = Argument("request_timeout")
        request_timeout_arg.data_type = Argument.data_type_number
        request_timeout_arg.description = "HTTP request timeout in seconds"
        request_timeout_arg.required_on_create = False
        scheme.add_argument(request_timeout_arg)

        retry_attempts_arg = Argument("retry_attempts")
        retry_attempts_arg.data_type = Argument.data_type_number
        retry_attempts_arg.description = "Number of retry attempts for failed requests"
        retry_attempts_arg.required_on_create = False
        scheme.add_argument(retry_attempts_arg)

        retry_delay_arg = Argument("retry_delay")
        retry_delay_arg.data_type = Argument.data_type_number
        retry_delay_arg.description = "Delay between retry attempts in seconds"
        retry_delay_arg.required_on_create = False
        scheme.add_argument(retry_delay_arg)

        # Time Range Configuration
        start_time_arg = Argument("start_time")
        start_time_arg.data_type = Argument.data_type_string
        start_time_arg.description = (
            "Start time for initial data collection "
            "(e.g., -24h, -7d, 2024-01-01T00:00:00Z)"
        )
        start_time_arg.required_on_create = False
        scheme.add_argument(start_time_arg)

        # Filtering Configuration
        filter_events_arg = Argument("filter_events")
        filter_events_arg.data_type = Argument.data_type_string
        filter_events_arg.description = "JSON filter criteria for events (optional)"
        filter_events_arg.required_on_create = False
        scheme.add_argument(filter_events_arg)

        # Logging Configuration
        log_level_arg = Argument("log_level")
        log_level_arg.data_type = Argument.data_type_string
        log_level_arg.description = (
            "Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)"
        )
        log_level_arg.required_on_create = False
        scheme.add_argument(log_level_arg)

        return scheme

    def validate_input(self, validation_definition):
        """Validate the input configuration"""
        try:
            parameters = validation_definition.parameters

            # Validate API endpoint
            api_endpoint = parameters.get("api_endpoint", "")
            if not api_endpoint:
                raise ValueError("API endpoint is required")

            parsed_url = urlparse(api_endpoint)
            if not parsed_url.scheme or not parsed_url.netloc:
                raise ValueError("Invalid API endpoint URL format")

            if not parsed_url.scheme.startswith("http"):
                raise ValueError("API endpoint must use HTTP or HTTPS")

            # Validate API token
            api_token = parameters.get("api_token", "")
            if not api_token:
                raise ValueError("API token is required")

            # Validate numeric parameters
            max_events = int(parameters.get("max_events_per_request", 1000))
            if max_events < 1 or max_events > 10000:
                raise ValueError("Max events per request must be between 1 and 10000")

            timeout = int(parameters.get("request_timeout", 60))
            if timeout < 1 or timeout > 300:
                raise ValueError("Request timeout must be between 1 and 300 seconds")

            retry_attempts = int(parameters.get("retry_attempts", 3))
            if retry_attempts < 0 or retry_attempts > 10:
                raise ValueError("Retry attempts must be between 0 and 10")

            retry_delay = int(parameters.get("retry_delay", 5))
            if retry_delay < 1 or retry_delay > 60:
                raise ValueError("Retry delay must be between 1 and 60 seconds")

            # Validate filter events JSON if provided
            filter_events = parameters.get("filter_events", "")
            if filter_events:
                try:
                    json.loads(filter_events)
                except json.JSONDecodeError:
                    raise ValueError("Filter events must be valid JSON")

            # Validate log level
            log_level = parameters.get("log_level", "INFO").upper()
            if log_level not in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
                raise ValueError("Invalid log level")

            # Test API connectivity
            self.test_api_connection(api_endpoint, api_token, timeout)

        except Exception as e:
            raise Exception(f"Validation failed: {str(e)}")

    def test_api_connection(self, api_endpoint, api_token, timeout):
        """Test API connection and authentication"""
        try:
            headers = {
                "Authorization": f"Bearer {api_token}",
                "Content-Type": "application/json",
                "User-Agent": "Splunk-OPA-Addon/1.0.0",
            }

            # Try to make a simple request to test connectivity
            response = requests.get(
                api_endpoint, headers=headers, timeout=timeout, params={"limit": 1}
            )

            if response.status_code == 401:
                raise ValueError("Invalid API token - authentication failed")
            elif response.status_code == 403:
                raise ValueError("API token does not have sufficient permissions")
            elif response.status_code == 404:
                raise ValueError("API endpoint not found - check the URL")
            elif response.status_code >= 400:
                raise ValueError(
                    f"API request failed with status {response.status_code}: "
                    f"{response.text}"
                )

        except requests.exceptions.Timeout:
            raise ValueError("API request timed out - check network connectivity")
        except requests.exceptions.ConnectionError:
            raise ValueError("Cannot connect to API endpoint - check URL and network")
        except requests.exceptions.RequestException as e:
            raise ValueError(f"API request failed: {str(e)}")

    def stream_events(self, inputs, ew):
        """Main execution method"""
        self.service.namespace["app"] = "opa_policy_audit_addon"

        for input_name, input_item in inputs.inputs.items():
            try:
                # Extract configuration
                config = {
                    "api_endpoint": input_item.get("api_endpoint"),
                    "api_token": input_item.get("api_token"),
                    "max_events_per_request": int(
                        input_item.get("max_events_per_request", 1000)
                    ),
                    "request_timeout": int(input_item.get("request_timeout", 60)),
                    "retry_attempts": int(input_item.get("retry_attempts", 3)),
                    "retry_delay": int(input_item.get("retry_delay", 5)),
                    "start_time": input_item.get("start_time", "-24h"),
                    "filter_events": input_item.get("filter_events", ""),
                    "log_level": input_item.get("log_level", "INFO").upper(),
                    "input_name": input_name,
                    "index": input_item.get("index", "main"),
                    "sourcetype": input_item.get(
                        "sourcetype", "styra:das:policy:audit"
                    ),
                    "host": input_item.get("host", "styra-das"),
                }

                # Setup logging
                self.setup_logging(config["log_level"])

                # Start polling
                poller = StyraDASPoller(config, ew, self.logger)
                poller.run()

            except Exception as e:
                ew.log(
                    EventWriter.ERROR, f"Error starting Styra DAS audit input: {str(e)}"
                )
                raise

    def setup_logging(self, log_level):
        """Setup logging configuration"""
        self.logger = logging.getLogger("styra_das_audit")
        self.logger.setLevel(getattr(logging, log_level))

        if not self.logger.handlers:
            handler = logging.StreamHandler(sys.stderr)
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)


class StyraDASPoller:
    """Styra DAS API poller"""

    def __init__(self, config, event_writer, logger):
        self.config = config
        self.event_writer = event_writer
        self.logger = logger
        self.session = requests.Session()
        self.last_poll_time = None
        self.checkpoint_file = f"/tmp/styra_das_checkpoint_{hashlib.md5(config['input_name'].encode()).hexdigest()}.json"

        # Setup session headers
        self.session.headers.update(
            {
                "Authorization": f'Bearer {config["api_token"]}',
                "Content-Type": "application/json",
                "User-Agent": "Splunk-OPA-Addon/1.0.0",
            }
        )

        # Load checkpoint
        self.load_checkpoint()

    def load_checkpoint(self):
        """Load the last poll time from checkpoint file"""
        try:
            if os.path.exists(self.checkpoint_file):
                with open(self.checkpoint_file, "r") as f:
                    checkpoint = json.load(f)
                    self.last_poll_time = checkpoint.get("last_poll_time")
                    self.logger.info(
                        f"Loaded checkpoint: last_poll_time={self.last_poll_time}"
                    )
            else:
                # First run - use start_time configuration
                self.last_poll_time = self.parse_start_time(self.config["start_time"])
                self.logger.info(f"First run - using start_time: {self.last_poll_time}")
        except Exception as e:
            self.logger.warning(f"Error loading checkpoint: {str(e)}")
            self.last_poll_time = self.parse_start_time(self.config["start_time"])

    def save_checkpoint(self):
        """Save the current poll time to checkpoint file"""
        try:
            checkpoint = {
                "last_poll_time": self.last_poll_time,
                "updated_at": datetime.now(timezone.utc).isoformat(),
            }
            with open(self.checkpoint_file, "w") as f:
                json.dump(checkpoint, f)
        except Exception as e:
            self.logger.error(f"Error saving checkpoint: {str(e)}")

    def parse_start_time(self, start_time_str):
        """Parse start time string into ISO format"""
        try:
            if start_time_str.startswith("-"):
                # Relative time (e.g., -24h, -7d)
                if start_time_str.endswith("h"):
                    hours = int(start_time_str[1:-1])
                    start_time = datetime.now(timezone.utc) - timedelta(hours=hours)
                elif start_time_str.endswith("d"):
                    days = int(start_time_str[1:-1])
                    start_time = datetime.now(timezone.utc) - timedelta(days=days)
                elif start_time_str.endswith("m"):
                    minutes = int(start_time_str[1:-1])
                    start_time = datetime.now(timezone.utc) - timedelta(minutes=minutes)
                else:
                    raise ValueError(f"Invalid relative time format: {start_time_str}")
            else:
                # Absolute time (ISO format)
                start_time = datetime.fromisoformat(
                    start_time_str.replace("Z", "+00:00")
                )

            return start_time.isoformat()
        except Exception as e:
            self.logger.warning(
                f"Error parsing start time '{start_time_str}': {str(e)}"
            )
            # Default to 24 hours ago
            return (datetime.now(timezone.utc) - timedelta(hours=24)).isoformat()

    def run(self):
        """Main polling loop"""
        self.logger.info("Starting Styra DAS audit log polling")

        try:
            while True:
                start_time = time.time()

                # Poll for new events
                events_processed = self.poll_events()

                # Update checkpoint
                self.last_poll_time = datetime.now(timezone.utc).isoformat()
                self.save_checkpoint()

                # Log statistics
                elapsed_time = time.time() - start_time
                self.logger.info(
                    f"Poll completed: {events_processed} events processed in {elapsed_time:.2f}s"
                )

                # Sleep until next poll (based on input interval)
                time.sleep(
                    60
                )  # Default 60 seconds, will be overridden by Splunk's interval

        except KeyboardInterrupt:
            self.logger.info("Polling interrupted by user")
        except Exception as e:
            self.logger.error(f"Error in polling loop: {str(e)}")
            raise

    def poll_events(self):
        """Poll for new audit events"""
        events_processed = 0

        try:
            # Build API request parameters
            params = {
                "limit": self.config["max_events_per_request"],
                "since": self.last_poll_time,
            }

            # Add filter if configured
            if self.config["filter_events"]:
                filter_criteria = json.loads(self.config["filter_events"])
                params.update(filter_criteria)

            # Make API request with retries
            response = self.make_api_request(self.config["api_endpoint"], params)

            if response and response.status_code == 200:
                data = response.json()

                # Process events
                events = data.get("result", [])
                if isinstance(events, list):
                    for event in events:
                        self.process_audit_event(event)
                        events_processed += 1
                else:
                    self.logger.warning(
                        "Unexpected API response format - expected list of events"
                    )

                # Handle pagination if present
                next_page = data.get("next_page")
                while (
                    next_page
                    and events_processed < self.config["max_events_per_request"] * 5
                ):  # Limit total events
                    response = self.make_api_request(next_page, {})
                    if response and response.status_code == 200:
                        data = response.json()
                        events = data.get("result", [])
                        for event in events:
                            self.process_audit_event(event)
                            events_processed += 1
                        next_page = data.get("next_page")
                    else:
                        break

        except Exception as e:
            self.logger.error(f"Error polling events: {str(e)}")

        return events_processed

    def make_api_request(self, url, params):
        """Make API request with retry logic"""
        for attempt in range(self.config["retry_attempts"] + 1):
            try:
                self.logger.debug(
                    f"Making API request to {url} (attempt {attempt + 1})"
                )

                response = self.session.get(
                    url, params=params, timeout=self.config["request_timeout"]
                )

                if response.status_code == 200:
                    return response
                elif response.status_code == 429:  # Rate limited
                    retry_after = int(
                        response.headers.get("Retry-After", self.config["retry_delay"])
                    )
                    self.logger.warning(f"Rate limited, waiting {retry_after} seconds")
                    time.sleep(retry_after)
                    continue
                elif response.status_code >= 500:  # Server error
                    self.logger.warning(
                        f"Server error {response.status_code}, retrying..."
                    )
                    if attempt < self.config["retry_attempts"]:
                        time.sleep(self.config["retry_delay"])
                        continue
                else:
                    self.logger.error(
                        f"API request failed with status {response.status_code}: {response.text}"
                    )
                    return None

            except requests.exceptions.Timeout:
                self.logger.warning(f"Request timeout (attempt {attempt + 1})")
                if attempt < self.config["retry_attempts"]:
                    time.sleep(self.config["retry_delay"])
                    continue
            except requests.exceptions.RequestException as e:
                self.logger.error(f"Request failed: {str(e)}")
                if attempt < self.config["retry_attempts"]:
                    time.sleep(self.config["retry_delay"])
                    continue

        return None

    def process_audit_event(self, event):
        """Process a single audit event"""
        try:
            # Enrich event with metadata
            enriched_event = self.enrich_audit_event(event)

            # Create Splunk event
            splunk_event = Event(
                data=json.dumps(enriched_event),
                time=self.parse_event_time(enriched_event),
                index=self.config["index"],
                sourcetype=self.config["sourcetype"],
                host=self.config["host"],
                source=f"styra_das_audit:{self.config['input_name']}",
            )

            # Write event to Splunk
            self.event_writer.write_event(splunk_event)

        except Exception as e:
            self.logger.error(f"Error processing audit event: {str(e)}")

    def enrich_audit_event(self, event):
        """Enrich audit event with additional metadata"""
        enriched = event.copy()

        # Add ingestion metadata
        enriched["ingestion_time"] = datetime.now(timezone.utc).isoformat()
        enriched["source_system"] = "styra_das"

        # Add unique event ID if not present
        if "id" not in enriched:
            event_data = json.dumps(enriched, sort_keys=True)
            enriched["id"] = hashlib.md5(event_data.encode()).hexdigest()

        # Normalize field names for consistency
        field_mappings = {
            "created_at": "timestamp",
            "updated_at": "timestamp",
            "occurred_at": "timestamp",
        }

        for old_field, new_field in field_mappings.items():
            if old_field in enriched and new_field not in enriched:
                enriched[new_field] = enriched[old_field]

        # Extract tenant and system information from API endpoint
        try:
            parsed_url = urlparse(self.config["api_endpoint"])
            path_parts = parsed_url.path.strip("/").split("/")

            if "systems" in path_parts:
                system_index = path_parts.index("systems")
                if system_index + 1 < len(path_parts):
                    enriched["system_id"] = path_parts[system_index + 1]

            # Extract tenant from hostname
            hostname_parts = parsed_url.hostname.split(".")
            if len(hostname_parts) > 0 and hostname_parts[0] != "api":
                enriched["tenant"] = hostname_parts[0]
        except Exception as e:
            self.logger.debug(f"Error extracting tenant/system info: {str(e)}")

        return enriched

    def parse_event_time(self, event):
        """Parse event timestamp for Splunk"""
        try:
            timestamp_fields = ["timestamp", "created_at", "updated_at", "occurred_at"]

            for field in timestamp_fields:
                if field in event:
                    timestamp_str = event[field]
                    if isinstance(timestamp_str, str):
                        # Parse ISO format timestamp
                        dt = datetime.fromisoformat(
                            timestamp_str.replace("Z", "+00:00")
                        )
                        return dt.timestamp()
                    elif isinstance(timestamp_str, (int, float)):
                        return timestamp_str

            # Fallback to current time
            return time.time()

        except Exception as e:
            self.logger.warning(f"Error parsing event timestamp: {str(e)}")
            return time.time()


if __name__ == "__main__":
    sys.exit(StyraDASAuditInput().run(sys.argv))
