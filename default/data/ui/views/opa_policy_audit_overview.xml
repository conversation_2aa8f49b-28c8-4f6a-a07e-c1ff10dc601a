<dashboard version="1.1">
  <label>OPA Policy Audit &amp; Compliance Overview</label>
  <description>Comprehensive overview of OPA policy decisions, compliance status, and security insights</description>
  <row>
    <panel>
      <title>Policy Decision Summary</title>
      <single>
        <search>
          <query>
            index=main sourcetype="opa:decision" 
            | stats count as total_decisions, 
                   count(eval(decision_result="allow")) as allowed_decisions,
                   count(eval(decision_result="deny")) as denied_decisions
            | eval allow_rate=round((allowed_decisions/total_decisions)*100,2)
            | fields allow_rate
          </query>
          <earliest>-24h@h</earliest>
          <latest>now</latest>
          <refresh>30s</refresh>
        </search>
        <option name="drilldown">none</option>
        <option name="colorBy">value</option>
        <option name="colorMode">none</option>
        <option name="rangeColors">["0x65A637","0x6DB7C6","0xF7BC38","0xF58F39","0xD93F3C"]</option>
        <option name="rangeValues">[0,30,70,90,100]</option>
        <option name="underLabel">Allow Rate %</option>
        <option name="unit">%</option>
      </single>
    </panel>
    <panel>
      <title>Total Decisions (24h)</title>
      <single>
        <search>
          <query>
            index=main sourcetype="opa:decision" 
            | stats count
          </query>
          <earliest>-24h@h</earliest>
          <latest>now</latest>
          <refresh>30s</refresh>
        </search>
        <option name="drilldown">none</option>
        <option name="colorBy">value</option>
        <option name="colorMode">none</option>
        <option name="underLabel">Total Decisions</option>
      </single>
    </panel>
    <panel>
      <title>High-Risk Denials</title>
      <single>
        <search>
          <query>
            index=main sourcetype="opa:decision" decision_result="deny" 
            | lookup policy_criticality policy_path OUTPUT risk_score
            | where risk_score >= 8
            | stats count
          </query>
          <earliest>-24h@h</earliest>
          <latest>now</latest>
          <refresh>30s</refresh>
        </search>
        <option name="drilldown">none</option>
        <option name="colorBy">value</option>
        <option name="colorMode">block</option>
        <option name="rangeColors">["0x65A637","0xF7BC38","0xD93F3C"]</option>
        <option name="rangeValues">[0,10]</option>
        <option name="underLabel">Critical Denials</option>
      </single>
    </panel>
    <panel>
      <title>OPA Instance Health</title>
      <single>
        <search>
          <query>
            index=main sourcetype="opa:health" 
            | stats latest(status) as status by endpoint
            | stats count(eval(status="healthy")) as healthy_instances,
                   count as total_instances
            | eval health_percentage=round((healthy_instances/total_instances)*100,2)
            | fields health_percentage
          </query>
          <earliest>-5m@m</earliest>
          <latest>now</latest>
          <refresh>30s</refresh>
        </search>
        <option name="drilldown">none</option>
        <option name="colorBy">value</option>
        <option name="colorMode">block</option>
        <option name="rangeColors">["0xD93F3C","0xF7BC38","0x65A637"]</option>
        <option name="rangeValues">[80,95]</option>
        <option name="underLabel">Instance Health %</option>
        <option name="unit">%</option>
      </single>
    </panel>
  </row>
  
  <row>
    <panel>
      <title>Policy Decision Trends</title>
      <chart>
        <search>
          <query>
            index=main sourcetype="opa:decision" 
            | timechart span=5m count by decision_result
          </query>
          <earliest>-4h@h</earliest>
          <latest>now</latest>
          <refresh>1m</refresh>
        </search>
        <option name="charting.chart">line</option>
        <option name="charting.axisTitleX.text">Time</option>
        <option name="charting.axisTitleY.text">Decision Count</option>
        <option name="charting.legend.placement">bottom</option>
        <option name="charting.seriesColors">[0x65A637,0xD93F3C,0xF7BC38]</option>
      </chart>
    </panel>
    <panel>
      <title>Top Denied Policies</title>
      <chart>
        <search>
          <query>
            index=main sourcetype="opa:decision" decision_result="deny" 
            | stats count by policy_path
            | sort -count
            | head 10
          </query>
          <earliest>-24h@h</earliest>
          <latest>now</latest>
          <refresh>5m</refresh>
        </search>
        <option name="charting.chart">bar</option>
        <option name="charting.axisTitleX.text">Policy Path</option>
        <option name="charting.axisTitleY.text">Denial Count</option>
        <option name="charting.legend.placement">none</option>
        <option name="charting.seriesColors">[0xD93F3C]</option>
      </chart>
    </panel>
  </row>
  
  <row>
    <panel>
      <title>Compliance Framework Status</title>
      <chart>
        <search>
          <query>
            index=main sourcetype="opa:decision" 
            | lookup policy_criticality policy_path OUTPUT compliance_framework
            | where isnotnull(compliance_framework)
            | stats count(eval(decision_result="allow")) as compliant,
                   count(eval(decision_result="deny")) as violations
                   by compliance_framework
            | eval total=compliant+violations
            | eval compliance_rate=round((compliant/total)*100,2)
            | fields compliance_framework, compliance_rate, violations
          </query>
          <earliest>-24h@h</earliest>
          <latest>now</latest>
          <refresh>5m</refresh>
        </search>
        <option name="charting.chart">column</option>
        <option name="charting.axisTitleX.text">Compliance Framework</option>
        <option name="charting.axisTitleY.text">Compliance Rate %</option>
        <option name="charting.legend.placement">bottom</option>
        <option name="charting.seriesColors">[0x65A637,0xD93F3C]</option>
      </chart>
    </panel>
    <panel>
      <title>User Risk Analysis</title>
      <chart>
        <search>
          <query>
            index=main sourcetype="opa:decision" 
            | lookup user_info input_user OUTPUT risk_score as user_risk_score
            | where isnotnull(user_risk_score)
            | eval risk_category=case(
                user_risk_score >= 8, "High Risk",
                user_risk_score >= 5, "Medium Risk",
                1=1, "Low Risk"
              )
            | stats count(eval(decision_result="deny")) as denials by risk_category
            | sort -denials
          </query>
          <earliest>-24h@h</earliest>
          <latest>now</latest>
          <refresh>5m</refresh>
        </search>
        <option name="charting.chart">pie</option>
        <option name="charting.legend.placement">right</option>
        <option name="charting.seriesColors">[0xD93F3C,0xF7BC38,0x65A637]</option>
      </chart>
    </panel>
  </row>
  
  <row>
    <panel>
      <title>Geographic Access Patterns</title>
      <map>
        <search>
          <query>
            index=main sourcetype="opa:decision" 
            | iplocation input_remote_addr
            | where isnotnull(lat) AND isnotnull(lon)
            | stats count by Country, lat, lon
            | geostats latfield=lat longfield=lon count
          </query>
          <earliest>-24h@h</earliest>
          <latest>now</latest>
          <refresh>5m</refresh>
        </search>
        <option name="mapping.type">marker</option>
        <option name="mapping.markerSize">medium</option>
        <option name="mapping.showTiles">1</option>
      </map>
    </panel>
    <panel>
      <title>Performance Metrics</title>
      <chart>
        <search>
          <query>
            index=main sourcetype="opa:decision" 
            | eval response_time_ms=decision_evaluation_duration_ns/1000000
            | timechart span=5m avg(response_time_ms) as avg_response_time,
                                 perc95(response_time_ms) as p95_response_time
          </query>
          <earliest>-4h@h</earliest>
          <latest>now</latest>
          <refresh>1m</refresh>
        </search>
        <option name="charting.chart">line</option>
        <option name="charting.axisTitleX.text">Time</option>
        <option name="charting.axisTitleY.text">Response Time (ms)</option>
        <option name="charting.legend.placement">bottom</option>
        <option name="charting.seriesColors">[0x6DB7C6,0xF7BC38]</option>
      </chart>
    </panel>
  </row>
  
  <row>
    <panel>
      <title>Recent High-Risk Policy Violations</title>
      <table>
        <search>
          <query>
            index=main sourcetype="opa:decision" decision_result="deny" 
            | lookup policy_criticality policy_path OUTPUT risk_score, compliance_framework
            | lookup user_info input_user OUTPUT department, role
            | where risk_score >= 7
            | eval timestamp=strftime(_time, "%Y-%m-%d %H:%M:%S")
            | table timestamp, input_user, department, role, policy_path, 
                   compliance_framework, risk_score, input_remote_addr
            | sort -_time
            | head 20
          </query>
          <earliest>-24h@h</earliest>
          <latest>now</latest>
          <refresh>1m</refresh>
        </search>
        <option name="drilldown">cell</option>
        <option name="refresh.display">progressbar</option>
        <format type="color" field="risk_score">
          <colorPalette type="list">[#65A637,#F7BC38,#D93F3C]</colorPalette>
          <scale type="threshold">5,8</scale>
        </format>
      </table>
    </panel>
  </row>
  
  <row>
    <panel>
      <title>Styra DAS Policy Changes</title>
      <table>
        <search>
          <query>
            index=main sourcetype="styra:audit" 
            | eval timestamp=strftime(_time, "%Y-%m-%d %H:%M:%S")
            | table timestamp, user_email, change_type, policy_path, 
                   system_name, severity, description
            | sort -_time
            | head 15
          </query>
          <earliest>-24h@h</earliest>
          <latest>now</latest>
          <refresh>2m</refresh>
        </search>
        <option name="drilldown">cell</option>
        <option name="refresh.display">progressbar</option>
        <format type="color" field="severity">
          <colorPalette type="map">{"low":#65A637,"medium":#F7BC38,"high":#D93F3C,"critical":#8B0000}</colorPalette>
        </format>
      </table>
    </panel>
  </row>
  
  <row>
    <panel>
      <title>OPA Instance Status</title>
      <table>
        <search>
          <query>
            index=main sourcetype="opa:health" 
            | stats latest(status) as current_status,
                   latest(checks.basic_health.response_time_ms) as response_time,
                   latest(checks.bundle_status.bundle_count) as bundle_count,
                   latest(endpoint_state.consecutive_failures) as failures
                   by endpoint
            | eval health_indicator=case(
                current_status="healthy", "🟢 Healthy",
                current_status="degraded", "🟡 Degraded", 
                current_status="unhealthy", "🔴 Unhealthy",
                1=1, "⚪ Unknown"
              )
            | table endpoint, health_indicator, response_time, bundle_count, failures
            | sort endpoint
          </query>
          <earliest>-5m@m</earliest>
          <latest>now</latest>
          <refresh>30s</refresh>
        </search>
        <option name="drilldown">cell</option>
        <option name="refresh.display">progressbar</option>
      </table>
    </panel>
  </row>
</dashboard>