<dashboard version="1.1">
  <label>OPA Security &amp; Threat Detection</label>
  <description>Advanced security monitoring and threat detection based on OPA policy violations and access patterns</description>
  
  <row>
    <panel>
      <title>Security Alert Level</title>
      <single>
        <search>
          <query>
            index=main sourcetype="opa:decision" 
            | lookup policy_criticality policy_path OUTPUT risk_score
            | lookup user_info input_user OUTPUT risk_score as user_risk_score
            | eval combined_risk=coalesce(risk_score,0) + coalesce(user_risk_score,0)
            | eval alert_level=case(
                combined_risk >= 15, "CRITICAL",
                combined_risk >= 10, "HIGH", 
                combined_risk >= 5, "MEDIUM",
                1=1, "LOW"
              )
            | where decision_result="deny" AND alert_level IN ("CRITICAL", "HIGH")
            | stats count
          </query>
          <earliest>-1h@h</earliest>
          <latest>now</latest>
          <refresh>30s</refresh>
        </search>
        <option name="drilldown">none</option>
        <option name="colorBy">value</option>
        <option name="colorMode">block</option>
        <option name="rangeColors">["0x65A637","0xF7BC38","0xD93F3C","0x8B0000"]</option>
        <option name="rangeValues">[0,5,20]</option>
        <option name="underLabel">High-Risk Violations (1h)</option>
      </single>
    </panel>
    <panel>
      <title>Anomalous Access Attempts</title>
      <single>
        <search>
          <query>
            index=main sourcetype="opa:decision" decision_result="deny"
            | bucket _time span=5m
            | stats count by _time, input_user
            | eventstats avg(count) as avg_denials, stdev(count) as stdev_denials by input_user
            | eval threshold=avg_denials + (2 * stdev_denials)
            | where count > threshold
            | stats dc(input_user) as anomalous_users
          </query>
          <earliest>-4h@h</earliest>
          <latest>now</latest>
          <refresh>1m</refresh>
        </search>
        <option name="drilldown">none</option>
        <option name="colorBy">value</option>
        <option name="colorMode">block</option>
        <option name="rangeColors">["0x65A637","0xF7BC38","0xD93F3C"]</option>
        <option name="rangeValues">[0,3]</option>
        <option name="underLabel">Users with Anomalous Activity</option>
      </single>
    </panel>
    <panel>
      <title>Geographic Risk Score</title>
      <single>
        <search>
          <query>
            index=main sourcetype="opa:decision" decision_result="deny"
            | iplocation input_remote_addr
            | lookup geographic_risk Country OUTPUT risk_multiplier
            | eval geo_risk=coalesce(risk_multiplier, 1)
            | where geo_risk > 2
            | stats count
          </query>
          <earliest>-24h@h</earliest>
          <latest>now</latest>
          <refresh>5m</refresh>
        </search>
        <option name="drilldown">none</option>
        <option name="colorBy">value</option>
        <option name="colorMode">block</option>
        <option name="rangeColors">["0x65A637","0xF7BC38","0xD93F3C"]</option>
        <option name="rangeValues">[0,10]</option>
        <option name="underLabel">High-Risk Geographic Access</option>
      </single>
    </panel>
    <panel>
      <title>Privilege Escalation Attempts</title>
      <single>
        <search>
          <query>
            index=main sourcetype="opa:decision" decision_result="deny"
            | rex field=policy_path "(?&lt;privilege_level&gt;admin|root|sudo|elevated)"
            | where isnotnull(privilege_level)
            | lookup user_info input_user OUTPUT clearance_level
            | where clearance_level != "high"
            | stats count
          </query>
          <earliest>-24h@h</earliest>
          <latest>now</latest>
          <refresh>2m</refresh>
        </search>
        <option name="drilldown">none</option>
        <option name="colorBy">value</option>
        <option name="colorMode">block</option>
        <option name="rangeColors">["0x65A637","0xF7BC38","0xD93F3C"]</option>
        <option name="rangeValues">[0,5]</option>
        <option name="underLabel">Privilege Escalation Attempts</option>
      </single>
    </panel>
  </row>
  
  <row>
    <panel>
      <title>Real-Time Security Events</title>
      <chart>
        <search>
          <query>
            index=main sourcetype="opa:decision" decision_result="deny"
            | lookup policy_criticality policy_path OUTPUT risk_score
            | eval security_level=case(
                risk_score >= 8, "Critical",
                risk_score >= 6, "High",
                risk_score >= 4, "Medium",
                1=1, "Low"
              )
            | timechart span=1m count by security_level
          </query>
          <earliest>-1h@h</earliest>
          <latest>now</latest>
          <refresh>30s</refresh>
        </search>
        <option name="charting.chart">area</option>
        <option name="charting.axisTitleX.text">Time</option>
        <option name="charting.axisTitleY.text">Security Events</option>
        <option name="charting.legend.placement">bottom</option>
        <option name="charting.seriesColors">[0x8B0000,0xD93F3C,0xF7BC38,0x65A637]</option>
        <option name="charting.chart.stackMode">stacked</option>
      </chart>
    </panel>
    <panel>
      <title>Attack Pattern Analysis</title>
      <chart>
        <search>
          <query>
            index=main sourcetype="opa:decision" decision_result="deny"
            | eval attack_pattern=case(
                match(policy_path, "(?i)auth|login|credential"), "Authentication Bypass",
                match(policy_path, "(?i)admin|root|sudo"), "Privilege Escalation",
                match(policy_path, "(?i)data|file|resource"), "Data Access",
                match(policy_path, "(?i)network|api|service"), "Service Access",
                1=1, "Other"
              )
            | timechart span=10m count by attack_pattern
          </query>
          <earliest>-4h@h</earliest>
          <latest>now</latest>
          <refresh>1m</refresh>
        </search>
        <option name="charting.chart">line</option>
        <option name="charting.axisTitleX.text">Time</option>
        <option name="charting.axisTitleY.text">Attack Attempts</option>
        <option name="charting.legend.placement">bottom</option>
        <option name="charting.seriesColors">[0xD93F3C,0xF7BC38,0x6DB7C6,0x9932CC,0x808080]</option>
      </chart>
    </panel>
  </row>
  
  <row>
    <panel>
      <title>Threat Actor Profiling</title>
      <table>
        <search>
          <query>
            index=main sourcetype="opa:decision" decision_result="deny"
            | lookup policy_criticality policy_path OUTPUT risk_score
            | lookup user_info input_user OUTPUT department, role, risk_score as user_risk_score
            | eval threat_score=(coalesce(risk_score,0) + coalesce(user_risk_score,0))
            | stats count as violation_count,
                   avg(threat_score) as avg_threat_score,
                   dc(policy_path) as unique_policies,
                   dc(input_remote_addr) as unique_ips,
                   values(department) as departments,
                   latest(role) as role
                   by input_user
            | eval risk_profile=case(
                avg_threat_score >= 12, "🔴 Critical Threat",
                avg_threat_score >= 8, "🟠 High Risk",
                avg_threat_score >= 5, "🟡 Medium Risk",
                1=1, "🟢 Low Risk"
              )
            | where violation_count >= 5
            | sort -avg_threat_score
            | head 15
            | table input_user, risk_profile, violation_count, unique_policies, 
                   unique_ips, departments, role
          </query>
          <earliest>-24h@h</earliest>
          <latest>now</latest>
          <refresh>2m</refresh>
        </search>
        <option name="drilldown">cell</option>
        <option name="refresh.display">progressbar</option>
        <format type="color" field="violation_count">
          <colorPalette type="list">[#65A637,#F7BC38,#D93F3C]</colorPalette>
          <scale type="threshold">10,25</scale>
        </format>
      </table>
    </panel>
  </row>
  
  <row>
    <panel>
      <title>Suspicious IP Analysis</title>
      <table>
        <search>
          <query>
            index=main sourcetype="opa:decision" decision_result="deny"
            | iplocation input_remote_addr
            | lookup policy_criticality policy_path OUTPUT risk_score
            | stats count as violation_count,
                   dc(input_user) as unique_users,
                   dc(policy_path) as unique_policies,
                   avg(risk_score) as avg_risk_score,
                   values(Country) as countries,
                   values(City) as cities
                   by input_remote_addr
            | eval suspicion_score=case(
                violation_count >= 50, 10,
                violation_count >= 20, 8,
                violation_count >= 10, 6,
                unique_users >= 5, 7,
                unique_policies >= 10, 5,
                1=1, 3
              )
            | eval threat_level=case(
                suspicion_score >= 8, "🔴 High Threat",
                suspicion_score >= 6, "🟠 Medium Threat",
                suspicion_score >= 4, "🟡 Low Threat",
                1=1, "🟢 Minimal"
              )
            | where violation_count >= 5
            | sort -suspicion_score
            | head 15
            | table input_remote_addr, threat_level, violation_count, unique_users,
                   unique_policies, countries, cities
          </query>
          <earliest>-24h@h</earliest>
          <latest>now</latest>
          <refresh>2m</refresh>
        </search>
        <option name="drilldown">cell</option>
        <option name="refresh.display">progressbar</option>
        <format type="color" field="violation_count">
          <colorPalette type="list">[#65A637,#F7BC38,#D93F3C]</colorPalette>
          <scale type="threshold">15,40</scale>
        </format>
      </table>
    </panel>
  </row>
  
  <row>
    <panel>
      <title>Time-based Attack Patterns</title>
      <chart>
        <search>
          <query>
            index=main sourcetype="opa:decision" decision_result="deny"
            | eval hour=strftime(_time, "%H")
            | eval time_category=case(
                hour >= 22 OR hour <= 6, "Night (22:00-06:00)",
                hour >= 7 AND hour <= 9, "Morning (07:00-09:00)",
                hour >= 10 AND hour <= 17, "Business Hours (10:00-17:00)",
                hour >= 18 AND hour <= 21, "Evening (18:00-21:00)"
              )
            | stats count by time_category
            | sort -count
          </query>
          <earliest>-7d@d</earliest>
          <latest>now</latest>
          <refresh>5m</refresh>
        </search>
        <option name="charting.chart">pie</option>
        <option name="charting.legend.placement">right</option>
        <option name="charting.seriesColors">[0x2F4F4F,0x4682B4,0x32CD32,0xFF6347]</option>
      </chart>
    </panel>
    <panel>
      <title>Critical Policy Violations by Department</title>
      <chart>
        <search>
          <query>
            index=main sourcetype="opa:decision" decision_result="deny"
            | lookup policy_criticality policy_path OUTPUT risk_score
            | lookup user_info input_user OUTPUT department
            | where risk_score >= 7
            | stats count by department
            | sort -count
            | head 10
          </query>
          <earliest>-24h@h</earliest>
          <latest>now</latest>
          <refresh>5m</refresh>
        </search>
        <option name="charting.chart">bar</option>
        <option name="charting.axisTitleX.text">Department</option>
        <option name="charting.axisTitleY.text">Critical Violations</option>
        <option name="charting.legend.placement">none</option>
        <option name="charting.seriesColors">[0xD93F3C]</option>
      </chart>
    </panel>
  </row>
  
  <row>
    <panel>
      <title>Security Incident Timeline</title>
      <table>
        <search>
          <query>
            index=main sourcetype="opa:decision" decision_result="deny"
            | lookup policy_criticality policy_path OUTPUT risk_score, compliance_framework
            | lookup user_info input_user OUTPUT department, role, risk_score as user_risk_score
            | eval incident_severity=case(
                risk_score >= 9, "CRITICAL",
                risk_score >= 7, "HIGH",
                risk_score >= 5, "MEDIUM",
                1=1, "LOW"
              )
            | where incident_severity IN ("CRITICAL", "HIGH")
            | eval timestamp=strftime(_time, "%Y-%m-%d %H:%M:%S")
            | eval incident_id=substr(md5(input_user.policy_path._time), 1, 8)
            | table timestamp, incident_id, incident_severity, input_user, department,
                   policy_path, compliance_framework, input_remote_addr
            | sort -_time
            | head 25
          </query>
          <earliest>-24h@h</earliest>
          <latest>now</latest>
          <refresh>1m</refresh>
        </search>
        <option name="drilldown">cell</option>
        <option name="refresh.display">progressbar</option>
        <format type="color" field="incident_severity">
          <colorPalette type="map">{"LOW":#65A637,"MEDIUM":#F7BC38,"HIGH":#D93F3C,"CRITICAL":#8B0000}</colorPalette>
        </format>
      </table>
    </panel>
  </row>
  
  <row>
    <panel>
      <title>Compliance Violations by Framework</title>
      <chart>
        <search>
          <query>
            index=main sourcetype="opa:decision" decision_result="deny"
            | lookup policy_criticality policy_path OUTPUT compliance_framework, risk_score
            | where isnotnull(compliance_framework) AND risk_score >= 6
            | stats count by compliance_framework
            | sort -count
          </query>
          <earliest>-7d@d</earliest>
          <latest>now</latest>
          <refresh>10m</refresh>
        </search>
        <option name="charting.chart">column</option>
        <option name="charting.axisTitleX.text">Compliance Framework</option>
        <option name="charting.axisTitleY.text">High-Risk Violations</option>
        <option name="charting.legend.placement">none</option>
        <option name="charting.seriesColors">[0xD93F3C]</option>
      </chart>
    </panel>
    <panel>
      <title>User Behavior Anomalies</title>
      <chart>
        <search>
          <query>
            index=main sourcetype="opa:decision"
            | bucket _time span=1h
            | stats count as hourly_requests by _time, input_user
            | eventstats avg(hourly_requests) as avg_requests, 
                       stdev(hourly_requests) as stdev_requests by input_user
            | eval anomaly_score=abs(hourly_requests - avg_requests) / stdev_requests
            | where anomaly_score > 2
            | timechart span=1h avg(anomaly_score) as avg_anomaly_score
          </query>
          <earliest>-24h@h</earliest>
          <latest>now</latest>
          <refresh>5m</refresh>
        </search>
        <option name="charting.chart">line</option>
        <option name="charting.axisTitleX.text">Time</option>
        <option name="charting.axisTitleY.text">Anomaly Score</option>
        <option name="charting.legend.placement">bottom</option>
        <option name="charting.seriesColors">[0xFF4500]</option>
      </chart>
    </panel>
  </row>
</dashboard>