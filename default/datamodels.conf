# OPA Policy Audit & Compliance Add-on - Data Models Configuration

# Authentication Data Model for OPA Policy Decisions
[Authentication_OPA]
acceleration = 1
acceleration.earliest_time = -1y
acceleration.max_time = 0

[Authentication_OPA.Authentication]
search = sourcetype="opa:decision" | eval action=if(result="true", "success", "failure") | eval app=coalesce(service_name, "opa") | eval dest=coalesce(opa_endpoint, "unknown") | eval src=coalesce(client_ip, requested_by) | eval user=coalesce(input_user, "unknown") | eval signature=policy_name | eval signature_id=decision_id

# Change Analysis Data Model for Policy Changes
[Change_Analysis_OPA]
acceleration = 1
acceleration.earliest_time = -1y
acceleration.max_time = 0

[Change_Analysis_OPA.All_Changes]
search = (sourcetype="styra:das:policy:audit" OR sourcetype="opa:decision") | eval change_type=case(sourcetype="styra:das:policy:audit", action, sourcetype="opa:decision" AND result="false", "policy_violation", 1=1, "unknown") | eval object=case(sourcetype="styra:das:policy:audit", object_name, sourcetype="opa:decision", path, 1=1, "unknown") | eval src_user=case(sourcetype="styra:das:policy:audit", user, sourcetype="opa:decision", input_user, 1=1, "unknown") | eval vendor_action=case(sourcetype="styra:das:policy:audit", action, sourcetype="opa:decision", "policy_evaluation", 1=1, "unknown")

[Change_Analysis_OPA.Policy_Changes]
search = sourcetype="styra:das:policy:audit" | eval change_type=action | eval object=object_name | eval src_user=user | eval vendor_action=action | eval result=if(status="success", "success", "failure") | eval command=action | eval dest=system_id

# Application State Data Model for Policy Evaluations
[Application_State_OPA]
acceleration = 1
acceleration.earliest_time = -1y
acceleration.max_time = 0

[Application_State_OPA.All_Application_State]
search = sourcetype="opa:decision" | eval dest=coalesce(opa_endpoint, "opa") | eval src=coalesce(client_ip, requested_by) | eval user=input_user | eval app=coalesce(service_name, "opa") | eval version=policy_version

# Alerts Data Model for Policy Violations
[Alerts_OPA]
acceleration = 1
acceleration.earliest_time = -1y
acceleration.max_time = 0

[Alerts_OPA.Alerts]
search = sourcetype="opa:decision" result="false" | eval action="denied" | eval dest=coalesce(opa_endpoint, "opa") | eval src=coalesce(client_ip, requested_by) | eval user=input_user | eval signature=policy_name | eval signature_id=decision_id | eval severity=case(policy_criticality="high", "high", policy_criticality="medium", "medium", 1=1, "low") | eval category="Policy Violation"

# Performance Data Model for OPA Metrics
[Performance_OPA]
acceleration = 1
acceleration.earliest_time = -1y
acceleration.max_time = 0

[Performance_OPA.Performance]
search = (sourcetype="opa:decision" OR sourcetype="opa:metrics" OR sourcetype="opa:health") | eval dest=coalesce(opa_endpoint, "opa") | eval response_time=case(sourcetype="opa:decision", query_duration_ms, sourcetype="opa:health", response_time_ms, 1=1, 0) | eval cpu_load_percent=0 | eval mem_used_percent=0

# Network Traffic Data Model for API Calls
[Network_Traffic_OPA]
acceleration = 1
acceleration.earliest_time = -1y
acceleration.max_time = 0

[Network_Traffic_OPA.All_Traffic]
search = sourcetype="opa:decision" | eval action=input_method | eval bytes_in=0 | eval bytes_out=0 | eval dest=coalesce(opa_endpoint, "opa") | eval dest_port=8181 | eval protocol="http" | eval src=coalesce(client_ip, requested_by) | eval transport="tcp" | eval url=input_path

# Vulnerability Data Model for Policy Weaknesses
[Vulnerabilities_OPA]
acceleration = 1
acceleration.earliest_time = -1y
acceleration.max_time = 0

[Vulnerabilities_OPA.Vulnerabilities]
search = sourcetype="opa:decision" result="false" | eval category="Policy Gap" | eval cve="N/A" | eval dest=coalesce(opa_endpoint, "opa") | eval severity=case(policy_criticality="high", "critical", policy_criticality="medium", "high", 1=1, "medium") | eval signature=policy_name | eval src=coalesce(client_ip, requested_by) | eval user=input_user

# Risk Data Model for Risk Assessment
[Risk_OPA]
acceleration = 1
acceleration.earliest_time = -1y
acceleration.max_time = 0

[Risk_OPA.Risk_Incidents]
search = (sourcetype="opa:decision" result="false") OR (sourcetype="styra:das:policy:audit" status!="success") | eval risk_score=case(sourcetype="opa:decision" AND policy_criticality="high", 90, sourcetype="opa:decision" AND policy_criticality="medium", 60, sourcetype="opa:decision", 30, sourcetype="styra:das:policy:audit", 50, 1=1, 10) | eval risk_object=case(sourcetype="opa:decision", path, sourcetype="styra:das:policy:audit", object_name, 1=1, "unknown") | eval risk_object_type="policy" | eval user=case(sourcetype="opa:decision", input_user, sourcetype="styra:das:policy:audit", user, 1=1, "unknown")

# Compliance Data Model for Regulatory Compliance
[Compliance_OPA]
acceleration = 1
acceleration.earliest_time = -1y
acceleration.max_time = 0

[Compliance_OPA.Compliance_Events]
search = (sourcetype="opa:decision" OR sourcetype="styra:das:policy:audit") | eval compliance_framework=case(match(path, "(?i)pci"), "PCI-DSS", match(path, "(?i)sox"), "SOX", match(path, "(?i)hipaa"), "HIPAA", match(path, "(?i)gdpr"), "GDPR", 1=1, "General") | eval compliance_status=case(sourcetype="opa:decision" AND result="true", "compliant", sourcetype="opa:decision" AND result="false", "non-compliant", sourcetype="styra:das:policy:audit" AND status="success", "compliant", 1=1, "unknown") | eval control_id=policy_name | eval user=case(sourcetype="opa:decision", input_user, sourcetype="styra:das:policy:audit", user, 1=1, "unknown")

# Audit Data Model for Audit Trail
[Audit_OPA]
acceleration = 1
acceleration.earliest_time = -1y
acceleration.max_time = 0

[Audit_OPA.Audit_Events]
search = (sourcetype="opa:decision" OR sourcetype="styra:das:policy:audit") | eval action=case(sourcetype="opa:decision", "policy_evaluation", sourcetype="styra:das:policy:audit", action, 1=1, "unknown") | eval object=case(sourcetype="opa:decision", path, sourcetype="styra:das:policy:audit", object_name, 1=1, "unknown") | eval result=case(sourcetype="opa:decision" AND result="true", "success", sourcetype="opa:decision" AND result="false", "failure", sourcetype="styra:das:policy:audit", status, 1=1, "unknown") | eval user=case(sourcetype="opa:decision", input_user, sourcetype="styra:das:policy:audit", user, 1=1, "unknown") | eval vendor_action=action

# Identity Management Data Model
[Identity_Management_OPA]
acceleration = 1
acceleration.earliest_time = -1y
acceleration.max_time = 0

[Identity_Management_OPA.All_Identity_Management]
search = sourcetype="opa:decision" | eval action=case(result="true", "granted", result="false", "denied", 1=1, "unknown") | eval app=coalesce(service_name, "opa") | eval dest=coalesce(opa_endpoint, "opa") | eval src=coalesce(client_ip, requested_by) | eval user=input_user | eval signature=policy_name

# Asset and Identity Data Model
[Asset_And_Identity_OPA]
acceleration = 1
acceleration.earliest_time = -1y
acceleration.max_time = 0

[Asset_And_Identity_OPA.Assets]
search = sourcetype="opa:decision" | eval asset=coalesce(opa_endpoint, "opa") | eval asset_type="policy_engine" | eval category="Infrastructure" | eval owner="Security Team" | eval priority=case(policy_criticality="high", "critical", policy_criticality="medium", "high", 1=1, "medium")

[Asset_And_Identity_OPA.Identities]
search = sourcetype="opa:decision" | eval identity=input_user | eval identity_type="user" | eval category="Account" | eval priority=case(user_risk_score>80, "critical", user_risk_score>60, "high", user_risk_score>40, "medium", 1=1, "low")