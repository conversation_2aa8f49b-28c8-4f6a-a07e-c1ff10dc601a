# OPA Policy Audit & Compliance Add-on - Event Types Configuration
# This file defines event type classifications for CIM compliance

# ============================================================================
# AUTHENTICATION EVENT TYPES
# ============================================================================

[opa_authentication_success]
search = sourcetype="opa:decision" decision_result="allow" (policy_path="*auth*" OR policy_path="*login*" OR policy_path="*authentication*")
priority = 5
description = Successful authentication events through OPA policies

[opa_authentication_failure]
search = sourcetype="opa:decision" decision_result="deny" (policy_path="*auth*" OR policy_path="*login*" OR policy_path="*authentication*")
priority = 5
description = Failed authentication events through OPA policies

[opa_privileged_authentication]
search = sourcetype="opa:decision" (policy_path="*admin*" OR policy_path="*root*" OR policy_path="*sudo*" OR policy_path="*privileged*" OR policy_path="*superuser*")
priority = 6
description = Authentication events for privileged access

# ============================================================================
# CHANGE ANALYSIS EVENT TYPES
# ============================================================================

[styra_policy_change]
search = sourcetype="styra:audit" change_type IN ("create", "modify", "delete")
priority = 5
description = Policy changes in Styra DAS

[styra_policy_creation]
search = sourcetype="styra:audit" change_type="create"
priority = 5
description = Policy creation events in Styra DAS

[styra_policy_modification]
search = sourcetype="styra:audit" change_type="modify"
priority = 5
description = Policy modification events in Styra DAS

[styra_policy_deletion]
search = sourcetype="styra:audit" change_type="delete"
priority = 6
description = Policy deletion events in Styra DAS

[opa_bundle_change]
search = sourcetype="opa:health" checks.bundle_status.status="*" checks.bundle_status.last_successful_download!=checks.bundle_status.last_successful_activation
priority = 5
description = OPA bundle configuration changes

# ============================================================================
# APPLICATION STATE EVENT TYPES
# ============================================================================

[opa_health_status]
search = sourcetype="opa:health"
priority = 3
description = OPA instance health status events

[opa_performance_metrics]
search = sourcetype="opa:metrics" OR (sourcetype="opa:decision" decision_evaluation_duration_ns>0)
priority = 3
description = OPA performance and metrics events

[opa_bundle_status]
search = sourcetype="opa:health" checks.bundle_status.status="*"
priority = 4
description = OPA bundle status events

# ============================================================================
# ALERT EVENT TYPES
# ============================================================================

[opa_critical_violation]
search = sourcetype="opa:decision" decision_result="deny" | lookup policy_criticality policy_path OUTPUT risk_score | where risk_score >= 9
priority = 8
description = Critical policy violations requiring immediate attention

[opa_high_risk_denial]
search = sourcetype="opa:decision" decision_result="deny" | lookup policy_criticality policy_path OUTPUT risk_score | where risk_score >= 7
priority = 7
description = High-risk policy denials

[opa_compliance_violation]
search = sourcetype="opa:decision" decision_result="deny" | lookup policy_criticality policy_path OUTPUT compliance_framework | where isnotnull(compliance_framework)
priority = 6
description = Compliance framework violations

[opa_performance_alert]
search = (sourcetype="opa:decision" decision_evaluation_duration_ns > **********) OR (sourcetype="opa:health" status="unhealthy")
priority = 5
description = Performance-related alerts

[opa_security_incident]
search = sourcetype="opa:decision" decision_result="deny" | lookup user_info input_user OUTPUT risk_score as user_risk_score | where user_risk_score >= 8
priority = 7
description = Security incidents involving high-risk users

# ============================================================================
# PERFORMANCE EVENT TYPES
# ============================================================================

[opa_slow_response]
search = sourcetype="opa:decision" decision_evaluation_duration_ns > 500000000
priority = 4
description = Slow OPA policy evaluation responses

[opa_high_volume]
search = sourcetype="opa:decision" | bucket _time span=1m | stats count by _time, policy_path | where count > 100
priority = 4
description = High volume policy evaluations

[opa_resource_utilization]
search = sourcetype="opa:metrics" (memory_usage>0 OR cpu_usage>0)
priority = 3
description = OPA resource utilization metrics

# ============================================================================
# NETWORK TRAFFIC EVENT TYPES
# ============================================================================

[opa_network_access]
search = sourcetype="opa:decision" input_remote_addr="*"
priority = 3
description = Network access through OPA policies

[opa_remote_access]
search = sourcetype="opa:decision" input_remote_addr!="127.0.0.1" input_remote_addr!="localhost" input_remote_addr!="::1"
priority = 4
description = Remote access attempts through OPA

[opa_geographic_access]
search = sourcetype="opa:decision" input_remote_addr="*" | iplocation input_remote_addr | where isnotnull(Country)
priority = 4
description = Geographic access patterns

# ============================================================================
# VULNERABILITY EVENT TYPES
# ============================================================================

[opa_privilege_escalation]
search = sourcetype="opa:decision" decision_result="deny" (policy_path="*admin*" OR policy_path="*root*" OR policy_path="*sudo*" OR policy_path="*elevated*" OR policy_path="*superuser*")
priority = 7
description = Privilege escalation attempts

[opa_unauthorized_access]
search = sourcetype="opa:decision" decision_result="deny" | lookup user_info input_user OUTPUT clearance_level | where clearance_level IN ("low", "medium")
priority = 6
description = Unauthorized access attempts by low-clearance users

[opa_data_exposure_risk]
search = sourcetype="opa:decision" (policy_path="*data*" OR policy_path="*file*" OR policy_path="*document*" OR policy_path="*export*" OR policy_path="*download*")
priority = 5
description = Potential data exposure risks

# ============================================================================
# RISK EVENT TYPES
# ============================================================================

[opa_high_risk_activity]
search = sourcetype="opa:decision" | lookup policy_criticality policy_path OUTPUT risk_score | lookup user_info input_user OUTPUT risk_score as user_risk_score | eval combined_risk=coalesce(risk_score,0)+coalesce(user_risk_score,0) | where combined_risk >= 12
priority = 6
description = High-risk activities based on combined risk scoring

[opa_critical_risk_activity]
search = sourcetype="opa:decision" | lookup policy_criticality policy_path OUTPUT risk_score | lookup user_info input_user OUTPUT risk_score as user_risk_score | eval combined_risk=coalesce(risk_score,0)+coalesce(user_risk_score,0) | where combined_risk >= 16
priority = 8
description = Critical risk activities requiring immediate attention

[opa_user_risk_activity]
search = sourcetype="opa:decision" | lookup user_info input_user OUTPUT risk_score as user_risk_score | where user_risk_score >= 7
priority = 5
description = Activities by high-risk users

[opa_geographic_risk]
search = sourcetype="opa:decision" input_remote_addr="*" | iplocation input_remote_addr | lookup geographic_risk Country OUTPUT risk_multiplier | where risk_multiplier >= 3
priority = 6
description = Access from high-risk geographic locations

[opa_policy_risk]
search = sourcetype="opa:decision" | lookup policy_criticality policy_path OUTPUT risk_score | where risk_score >= 8
priority = 5
description = High-risk policy evaluations

# ============================================================================
# COMPLIANCE EVENT TYPES
# ============================================================================

[opa_sox_compliance]
search = sourcetype="opa:decision" | lookup policy_criticality policy_path OUTPUT compliance_framework | where compliance_framework="SOX"
priority = 5
description = SOX compliance-related policy evaluations

[opa_pci_compliance]
search = sourcetype="opa:decision" | lookup policy_criticality policy_path OUTPUT compliance_framework | where compliance_framework="PCI-DSS"
priority = 5
description = PCI-DSS compliance-related policy evaluations

[opa_hipaa_compliance]
search = sourcetype="opa:decision" | lookup policy_criticality policy_path OUTPUT compliance_framework | where compliance_framework="HIPAA"
priority = 5
description = HIPAA compliance-related policy evaluations

[opa_gdpr_compliance]
search = sourcetype="opa:decision" | lookup policy_criticality policy_path OUTPUT compliance_framework | where compliance_framework="GDPR"
priority = 5
description = GDPR compliance-related policy evaluations

[opa_iso27001_compliance]
search = sourcetype="opa:decision" | lookup policy_criticality policy_path OUTPUT compliance_framework | where compliance_framework="ISO 27001"
priority = 5
description = ISO 27001 compliance-related policy evaluations

[opa_nist_compliance]
search = sourcetype="opa:decision" | lookup policy_criticality policy_path OUTPUT compliance_framework | where compliance_framework="NIST"
priority = 5
description = NIST compliance-related policy evaluations

# ============================================================================
# AUDIT EVENT TYPES
# ============================================================================

[opa_audit_event]
search = sourcetype="opa:decision" OR sourcetype="styra:audit"
priority = 3
description = All OPA and Styra audit events

[styra_audit_event]
search = sourcetype="styra:audit"
priority = 4
description = Styra DAS audit events

[opa_access_audit]
search = sourcetype="opa:decision"
priority = 3
description = OPA access audit events

[opa_policy_audit]
search = sourcetype="opa:decision" OR (sourcetype="styra:audit" change_type IN ("create", "modify", "delete"))
priority = 4
description = Policy-related audit events

[opa_compliance_audit]
search = sourcetype="opa:decision" | lookup policy_criticality policy_path OUTPUT compliance_framework | where isnotnull(compliance_framework)
priority = 5
description = Compliance audit events

# ============================================================================
# IDENTITY MANAGEMENT EVENT TYPES
# ============================================================================

[opa_user_activity]
search = sourcetype="opa:decision" input_user="*"
priority = 3
description = User activity through OPA policies

[opa_privileged_user_activity]
search = sourcetype="opa:decision" | lookup user_info input_user OUTPUT clearance_level | where clearance_level="high"
priority = 5
description = Privileged user activity

[opa_service_account_activity]
search = sourcetype="opa:decision" (input_user="*service*" OR input_user="*svc*" OR input_user="*system*" OR input_user="*app*")
priority = 4
description = Service account activity

[opa_role_based_access]
search = sourcetype="opa:decision" | lookup user_info input_user OUTPUT role | where isnotnull(role)
priority = 4
description = Role-based access control events

# ============================================================================
# ASSET AND IDENTITY EVENT TYPES
# ============================================================================

[opa_asset_access]
search = sourcetype="opa:decision" (policy_path="*asset*" OR policy_path="*resource*" OR policy_path="*system*")
priority = 4
description = Asset access events

[opa_identity_verification]
search = sourcetype="opa:decision" (policy_path="*identity*" OR policy_path="*verify*" OR policy_path="*validate*")
priority = 5
description = Identity verification events

[opa_resource_access]
search = sourcetype="opa:decision" policy_path="*"
priority = 3
description = Resource access events

# ============================================================================
# SECURITY EVENT TYPES
# ============================================================================

[opa_security_violation]
search = sourcetype="opa:decision" decision_result="deny"
priority = 5
description = Security policy violations

[opa_threat_detection]
search = sourcetype="opa:decision" decision_result="deny" | lookup user_info input_user OUTPUT risk_score as user_risk_score | where user_risk_score >= 6
priority = 6
description = Potential threat detection events

[opa_anomaly_detection]
search = sourcetype="opa:decision" | bucket _time span=5m | stats count by _time, input_user | eventstats avg(count) as avg_activity, stdev(count) as stdev_activity by input_user | eval threshold=avg_activity + (3 * stdev_activity) | where count > threshold
priority = 6
description = Anomalous activity detection

[opa_lateral_movement]
search = sourcetype="opa:decision" | stats dc(input_remote_addr) as unique_ips, dc(policy_path) as unique_policies by input_user | where unique_ips >= 3 AND unique_policies >= 5
priority = 7
description = Potential lateral movement indicators

[opa_data_exfiltration]
search = sourcetype="opa:decision" (policy_path="*data*" OR policy_path="*file*" OR policy_path="*export*" OR policy_path="*download*" OR policy_path="*backup*") | stats count by input_user | where count >= 10
priority = 7
description = Potential data exfiltration patterns

[opa_account_compromise]
search = sourcetype="opa:decision" | iplocation input_remote_addr | stats dc(Country) as countries, count(eval(decision_result="deny")) as denials by input_user | where countries >= 2 AND denials >= 5
priority = 8
description = Potential account compromise indicators

# ============================================================================
# OPERATIONAL EVENT TYPES
# ============================================================================

[opa_operational_event]
search = sourcetype="opa:health" OR sourcetype="opa:metrics"
priority = 2
description = Operational events from OPA instances

[opa_system_health]
search = sourcetype="opa:health" status="*"
priority = 3
description = OPA system health events

[opa_configuration_change]
search = sourcetype="opa:health" checks.bundle_status.last_successful_download!=checks.bundle_status.last_successful_activation
priority = 4
description = OPA configuration changes

[opa_deployment_event]
search = sourcetype="opa:health" checks.bundle_status.status="healthy" checks.bundle_status.last_successful_activation>relative_time(now(), "-5m")
priority = 4
description = OPA deployment-related events

# ============================================================================
# DATA CLASSIFICATION EVENT TYPES
# ============================================================================

[opa_sensitive_data_access]
search = sourcetype="opa:decision" (policy_path="*sensitive*" OR policy_path="*confidential*" OR policy_path="*private*")
priority = 5
description = Sensitive data access events

[opa_confidential_data_access]
search = sourcetype="opa:decision" policy_path="*confidential*"
priority = 6
description = Confidential data access events

[opa_public_data_access]
search = sourcetype="opa:decision" policy_path="*public*"
priority = 2
description = Public data access events

[opa_restricted_data_access]
search = sourcetype="opa:decision" (policy_path="*restricted*" OR policy_path="*classified*")
priority = 7
description = Restricted data access events

# ============================================================================
# ENVIRONMENT EVENT TYPES
# ============================================================================

[opa_production_event]
search = sourcetype="opa:decision" OR sourcetype="opa:health" | lookup opa_instances endpoint OUTPUT environment | where environment="production"
priority = 5
description = Production environment events

[opa_staging_event]
search = sourcetype="opa:decision" OR sourcetype="opa:health" | lookup opa_instances endpoint OUTPUT environment | where environment="staging"
priority = 3
description = Staging environment events

[opa_development_event]
search = sourcetype="opa:decision" OR sourcetype="opa:health" | lookup opa_instances endpoint OUTPUT environment | where environment="development"
priority = 2
description = Development environment events

[opa_test_event]
search = sourcetype="opa:decision" OR sourcetype="opa:health" | lookup opa_instances endpoint OUTPUT environment | where environment="test"
priority = 2
description = Test environment events

# ============================================================================
# TIME-BASED EVENT TYPES
# ============================================================================

[opa_business_hours]
search = sourcetype="opa:decision" | eval hour=strftime(_time, "%H"), day_of_week=strftime(_time, "%w") | where hour >= 8 AND hour <= 18 AND day_of_week NOT IN ("0", "6")
priority = 3
description = Business hours activity

[opa_off_hours]
search = sourcetype="opa:decision" | eval hour=strftime(_time, "%H"), day_of_week=strftime(_time, "%w") | where (hour < 8 OR hour > 18) OR day_of_week IN ("0", "6")
priority = 4
description = Off-hours activity

[opa_weekend_activity]
search = sourcetype="opa:decision" | eval day_of_week=strftime(_time, "%w") | where day_of_week IN ("0", "6")
priority = 4
description = Weekend activity

# ============================================================================
# ERROR EVENT TYPES
# ============================================================================

[opa_error_event]
search = sourcetype="opa:health" status="unhealthy" OR (sourcetype="opa:decision" decision_result="error")
priority = 5
description = OPA error events

[opa_timeout_event]
search = sourcetype="opa:decision" decision_evaluation_duration_ns > **********
priority = 4
description = OPA timeout events

[opa_failure_event]
search = sourcetype="opa:health" endpoint_state.consecutive_failures > 0
priority = 5
description = OPA failure events