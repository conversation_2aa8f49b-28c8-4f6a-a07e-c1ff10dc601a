# OPA Policy Audit & Compliance Add-on - Input Configuration

# OPA Decision Log HTTP Listener
[opa_decision_logs://default]
disabled = 0
interval = 30
index = opa_audit
sourcetype = opa:decision
host_segment = 4
http_port = 8088
http_path = /opadecisions
max_content_length = 10485760
max_connections = 100
buffer_size = 8192
timeout = 30
ssl_enabled = 0
ssl_cert_path = 
ssl_key_path = 
ssl_ca_path = 
log_level = INFO
health_check_enabled = 1
health_check_interval = 300

# Styra DAS Policy Audit Logs (Optional)
[styra_das_audit://default]
disabled = 1
interval = 300
index = opa_audit
sourcetype = styra:das:policy:audit
api_endpoint = https://tenant.styra.com/v1/systems/systemid/audits
api_token = 
max_events_per_request = 1000
request_timeout = 60
retry_attempts = 3
retry_delay = 5
log_level = INFO
start_time = -24h
filter_events = 

# OPA Instance Health Monitor
[opa_health_monitor://default]
disabled = 1
interval = 60
index = opa_audit
sourcetype = opa:health
opa_endpoints = http://localhost:8181/health
request_timeout = 10
log_level = INFO
health_threshold = 5

# Policy Performance Metrics
[opa_metrics://default]
disabled = 1
interval = 120
index = opa_audit
sourcetype = opa:metrics
opa_endpoints = http://localhost:8181/metrics
request_timeout = 15
metrics_format = prometheus
log_level = INFO