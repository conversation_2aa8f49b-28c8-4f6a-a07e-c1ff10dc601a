# OPA Policy Audit & Compliance Add-on - Search Macros
# This file contains reusable search macros for common OPA operations

# ============================================================================
# BASE FILTERS
# ============================================================================

[opa_base_filter]
definition = index=main (sourcetype="opa:decision" OR sourcetype="opa:health" OR sourcetype="opa:metrics" OR sourcetype="styra:audit")
args = 
description = Base filter for all OPA-related data sources
iseval = 0

[opa_decisions_only]
definition = index=main sourcetype="opa:decision"
args = 
description = Filter for OPA decision logs only
iseval = 0

[opa_denials_only]
definition = index=main sourcetype="opa:decision" decision_result="deny"
args = 
description = Filter for OPA policy denials only
iseval = 0

[opa_approvals_only]
definition = index=main sourcetype="opa:decision" decision_result="allow"
args = 
description = Filter for OPA policy approvals only
iseval = 0

[styra_audit_only]
definition = index=main sourcetype="styra:audit"
args = 
description = Filter for Styra DAS audit events only
iseval = 0

[opa_health_only]
definition = index=main sourcetype="opa:health"
args = 
description = Filter for OPA health monitoring data only
iseval = 0

[opa_metrics_only]
definition = index=main sourcetype="opa:metrics"
args = 
description = Filter for OPA performance metrics only
iseval = 0

# ============================================================================
# TIME-BASED FILTERS
# ============================================================================

[opa_last_hour]
definition = `opa_base_filter` earliest=-1h@h latest=now
args = 
description = OPA data from the last hour
iseval = 0

[opa_last_24h]
definition = `opa_base_filter` earliest=-24h@h latest=now
args = 
description = OPA data from the last 24 hours
iseval = 0

[opa_last_week]
definition = `opa_base_filter` earliest=-7d@d latest=now
args = 
description = OPA data from the last week
iseval = 0

[opa_business_hours]
definition = `opa_base_filter` | eval hour=strftime(_time, "%H"), day_of_week=strftime(_time, "%w") | where hour >= 8 AND hour <= 18 AND day_of_week NOT IN ("0", "6")
args = 
description = Filter for business hours (8 AM - 6 PM, weekdays)
iseval = 0

[opa_off_hours]
definition = `opa_base_filter` | eval hour=strftime(_time, "%H"), day_of_week=strftime(_time, "%w") | where (hour < 8 OR hour > 18) OR day_of_week IN ("0", "6")
args = 
description = Filter for off-hours activity (nights, weekends)
iseval = 0

# ============================================================================
# RISK AND SECURITY FILTERS
# ============================================================================

[opa_high_risk_policies]
definition = `opa_decisions_only` | lookup policy_criticality policy_path OUTPUT risk_score | where risk_score >= 8
args = 
description = Filter for high-risk policy evaluations (risk score >= 8)
iseval = 0

[opa_critical_violations]
definition = `opa_denials_only` | lookup policy_criticality policy_path OUTPUT risk_score | where risk_score >= 8
args = 
description = Filter for critical policy violations (denials with risk score >= 8)
iseval = 0

[opa_compliance_violations(1)]
definition = `opa_denials_only` | lookup policy_criticality policy_path OUTPUT compliance_framework | where compliance_framework="$framework$"
args = framework
description = Filter for compliance violations by framework (SOX, PCI, HIPAA, etc.)
iseval = 0

[opa_privileged_access]
definition = `opa_decisions_only` | rex field=policy_path "(?<privilege_level>admin|root|sudo|elevated|superuser|privileged)"
args = 
description = Filter for privileged access attempts
iseval = 0

[opa_suspicious_users]
definition = `opa_decisions_only` | lookup user_info input_user OUTPUT risk_score as user_risk_score | where user_risk_score >= 7
args = 
description = Filter for decisions involving high-risk users
iseval = 0

[opa_geographic_risk]
definition = `opa_decisions_only` | iplocation input_remote_addr | lookup geographic_risk Country OUTPUT risk_multiplier | where risk_multiplier >= 2
args = 
description = Filter for access from high-risk geographic locations
iseval = 0

# ============================================================================
# ENRICHMENT MACROS
# ============================================================================

[opa_enrich_user_context]
definition = lookup user_info input_user OUTPUT department, role, risk_score as user_risk_score, manager, location, clearance_level
args = 
description = Enrich events with user context information
iseval = 0

[opa_enrich_policy_context]
definition = lookup policy_criticality policy_path OUTPUT risk_score, compliance_framework, description as policy_description
args = 
description = Enrich events with policy context and risk information
iseval = 0

[opa_enrich_instance_context]
definition = lookup opa_instances endpoint OUTPUT environment, datacenter, cluster, version, owner_team, criticality
args = 
description = Enrich events with OPA instance context
iseval = 0

[opa_enrich_geographic_context]
definition = iplocation input_remote_addr | lookup geographic_risk Country OUTPUT risk_multiplier as geo_risk_multiplier
args = 
description = Enrich events with geographic and risk context
iseval = 0

[opa_full_enrichment]
definition = `opa_enrich_user_context` | `opa_enrich_policy_context` | `opa_enrich_instance_context` | `opa_enrich_geographic_context`
args = 
description = Apply all enrichment lookups to events
iseval = 0

# ============================================================================
# PERFORMANCE CALCULATIONS
# ============================================================================

[opa_calc_response_time]
definition = eval response_time_ms=decision_evaluation_duration_ns/1000000
args = 
description = Calculate response time in milliseconds from nanoseconds
iseval = 0

[opa_calc_performance_category]
definition = `opa_calc_response_time` | eval performance_category=case(response_time_ms < 10, "Excellent", response_time_ms < 50, "Good", response_time_ms < 200, "Fair", response_time_ms < 1000, "Poor", 1=1, "Critical")
args = 
description = Categorize performance based on response time
iseval = 0

[opa_calc_combined_risk]
definition = `opa_enrich_user_context` | `opa_enrich_policy_context` | `opa_enrich_geographic_context` | eval combined_risk=coalesce(risk_score,0) + coalesce(user_risk_score,0) + (coalesce(geo_risk_multiplier,1) * 2)
args = 
description = Calculate combined risk score from policy, user, and geographic factors
iseval = 0

# ============================================================================
# STATISTICAL CALCULATIONS
# ============================================================================

[opa_stats_by_user]
definition = stats count as total_decisions, count(eval(decision_result="allow")) as approvals, count(eval(decision_result="deny")) as denials, dc(policy_path) as unique_policies, dc(input_remote_addr) as unique_ips by input_user
args = 
description = Calculate decision statistics by user
iseval = 0

[opa_stats_by_policy]
definition = stats count as total_evaluations, count(eval(decision_result="allow")) as approvals, count(eval(decision_result="deny")) as denials, dc(input_user) as unique_users, avg(decision_evaluation_duration_ns/1000000) as avg_response_time_ms by policy_path
args = 
description = Calculate evaluation statistics by policy
iseval = 0

[opa_stats_by_department]
definition = `opa_enrich_user_context` | stats count as total_decisions, count(eval(decision_result="allow")) as approvals, count(eval(decision_result="deny")) as denials, dc(input_user) as unique_users, dc(policy_path) as unique_policies by department
args = 
description = Calculate decision statistics by department
iseval = 0

[opa_hourly_stats]
definition = bucket _time span=1h | stats count as decisions_per_hour, count(eval(decision_result="deny")) as denials_per_hour, dc(input_user) as unique_users_per_hour by _time
args = 
description = Calculate hourly decision statistics
iseval = 0

# ============================================================================
# ANOMALY DETECTION
# ============================================================================

[opa_detect_user_anomalies]
definition = `opa_denials_only` | bucket _time span=5m | stats count by _time, input_user | eventstats avg(count) as avg_denials, stdev(count) as stdev_denials by input_user | eval threshold=avg_denials + (3 * stdev_denials) | where count > threshold AND count >= 5
args = 
description = Detect anomalous denial patterns by user using statistical analysis
iseval = 0

[opa_detect_policy_anomalies]
definition = `opa_decisions_only` | bucket _time span=5m | stats count by _time, policy_path | eventstats avg(count) as avg_requests, stdev(count) as stdev_requests by policy_path | eval threshold=avg_requests + (2 * stdev_requests) | where count > threshold AND count >= 20
args = 
description = Detect anomalous request patterns by policy using statistical analysis
iseval = 0

[opa_detect_geographic_anomalies]
definition = `opa_decisions_only` | `opa_enrich_geographic_context` | stats count by input_user, Country | eventstats count as user_total by input_user | eval country_percentage=(count/user_total)*100 | where country_percentage >= 10 AND geo_risk_multiplier >= 2
args = 
description = Detect users accessing from unusual geographic locations
iseval = 0

# ============================================================================
# COMPLIANCE AND AUDIT
# ============================================================================

[opa_compliance_summary(1)]
definition = `opa_decisions_only` | `opa_enrich_policy_context` | where compliance_framework="$framework$" | stats count(eval(decision_result="allow")) as compliant_decisions, count(eval(decision_result="deny")) as violations, count as total_decisions | eval compliance_rate=round((compliant_decisions/total_decisions)*100,2)
args = framework
description = Calculate compliance summary for a specific framework
iseval = 0

[opa_audit_trail(2)]
definition = (`opa_decisions_only` decision_result="deny" input_user="$user$") OR (`styra_audit_only` user_email="$user$") | sort _time | eval event_type=case(sourcetype="opa:decision", "Policy Violation", sourcetype="styra:audit", "Policy Change") | table _time, event_type, input_user, user_email, policy_path, decision_result, change_type, description
args = user
description = Generate audit trail for a specific user across OPA decisions and Styra changes
iseval = 0

[opa_policy_usage_analysis(1)]
definition = `opa_decisions_only` policy_path="$policy$" | `opa_full_enrichment` | stats count as usage_count, count(eval(decision_result="allow")) as approvals, count(eval(decision_result="deny")) as denials, dc(input_user) as unique_users, values(department) as departments, avg(decision_evaluation_duration_ns/1000000) as avg_response_time
args = policy
description = Analyze usage patterns for a specific policy
iseval = 0

# ============================================================================
# SECURITY HUNTING
# ============================================================================

[opa_hunt_privilege_escalation]
definition = `opa_privileged_access` decision_result="deny" | `opa_enrich_user_context` | where clearance_level != "high" | stats count as escalation_attempts, dc(policy_path) as unique_privileged_resources by input_user, department, clearance_level | where escalation_attempts >= 3
args = 
description = Hunt for privilege escalation attempts
iseval = 0

[opa_hunt_data_exfiltration]
definition = `opa_decisions_only` | rex field=policy_path "(?<resource_type>data|file|document|export|download|backup)" | where isnotnull(resource_type) | `opa_enrich_user_context` | stats count as access_attempts, dc(policy_path) as unique_resources, values(decision_result) as results by input_user, department | where access_attempts >= 15 AND unique_resources >= 3
args = 
description = Hunt for potential data exfiltration patterns
iseval = 0

[opa_hunt_lateral_movement]
definition = `opa_decisions_only` | stats dc(input_remote_addr) as unique_ips, dc(policy_path) as unique_policies, count(eval(decision_result="deny")) as denials by input_user | where unique_ips >= 3 AND unique_policies >= 8 AND denials >= 5 | eval lateral_movement_score=(unique_ips * 2) + unique_policies + (denials * 2)
args = 
description = Hunt for lateral movement indicators
iseval = 0

[opa_hunt_account_compromise]
definition = `opa_decisions_only` | `opa_enrich_user_context` | `opa_enrich_geographic_context` | stats count as total_activity, dc(Country) as unique_countries, dc(input_remote_addr) as unique_ips, count(eval(decision_result="deny")) as denials by input_user | where (unique_countries >= 2 OR unique_ips >= 5) AND denials >= 10
args = 
description = Hunt for indicators of compromised accounts
iseval = 0

# ============================================================================
# OPERATIONAL MONITORING
# ============================================================================

[opa_instance_health_summary]
definition = `opa_health_only` | stats latest(status) as current_status, latest(checks.bundle_status.status) as bundle_status, latest(endpoint_state.consecutive_failures) as failures by endpoint | eval health_indicator=case(current_status="healthy" AND bundle_status="healthy" AND failures < 3, "✅ Healthy", current_status="unhealthy" OR failures >= 5, "❌ Critical", 1=1, "⚠️ Warning")
args = 
description = Summarize OPA instance health status
iseval = 0

[opa_performance_summary]
definition = `opa_decisions_only` | `opa_calc_performance_category` | stats count as total_decisions, avg(response_time_ms) as avg_response_time, perc95(response_time_ms) as p95_response_time by performance_category | eval performance_health=case(avg_response_time < 50, "Excellent", avg_response_time < 200, "Good", avg_response_time < 1000, "Fair", 1=1, "Poor")
args = 
description = Summarize OPA performance metrics
iseval = 0

[opa_bundle_status_check]
definition = `opa_health_only` | stats latest(checks.bundle_status.bundle_count) as bundle_count, latest(checks.bundle_status.last_successful_download) as last_download, latest(checks.bundle_status.last_successful_activation) as last_activation by endpoint | eval bundle_health=case(bundle_count > 0 AND (now() - last_download) < 3600, "Healthy", bundle_count = 0, "No Bundles", (now() - last_download) >= 3600, "Stale", 1=1, "Unknown")
args = 
description = Check OPA bundle status and freshness
iseval = 0

# ============================================================================
# REPORTING HELPERS
# ============================================================================

[opa_format_time_readable]
definition = eval readable_time=strftime(_time, "%Y-%m-%d %H:%M:%S")
args = 
description = Format timestamp in readable format
iseval = 0

[opa_calculate_percentages]
definition = eval approval_rate=round((approvals/total_decisions)*100,2), denial_rate=round((denials/total_decisions)*100,2)
args = 
description = Calculate approval and denial percentages (requires approvals, denials, total_decisions fields)
iseval = 0

[opa_risk_categorization]
definition = eval risk_category=case(combined_risk >= 15, "Critical", combined_risk >= 10, "High", combined_risk >= 5, "Medium", 1=1, "Low")
args = 
description = Categorize combined risk scores (requires combined_risk field)
iseval = 0

[opa_compliance_status]
definition = eval compliance_status=case(denial_rate <= 1, "Excellent", denial_rate <= 5, "Good", denial_rate <= 10, "Fair", 1=1, "Poor")
args = 
description = Determine compliance status based on denial rate (requires denial_rate field)
iseval = 0