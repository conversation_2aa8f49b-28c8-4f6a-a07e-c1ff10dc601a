# OPA Policy Audit & Compliance Add-on - Settings Configuration
# This file stores the add-on configuration settings managed through the setup page

[setup]
# OPA Decision Logs Configuration
opa_http_port = 8088
opa_http_path = /opadecisions
opa_ssl_enabled = 0
opa_ssl_cert_path = 
opa_ssl_key_path = 
opa_max_content_length = 10485760
opa_max_connections = 100
opa_buffer_size = 8192
opa_timeout = 30

# Styra DAS Integration Configuration
styra_enabled = 0
styra_api_endpoint = 
styra_api_token = 
styra_polling_interval = 300
styra_max_events_per_request = 1000
styra_request_timeout = 60
styra_retry_attempts = 3

# Health Monitoring Configuration
health_monitoring_enabled = 1
opa_endpoints = http://localhost:8181
health_check_interval = 60
health_timeout = 10

# Data Configuration
target_index = opa_audit
log_level = INFO

# Advanced Configuration
enable_metrics_collection = 1
metrics_polling_interval = 120
enable_performance_monitoring = 1