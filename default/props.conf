# OPA Policy Audit & Compliance Add-on - Props Configuration

# OPA Decision Logs
[opa:decision]
KV_MODE = json
AUTO_KV_JSON = true
SHOULD_LINEMERGE = false
LINE_BREAKER = ([\r\n]+)
TRUNCATE = 10000
TIME_PREFIX = "timestamp":
TIME_FORMAT = %Y-%m-%dT%H:%M:%S.%3N%z
MAX_TIMESTAMP_LOOKAHEAD = 32
TZ = UTC
category = Application
description = OPA Policy Decision Logs

# Field extractions for OPA decision logs
EXTRACT-decision_id = "decision_id":"(?<decision_id>[^"]+)"
EXTRACT-path = "path":"(?<path>[^"]+)"
EXTRACT-result = "result":(?<result>true|false)
EXTRACT-requested_by = "requested_by":"(?<requested_by>[^"]+)"
EXTRACT-policy_version = "revision":"(?<policy_version>[^"]+)"
EXTRACT-input_user = "input":{[^}]*"user":"(?<input_user>[^"]+)"
EXTRACT-input_method = "input":{[^}]*"method":"(?<input_method>[^"]+)"
EXTRACT-input_path = "input":{[^}]*"path":"(?<input_path>[^"]+)"
EXTRACT-query_duration_ns = "metrics":{[^}]*"timer_rego_query_eval_ns":(?<query_duration_ns>\d+)
EXTRACT-server_duration_ns = "metrics":{[^}]*"timer_server_handler_ns":(?<server_duration_ns>\d+)

# Calculated fields
EVAL-decision_result = if(result="true", "allow", "deny")
EVAL-query_duration_ms = round(query_duration_ns/1000000, 2)
EVAL-server_duration_ms = round(server_duration_ns/1000000, 2)
EVAL-policy_name = mvindex(split(path, "/"), -1)
EVAL-policy_package = mvjoin(mvindex(split(path, "/"), 0, -2), "/")

# Lookups
LOOKUP-policy_criticality = policy_criticality_lookup path OUTPUT criticality
LOOKUP-user_info = user_lookup input_user OUTPUT department, role, risk_score

# Tags for CIM compliance
TAGS = authentication, authorization, policy, security

# Styra DAS Policy Audit Logs
[styra:das:policy:audit]
KV_MODE = json
AUTO_KV_JSON = true
SHOULD_LINEMERGE = false
LINE_BREAKER = ([\r\n]+)
TRUNCATE = 10000
TIME_PREFIX = "timestamp":
TIME_FORMAT = %Y-%m-%dT%H:%M:%S.%3N%z
MAX_TIMESTAMP_LOOKAHEAD = 32
TZ = UTC
category = Change
description = Styra DAS Policy Audit Events

# Field extractions for Styra DAS audit logs
EXTRACT-event_id = "id":"(?<event_id>[^"]+)"
EXTRACT-event_type = "type":"(?<event_type>[^"]+)"
EXTRACT-user = "user":{[^}]*"id":"(?<user>[^"]+)"
EXTRACT-user_name = "user":{[^}]*"name":"(?<user_name>[^"]+)"
EXTRACT-object_type = "object":{[^}]*"type":"(?<object_type>[^"]+)"
EXTRACT-object_id = "object":{[^}]*"id":"(?<object_id>[^"]+)"
EXTRACT-object_name = "object":{[^}]*"name":"(?<object_name>[^"]+)"
EXTRACT-action = "action":"(?<action>[^"]+)"
EXTRACT-status = "status":"(?<status>[^"]+)"
EXTRACT-system_id = "system_id":"(?<system_id>[^"]+)"
EXTRACT-tenant = "tenant":"(?<tenant>[^"]+)"

# Calculated fields for change analysis
EVAL-change_type = case(
    action="create", "created",
    action="update", "modified", 
    action="delete", "deleted",
    action="publish", "published",
    1=1, action
)
EVAL-vendor_action = action
EVAL-object = coalesce(object_name, object_id)
EVAL-src_user = user
EVAL-result = if(status="success", "success", "failure")

# Tags for CIM compliance
TAGS = change, audit, policy, governance

# OPA Health Status
[opa:health]
KV_MODE = json
AUTO_KV_JSON = true
SHOULD_LINEMERGE = false
LINE_BREAKER = ([\r\n]+)
TRUNCATE = 5000
TIME_FORMAT = %Y-%m-%dT%H:%M:%S.%3N%z
MAX_TIMESTAMP_LOOKAHEAD = 32
TZ = UTC
category = Application
description = OPA Instance Health Status

# Field extractions for health monitoring
EXTRACT-opa_endpoint = "endpoint":"(?<opa_endpoint>[^"]+)"
EXTRACT-health_status = "status":"(?<health_status>[^"]+)"
EXTRACT-response_time_ms = "response_time_ms":(?<response_time_ms>[\d.]+)
EXTRACT-bundle_status = "bundle_status":"(?<bundle_status>[^"]+)"
EXTRACT-plugin_status = "plugin_status":"(?<plugin_status>[^"]+)"

# Tags
TAGS = monitoring, health, infrastructure

# OPA Metrics
[opa:metrics]
KV_MODE = none
SHOULD_LINEMERGE = false
LINE_BREAKER = ([\r\n]+)
TRUNCATE = 10000
TIME_FORMAT = %Y-%m-%dT%H:%M:%S.%3N%z
MAX_TIMESTAMP_LOOKAHEAD = 32
TZ = UTC
category = Metrics
description = OPA Performance Metrics

# Prometheus format field extractions
EXTRACT-metric_name = ^(?<metric_name>[a-zA-Z_:][a-zA-Z0-9_:]*)
EXTRACT-metric_value = }\s+(?<metric_value>[\d.]+)
EXTRACT-metric_labels = {(?<metric_labels>[^}]*)}

# Tags
TAGS = metrics, performance, monitoring

# Common field aliases for CIM compliance
[opa:decision]
FIELDALIAS-user = input_user AS user
FIELDALIAS-src = requested_by AS src
FIELDALIAS-action = input_method AS action
FIELDALIAS-object = path AS object
FIELDALIAS-vendor_action = path AS vendor_action
FIELDALIAS-signature = policy_name AS signature
FIELDALIAS-signature_id = decision_id AS signature_id

[styra:das:policy:audit]
FIELDALIAS-src_user = user AS src_user
FIELDALIAS-user = user_name AS user
FIELDALIAS-dest = system_id AS dest
FIELDALIAS-vendor = "Styra" AS vendor
FIELDALIAS-vendor_product = "DAS" AS vendor_product