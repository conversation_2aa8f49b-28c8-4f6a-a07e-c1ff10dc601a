# OPA Policy Audit & Compliance Add-on - Saved Searches
# This file contains pre-built searches, alerts, and reports for comprehensive OPA monitoring

# ============================================================================
# SECURITY ALERTS
# ============================================================================

[OPA - Critical Policy Violations]
search = index=main sourcetype="opa:decision" decision_result="deny" \
| lookup policy_criticality policy_path OUTPUT risk_score, compliance_framework \
| where risk_score >= 8 \
| lookup user_info input_user OUTPUT department, role, risk_score as user_risk_score \
| eval combined_risk=coalesce(risk_score,0) + coalesce(user_risk_score,0) \
| where combined_risk >= 15 \
| eval alert_time=strftime(_time, "%Y-%m-%d %H:%M:%S") \
| table alert_time, input_user, department, role, policy_path, compliance_framework, combined_risk, input_remote_addr
dispatch.earliest_time = -15m
dispatch.latest_time = now
cron_schedule = */5 * * * *
is_scheduled = 1
alert.track = 1
alert.severity = 3
action.email = 1
action.email.to = <EMAIL>
action.email.subject = CRITICAL: High-Risk OPA Policy Violation Detected
action.email.message.alert = A critical policy violation has been detected in OPA with combined risk score >= 15. Immediate investigation required.
description = Detects critical policy violations with high combined risk scores requiring immediate security attention

[OPA - Privilege Escalation Attempts]
search = index=main sourcetype="opa:decision" decision_result="deny" \
| rex field=policy_path "(?<privilege_level>admin|root|sudo|elevated|superuser)" \
| where isnotnull(privilege_level) \
| lookup user_info input_user OUTPUT clearance_level, department, role \
| where clearance_level != "high" \
| eval alert_time=strftime(_time, "%Y-%m-%d %H:%M:%S") \
| table alert_time, input_user, department, role, clearance_level, policy_path, privilege_level, input_remote_addr
dispatch.earliest_time = -10m
dispatch.latest_time = now
cron_schedule = */5 * * * *
is_scheduled = 1
alert.track = 1
alert.severity = 2
action.email = 1
action.email.to = <EMAIL>
action.email.subject = ALERT: Privilege Escalation Attempt Detected
action.email.message.alert = Unauthorized privilege escalation attempt detected. User without high clearance attempted to access privileged resources.
description = Detects attempts by users to access privileged resources without proper authorization

[OPA - Anomalous User Behavior]
search = index=main sourcetype="opa:decision" decision_result="deny" \
| bucket _time span=5m \
| stats count by _time, input_user \
| eventstats avg(count) as avg_denials, stdev(count) as stdev_denials by input_user \
| eval threshold=avg_denials + (3 * stdev_denials) \
| where count > threshold AND count >= 10 \
| lookup user_info input_user OUTPUT department, role \
| eval alert_time=strftime(_time, "%Y-%m-%d %H:%M:%S") \
| table alert_time, input_user, department, role, count, threshold
dispatch.earliest_time = -1h
dispatch.latest_time = now
cron_schedule = */10 * * * *
is_scheduled = 1
alert.track = 1
alert.severity = 2
action.email = 1
action.email.to = <EMAIL>
action.email.subject = ALERT: Anomalous User Behavior Detected
action.email.message.alert = User showing anomalous access denial patterns. Potential security incident or compromised account.
description = Detects users with anomalously high denial rates indicating potential security issues

[OPA - Geographic Risk Alert]
search = index=main sourcetype="opa:decision" decision_result="deny" \
| iplocation input_remote_addr \
| lookup geographic_risk Country OUTPUT risk_multiplier \
| eval geo_risk=coalesce(risk_multiplier, 1) \
| where geo_risk >= 3 \
| lookup user_info input_user OUTPUT department, role \
| eval alert_time=strftime(_time, "%Y-%m-%d %H:%M:%S") \
| table alert_time, input_user, department, role, input_remote_addr, Country, City, geo_risk, policy_path
dispatch.earliest_time = -15m
dispatch.latest_time = now
cron_schedule = */10 * * * *
is_scheduled = 1
alert.track = 1
alert.severity = 2
action.email = 1
action.email.to = <EMAIL>
action.email.subject = ALERT: High-Risk Geographic Access Detected
action.email.message.alert = Access attempt from high-risk geographic location detected. Review for potential unauthorized access.
description = Detects access attempts from high-risk geographic locations

[OPA - Instance Health Critical]
search = index=main sourcetype="opa:health" \
| stats latest(status) as current_status, latest(endpoint_state.consecutive_failures) as failures by endpoint \
| where current_status="unhealthy" OR failures >= 5 \
| eval alert_time=strftime(_time, "%Y-%m-%d %H:%M:%S") \
| table alert_time, endpoint, current_status, failures
dispatch.earliest_time = -5m
dispatch.latest_time = now
cron_schedule = */2 * * * *
is_scheduled = 1
alert.track = 1
alert.severity = 3
action.email = 1
action.email.to = <EMAIL>
action.email.subject = CRITICAL: OPA Instance Health Alert
action.email.message.alert = OPA instance health critical. Immediate attention required to maintain policy enforcement.
description = Monitors OPA instance health and alerts on critical failures

# ============================================================================
# COMPLIANCE REPORTS
# ============================================================================

[OPA - Daily Compliance Summary]
search = index=main sourcetype="opa:decision" \
| lookup policy_criticality policy_path OUTPUT compliance_framework, risk_score \
| where isnotnull(compliance_framework) \
| stats count(eval(decision_result="allow")) as compliant_decisions, \
       count(eval(decision_result="deny")) as violation_count, \
       count as total_decisions \
       by compliance_framework \
| eval compliance_rate=round((compliant_decisions/total_decisions)*100,2) \
| eval violation_rate=round((violation_count/total_decisions)*100,2) \
| table compliance_framework, total_decisions, compliant_decisions, violation_count, compliance_rate, violation_rate \
| sort -violation_count
dispatch.earliest_time = -24h@h
dispatch.latest_time = now
cron_schedule = 0 8 * * *
is_scheduled = 1
action.email = 1
action.email.to = <EMAIL>
action.email.subject = Daily OPA Compliance Summary Report
action.email.message.alert = Daily compliance summary for OPA policy decisions across all frameworks.
action.email.format = table
description = Daily compliance summary report showing violation rates by framework

[OPA - Weekly Security Metrics]
search = index=main sourcetype="opa:decision" \
| lookup policy_criticality policy_path OUTPUT risk_score \
| lookup user_info input_user OUTPUT department \
| eval risk_category=case(risk_score >= 8, "Critical", risk_score >= 6, "High", risk_score >= 4, "Medium", 1=1, "Low") \
| stats count(eval(decision_result="deny")) as violations, \
       count as total_decisions, \
       dc(input_user) as unique_users, \
       dc(input_remote_addr) as unique_ips \
       by department, risk_category \
| eval violation_rate=round((violations/total_decisions)*100,2) \
| sort department, risk_category
dispatch.earliest_time = -7d@d
dispatch.latest_time = now
cron_schedule = 0 9 * * 1
is_scheduled = 1
action.email = 1
action.email.to = <EMAIL>,<EMAIL>
action.email.subject = Weekly OPA Security Metrics Report
action.email.message.alert = Weekly security metrics showing policy violations and risk distribution by department.
action.email.format = table
description = Weekly security metrics report by department and risk category

[OPA - Monthly Audit Report]
search = index=main (sourcetype="opa:decision" OR sourcetype="styra:audit") \
| eval event_type=case(sourcetype="opa:decision", "Policy Decision", sourcetype="styra:audit", "Policy Change") \
| lookup policy_criticality policy_path OUTPUT compliance_framework, risk_score \
| stats count by event_type, compliance_framework, decision_result \
| fillnull value="N/A" \
| sort event_type, compliance_framework
dispatch.earliest_time = -30d@d
dispatch.latest_time = now
cron_schedule = 0 10 1 * *
is_scheduled = 1
action.email = 1
action.email.to = <EMAIL>,<EMAIL>
action.email.subject = Monthly OPA Audit Report
action.email.message.alert = Monthly comprehensive audit report covering policy decisions and changes.
action.email.format = table
description = Monthly comprehensive audit report for compliance and audit teams

# ============================================================================
# OPERATIONAL REPORTS
# ============================================================================

[OPA - Performance Baseline]
search = index=main sourcetype="opa:decision" \
| eval response_time_ms=decision_evaluation_duration_ns/1000000 \
| stats avg(response_time_ms) as avg_response_time, \
       perc50(response_time_ms) as median_response_time, \
       perc95(response_time_ms) as p95_response_time, \
       perc99(response_time_ms) as p99_response_time, \
       count as total_decisions \
       by policy_path \
| where total_decisions >= 100 \
| sort -p95_response_time
dispatch.earliest_time = -7d@d
dispatch.latest_time = now
cron_schedule = 0 6 * * 1
is_scheduled = 1
action.email = 1
action.email.to = <EMAIL>
action.email.subject = Weekly OPA Performance Baseline Report
action.email.message.alert = Weekly performance baseline showing response times by policy path.
action.email.format = table
description = Weekly performance baseline report for capacity planning

[OPA - Top Policy Violators]
search = index=main sourcetype="opa:decision" decision_result="deny" \
| lookup user_info input_user OUTPUT department, role, manager \
| stats count as violation_count, \
       dc(policy_path) as unique_policies_violated, \
       dc(input_remote_addr) as unique_source_ips, \
       values(department) as departments, \
       latest(role) as role, \
       latest(manager) as manager \
       by input_user \
| where violation_count >= 10 \
| sort -violation_count \
| head 20
dispatch.earliest_time = -7d@d
dispatch.latest_time = now
cron_schedule = 0 9 * * 1
is_scheduled = 1
action.email = 1
action.email.to = <EMAIL>,<EMAIL>
action.email.subject = Weekly Top Policy Violators Report
action.email.message.alert = Weekly report of users with highest policy violation counts for review.
action.email.format = table
description = Weekly report identifying users with highest violation counts

[OPA - Bundle Health Status]
search = index=main sourcetype="opa:health" \
| stats latest(checks.bundle_status.status) as bundle_status, \
       latest(checks.bundle_status.bundle_count) as bundle_count, \
       values(checks.bundle_status.bundles.*) as bundle_details \
       by endpoint \
| eval health_status=case(bundle_status="healthy", "✅ Healthy", bundle_status="error", "❌ Error", 1=1, "⚠️ Unknown") \
| table endpoint, health_status, bundle_count, bundle_details
dispatch.earliest_time = -1h@h
dispatch.latest_time = now
cron_schedule = 0 */4 * * *
is_scheduled = 1
action.email = 1
action.email.to = <EMAIL>
action.email.subject = OPA Bundle Health Status Report
action.email.message.alert = Current status of OPA bundle health across all instances.
action.email.format = table
description = Regular bundle health status report for operational monitoring

# ============================================================================
# THREAT HUNTING SEARCHES
# ============================================================================

[OPA - Potential Data Exfiltration]
search = index=main sourcetype="opa:decision" \
| rex field=policy_path "(?<resource_type>data|file|document|export|download)" \
| where isnotnull(resource_type) \
| lookup user_info input_user OUTPUT department, clearance_level \
| stats count as access_attempts, \
       dc(policy_path) as unique_resources, \
       values(decision_result) as results \
       by input_user, department, clearance_level \
| where access_attempts >= 20 AND unique_resources >= 5 \
| eval risk_indicator=case(access_attempts >= 50, "High", access_attempts >= 30, "Medium", 1=1, "Low") \
| sort -access_attempts
dispatch.earliest_time = -24h@h
dispatch.latest_time = now
cron_schedule = 0 */6 * * *
is_scheduled = 0
description = Hunt for potential data exfiltration attempts based on resource access patterns

[OPA - Lateral Movement Detection]
search = index=main sourcetype="opa:decision" \
| stats dc(input_remote_addr) as unique_ips, \
       dc(policy_path) as unique_policies, \
       count(eval(decision_result="deny")) as denials, \
       count as total_attempts \
       by input_user \
| where unique_ips >= 3 AND unique_policies >= 10 AND denials >= 5 \
| lookup user_info input_user OUTPUT department, role \
| eval lateral_movement_score=(unique_ips * 2) + (unique_policies * 1) + (denials * 3) \
| where lateral_movement_score >= 25 \
| sort -lateral_movement_score
dispatch.earliest_time = -4h@h
dispatch.latest_time = now
cron_schedule = 0 */2 * * *
is_scheduled = 0
description = Detect potential lateral movement based on multi-IP, multi-policy access patterns

[OPA - Off-Hours Activity]
search = index=main sourcetype="opa:decision" \
| eval hour=strftime(_time, "%H"), day_of_week=strftime(_time, "%w") \
| where (hour < 7 OR hour > 19) OR day_of_week IN ("0", "6") \
| lookup user_info input_user OUTPUT department, role \
| stats count as off_hours_activity, \
       count(eval(decision_result="deny")) as off_hours_denials \
       by input_user, department, role \
| where off_hours_activity >= 10 \
| eval denial_rate=round((off_hours_denials/off_hours_activity)*100,2) \
| sort -off_hours_activity
dispatch.earliest_time = -7d@d
dispatch.latest_time = now
cron_schedule = 0 8 * * 1
is_scheduled = 0
description = Identify users with significant off-hours activity for investigation

# ============================================================================
# PERFORMANCE MONITORING
# ============================================================================

[OPA - Slow Policy Evaluation]
search = index=main sourcetype="opa:decision" \
| eval response_time_ms=decision_evaluation_duration_ns/1000000 \
| where response_time_ms > 1000 \
| stats count as slow_evaluations, \
       avg(response_time_ms) as avg_response_time, \
       max(response_time_ms) as max_response_time \
       by policy_path \
| where slow_evaluations >= 5 \
| sort -avg_response_time
dispatch.earliest_time = -1h@h
dispatch.latest_time = now
cron_schedule = */15 * * * *
is_scheduled = 1
alert.track = 1
alert.severity = 1
action.email = 1
action.email.to = <EMAIL>
action.email.subject = OPA Performance Alert - Slow Policy Evaluations
action.email.message.alert = Slow policy evaluations detected. Performance optimization may be required.
description = Monitor and alert on slow policy evaluations affecting performance

[OPA - High Volume Policies]
search = index=main sourcetype="opa:decision" \
| bucket _time span=5m \
| stats count by _time, policy_path \
| eventstats avg(count) as avg_requests, stdev(count) as stdev_requests by policy_path \
| eval threshold=avg_requests + (2 * stdev_requests) \
| where count > threshold AND count >= 100 \
| eval alert_time=strftime(_time, "%Y-%m-%d %H:%M:%S") \
| table alert_time, policy_path, count, threshold
dispatch.earliest_time = -1h@h
dispatch.latest_time = now
cron_schedule = */10 * * * *
is_scheduled = 1
alert.track = 1
alert.severity = 1
action.email = 1
action.email.to = <EMAIL>
action.email.subject = OPA Alert - High Volume Policy Activity
action.email.message.alert = Unusually high volume detected for specific policies. May indicate load issues or attacks.
description = Detect and alert on unusually high volume policy evaluations

# ============================================================================
# STYRA DAS MONITORING
# ============================================================================

[Styra - Critical Policy Changes]
search = index=main sourcetype="styra:audit" \
| where severity IN ("high", "critical") \
| eval alert_time=strftime(_time, "%Y-%m-%d %H:%M:%S") \
| table alert_time, user_email, change_type, policy_path, system_name, severity, description
dispatch.earliest_time = -15m
dispatch.latest_time = now
cron_schedule = */5 * * * *
is_scheduled = 1
alert.track = 1
alert.severity = 2
action.email = 1
action.email.to = <EMAIL>,<EMAIL>
action.email.subject = ALERT: Critical Policy Changes in Styra DAS
action.email.message.alert = Critical policy changes detected in Styra DAS requiring immediate review.
description = Monitor critical policy changes in Styra DAS

[Styra - Unauthorized Policy Modifications]
search = index=main sourcetype="styra:audit" change_type IN ("delete", "modify") \
| lookup authorized_policy_editors user_email OUTPUT authorized \
| where isnull(authorized) OR authorized="false" \
| eval alert_time=strftime(_time, "%Y-%m-%d %H:%M:%S") \
| table alert_time, user_email, change_type, policy_path, system_name, description
dispatch.earliest_time = -10m
dispatch.latest_time = now
cron_schedule = */5 * * * *
is_scheduled = 1
alert.track = 1
alert.severity = 3
action.email = 1
action.email.to = <EMAIL>
action.email.subject = CRITICAL: Unauthorized Policy Modification Detected
action.email.message.alert = Unauthorized user attempted to modify policies in Styra DAS. Immediate investigation required.
description = Detect unauthorized policy modifications in Styra DAS