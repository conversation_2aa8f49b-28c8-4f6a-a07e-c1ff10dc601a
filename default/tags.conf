# OPA Policy Audit & Compliance Add-on - Tags Configuration
# This file defines CIM-compliant tags for proper data model acceleration

# ============================================================================
# AUTHENTICATION TAGS
# ============================================================================

[eventtype=opa_authentication_success]
authentication = enabled
action = success

[eventtype=opa_authentication_failure]
authentication = enabled
action = failure

[eventtype=opa_privileged_authentication]
authentication = enabled
privileged = enabled

# ============================================================================
# CHANGE ANALYSIS TAGS
# ============================================================================

[eventtype=styra_policy_change]
change = enabled
change_analysis = enabled

[eventtype=styra_policy_creation]
change = enabled
change_analysis = enabled
action = created

[eventtype=styra_policy_modification]
change = enabled
change_analysis = enabled
action = modified

[eventtype=styra_policy_deletion]
change = enabled
change_analysis = enabled
action = deleted

[eventtype=opa_bundle_change]
change = enabled
change_analysis = enabled
configuration = enabled

# ============================================================================
# APPLICATION STATE TAGS
# ============================================================================

[eventtype=opa_health_status]
application_state = enabled
availability = enabled

[eventtype=opa_performance_metrics]
application_state = enabled
performance = enabled

[eventtype=opa_bundle_status]
application_state = enabled
configuration = enabled

# ============================================================================
# ALERT TAGS
# ============================================================================

[eventtype=opa_critical_violation]
alert = enabled
severity = critical
security = enabled

[eventtype=opa_high_risk_denial]
alert = enabled
severity = high
security = enabled

[eventtype=opa_compliance_violation]
alert = enabled
compliance = enabled

[eventtype=opa_performance_alert]
alert = enabled
performance = enabled
availability = enabled

[eventtype=opa_security_incident]
alert = enabled
security = enabled
incident = enabled

# ============================================================================
# PERFORMANCE TAGS
# ============================================================================

[eventtype=opa_slow_response]
performance = enabled
latency = enabled

[eventtype=opa_high_volume]
performance = enabled
throughput = enabled

[eventtype=opa_resource_utilization]
performance = enabled
resource = enabled

# ============================================================================
# NETWORK TRAFFIC TAGS
# ============================================================================

[eventtype=opa_network_access]
network = enabled
communicate = enabled

[eventtype=opa_remote_access]
network = enabled
communicate = enabled
remote = enabled

[eventtype=opa_geographic_access]
network = enabled
communicate = enabled
geographic = enabled

# ============================================================================
# VULNERABILITY TAGS
# ============================================================================

[eventtype=opa_privilege_escalation]
vulnerability = enabled
security = enabled
privileged = enabled

[eventtype=opa_unauthorized_access]
vulnerability = enabled
security = enabled
unauthorized = enabled

[eventtype=opa_data_exposure_risk]
vulnerability = enabled
security = enabled
data = enabled

# ============================================================================
# RISK TAGS
# ============================================================================

[eventtype=opa_high_risk_activity]
risk = enabled
severity = high

[eventtype=opa_critical_risk_activity]
risk = enabled
severity = critical

[eventtype=opa_user_risk_activity]
risk = enabled
user = enabled

[eventtype=opa_geographic_risk]
risk = enabled
geographic = enabled

[eventtype=opa_policy_risk]
risk = enabled
policy = enabled

# ============================================================================
# COMPLIANCE TAGS
# ============================================================================

[eventtype=opa_sox_compliance]
compliance = enabled
framework = sox

[eventtype=opa_pci_compliance]
compliance = enabled
framework = pci

[eventtype=opa_hipaa_compliance]
compliance = enabled
framework = hipaa

[eventtype=opa_gdpr_compliance]
compliance = enabled
framework = gdpr

[eventtype=opa_iso27001_compliance]
compliance = enabled
framework = iso27001

[eventtype=opa_nist_compliance]
compliance = enabled
framework = nist

# ============================================================================
# AUDIT TAGS
# ============================================================================

[eventtype=opa_audit_event]
audit = enabled

[eventtype=styra_audit_event]
audit = enabled
change = enabled

[eventtype=opa_access_audit]
audit = enabled
access = enabled

[eventtype=opa_policy_audit]
audit = enabled
policy = enabled

[eventtype=opa_compliance_audit]
audit = enabled
compliance = enabled

# ============================================================================
# IDENTITY MANAGEMENT TAGS
# ============================================================================

[eventtype=opa_user_activity]
identity = enabled
user = enabled

[eventtype=opa_privileged_user_activity]
identity = enabled
user = enabled
privileged = enabled

[eventtype=opa_service_account_activity]
identity = enabled
service = enabled

[eventtype=opa_role_based_access]
identity = enabled
role = enabled

# ============================================================================
# ASSET AND IDENTITY TAGS
# ============================================================================

[eventtype=opa_asset_access]
asset = enabled
access = enabled

[eventtype=opa_identity_verification]
identity = enabled
verification = enabled

[eventtype=opa_resource_access]
asset = enabled
resource = enabled

# ============================================================================
# SECURITY TAGS
# ============================================================================

[eventtype=opa_security_violation]
security = enabled
violation = enabled

[eventtype=opa_threat_detection]
security = enabled
threat = enabled

[eventtype=opa_anomaly_detection]
security = enabled
anomaly = enabled

[eventtype=opa_lateral_movement]
security = enabled
lateral_movement = enabled

[eventtype=opa_data_exfiltration]
security = enabled
data_exfiltration = enabled

[eventtype=opa_account_compromise]
security = enabled
compromise = enabled

# ============================================================================
# OPERATIONAL TAGS
# ============================================================================

[eventtype=opa_operational_event]
operational = enabled

[eventtype=opa_system_health]
operational = enabled
health = enabled

[eventtype=opa_configuration_change]
operational = enabled
configuration = enabled

[eventtype=opa_deployment_event]
operational = enabled
deployment = enabled

# ============================================================================
# DATA CLASSIFICATION TAGS
# ============================================================================

[eventtype=opa_sensitive_data_access]
data = enabled
sensitive = enabled

[eventtype=opa_confidential_data_access]
data = enabled
confidential = enabled

[eventtype=opa_public_data_access]
data = enabled
public = enabled

[eventtype=opa_restricted_data_access]
data = enabled
restricted = enabled

# ============================================================================
# ENVIRONMENT TAGS
# ============================================================================

[eventtype=opa_production_event]
environment = production

[eventtype=opa_staging_event]
environment = staging

[eventtype=opa_development_event]
environment = development

[eventtype=opa_test_event]
environment = test

# ============================================================================
# CRITICALITY TAGS
# ============================================================================

[eventtype=opa_critical_system]
criticality = critical

[eventtype=opa_high_criticality]
criticality = high

[eventtype=opa_medium_criticality]
criticality = medium

[eventtype=opa_low_criticality]
criticality = low

# ============================================================================
# TIME-BASED TAGS
# ============================================================================

[eventtype=opa_business_hours]
time_category = business_hours

[eventtype=opa_off_hours]
time_category = off_hours

[eventtype=opa_weekend_activity]
time_category = weekend

[eventtype=opa_holiday_activity]
time_category = holiday

# ============================================================================
# GEOGRAPHIC TAGS
# ============================================================================

[eventtype=opa_domestic_access]
geographic = domestic

[eventtype=opa_international_access]
geographic = international

[eventtype=opa_high_risk_country]
geographic = high_risk

[eventtype=opa_restricted_country]
geographic = restricted

# ============================================================================
# DEPARTMENT TAGS
# ============================================================================

[eventtype=opa_finance_activity]
department = finance

[eventtype=opa_hr_activity]
department = hr

[eventtype=opa_it_activity]
department = it

[eventtype=opa_legal_activity]
department = legal

[eventtype=opa_operations_activity]
department = operations

[eventtype=opa_sales_activity]
department = sales

[eventtype=opa_marketing_activity]
department = marketing

# ============================================================================
# APPLICATION TAGS
# ============================================================================

[eventtype=opa_web_application]
application = web

[eventtype=opa_api_application]
application = api

[eventtype=opa_mobile_application]
application = mobile

[eventtype=opa_database_application]
application = database

[eventtype=opa_microservice_application]
application = microservice

# ============================================================================
# PROTOCOL TAGS
# ============================================================================

[eventtype=opa_http_request]
protocol = http

[eventtype=opa_https_request]
protocol = https

[eventtype=opa_grpc_request]
protocol = grpc

[eventtype=opa_rest_api]
protocol = rest

# ============================================================================
# ERROR AND EXCEPTION TAGS
# ============================================================================

[eventtype=opa_error_event]
error = enabled

[eventtype=opa_exception_event]
exception = enabled

[eventtype=opa_timeout_event]
timeout = enabled

[eventtype=opa_failure_event]
failure = enabled

# ============================================================================
# MONITORING TAGS
# ============================================================================

[eventtype=opa_monitoring_event]
monitoring = enabled

[eventtype=opa_alerting_event]
alerting = enabled

[eventtype=opa_dashboard_event]
dashboard = enabled

[eventtype=opa_reporting_event]
reporting = enabled