# OPA Policy Audit & Compliance Add-on - Transforms Configuration

# Policy Criticality Lookup
[policy_criticality_lookup]
filename = policy_criticality.csv
case_sensitive_match = false

# User Information Lookup
[user_lookup]
filename = user_info.csv
case_sensitive_match = false

# OPA Instance Lookup
[opa_instance_lookup]
filename = opa_instances.csv
case_sensitive_match = false

# Geographic Risk Lookup
[geographic_risk]
filename = geographic_risk.csv
case_sensitive_match = false

# Policy Package Mapping
[policy_package_lookup]
filename = policy_packages.csv
case_sensitive_match = false

# Risk Score Calculation Transform
[risk_score_transform]
regex = .*
format = risk_score
external_cmd = calculate_risk_score.py
fields_list = result,policy_criticality,user_risk_score,input_method

# Decision Result Normalization
[decision_result_normalize]
regex = "result":(true|false)
format = decision_result::$1

# Policy Path Extraction
[policy_path_extract]
regex = "path":"([^"]+)"
format = policy_path::$1

# Input Data Sanitization (Remove sensitive fields)
[input_sanitize]
regex = ("password"|"secret"|"token"|"key"):\s*"[^"]*"
format = $1:"[REDACTED]"

# Timestamp Normalization for Styra DAS
[styra_timestamp_normalize]
regex = "timestamp":"([^"]+)"
format = normalized_timestamp::$1

# User Agent Parsing
[user_agent_parse]
regex = "user_agent":"([^"]+)"
format = parsed_user_agent::$1

# IP Address Extraction from Input
[ip_extract_from_input]
regex = "input":{[^}]*"(?:client_ip|remote_addr|ip)":"([^"]+)"
format = client_ip::$1

# Policy Version Extraction
[policy_version_extract]
regex = "revision":"([^"]+)"
format = policy_version::$1

# Bundle Information Extraction
[bundle_info_extract]
regex = "bundles":{[^}]*"([^"]+)":{[^}]*"revision":"([^"]+)"
format = bundle_name::$1,bundle_revision::$2

# Error Message Extraction
[error_message_extract]
regex = "error":{[^}]*"message":"([^"]+)"
format = error_message::$1

# Performance Metrics Extraction
[performance_metrics_extract]
regex = "metrics":{[^}]*"timer_rego_query_eval_ns":(\d+)[^}]*"timer_server_handler_ns":(\d+)
format = query_time_ns::$1,server_time_ns::$2

# System Information Extraction
[system_info_extract]
regex = "labels":{[^}]*"system":"([^"]+)"
format = system_name::$1

# Tenant Information Extraction for Styra DAS
[tenant_extract]
regex = "tenant":"([^"]+)"
format = tenant_id::$1

# Change Type Mapping for Styra DAS
[change_type_mapping]
filename = change_type_mapping.csv
case_sensitive_match = false

# Severity Mapping
[severity_mapping]
regex = .*
format = severity
external_cmd = calculate_severity.py
fields_list = result,policy_criticality,error_message

# Geographic Location Lookup for IP Addresses
[geo_lookup]
external_cmd = geoip_lookup.py
fields_list = client_ip

# Policy Compliance Status
[compliance_status_lookup]
filename = compliance_policies.csv
case_sensitive_match = false

# Department Mapping
[department_lookup]
filename = department_mapping.csv
case_sensitive_match = false

# Application Mapping
[application_lookup]
filename = application_mapping.csv
case_sensitive_match = false

# Environment Classification
[environment_classify]
regex = "labels":{[^}]*"env(?:ironment)?":"([^"]+)"
format = environment::$1

# Data Classification
[data_classification]
regex = "input":{[^}]*"data_classification":"([^"]+)"
format = data_class::$1

# Request Method Normalization
[method_normalize]
regex = "method":"([^"]+)"
format = http_method::$1

# URL Path Extraction
[url_path_extract]
regex = "path":"([^"?]+)"
format = url_path::$1

# Query Parameters Extraction
[query_params_extract]
regex = "path":"[^?]*\?([^"]+)"
format = query_params::$1

# Session ID Extraction
[session_id_extract]
regex = "input":{[^}]*"session_id":"([^"]+)"
format = session_id::$1

# Request ID Extraction
[request_id_extract]
regex = "input":{[^}]*"request_id":"([^"]+)"
format = request_id::$1

# Service Name Extraction
[service_name_extract]
regex = "labels":{[^}]*"service":"([^"]+)"
format = service_name::$1

# Namespace Extraction
[namespace_extract]
regex = "labels":{[^}]*"namespace":"([^"]+)"
format = namespace::$1