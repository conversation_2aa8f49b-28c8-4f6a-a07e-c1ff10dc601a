# OPA Policy Audit & Compliance Add-on - Workflow Actions Configuration
# This file defines automated response actions for security incidents and operational issues

# ============================================================================
# SECURITY INCIDENT RESPONSE WORKFLOWS
# ============================================================================

[investigate_user_activity]
label = Investigate User Activity
fields = input_user
type = link
link.method = get
link.uri = /app/search/search?q=search%20index%3Dmain%20sourcetype%3D%22opa%3Adecision%22%20input_user%3D%22$input_user$%22%20%7C%20head%20100%20%7C%20table%20_time%2C%20input_user%2C%20policy_path%2C%20decision_result%2C%20input_remote_addr&earliest=-24h%40h&latest=now
link.target = blank
description = Investigate detailed activity for the selected user over the last 24 hours

[investigate_policy_violations]
label = Investigate Policy Violations
fields = policy_path
type = link
link.method = get
link.uri = /app/search/search?q=search%20index%3Dmain%20sourcetype%3D%22opa%3Adecision%22%20policy_path%3D%22$policy_path$%22%20decision_result%3D%22deny%22%20%7C%20head%20100%20%7C%20table%20_time%2C%20input_user%2C%20policy_path%2C%20input_remote_addr%2C%20decision_evaluation_duration_ns&earliest=-7d%40d&latest=now
link.target = blank
description = Investigate all violations for the selected policy over the last week

[investigate_ip_activity]
label = Investigate IP Address Activity
fields = input_remote_addr
type = link
link.method = get
link.uri = /app/search/search?q=search%20index%3Dmain%20sourcetype%3D%22opa%3Adecision%22%20input_remote_addr%3D%22$input_remote_addr$%22%20%7C%20head%20100%20%7C%20table%20_time%2C%20input_user%2C%20policy_path%2C%20decision_result%2C%20input_remote_addr&earliest=-24h%40h&latest=now
link.target = blank
description = Investigate all activity from the selected IP address over the last 24 hours

[create_security_incident]
label = Create Security Incident
fields = input_user, policy_path, input_remote_addr
type = link
link.method = post
link.uri = /app/search/incident_review?action=create&user=$input_user$&policy=$policy_path$&ip=$input_remote_addr$&time=$_time$
link.target = blank
description = Create a security incident ticket for investigation

[block_user_access]
label = Block User Access
fields = input_user
type = link
link.method = get
link.uri = /app/search/search?q=search%20index%3Dmain%20sourcetype%3D%22opa%3Adecision%22%20input_user%3D%22$input_user$%22%20%7C%20stats%20count%20by%20input_user%20%7C%20eval%20action%3D%22BLOCK_USER%22%20%7C%20collect%20index%3Dsecurity_actions
link.target = blank
description = Add user to security block list for immediate review

[escalate_to_security_team]
label = Escalate to Security Team
fields = input_user, policy_path, decision_result
type = link
link.method = post
link.uri = /app/search/alert_manager?action=escalate&user=$input_user$&policy=$policy_path$&result=$decision_result$&severity=high
link.target = blank
description = Escalate this event to the security team for immediate attention

# ============================================================================
# COMPLIANCE INVESTIGATION WORKFLOWS
# ============================================================================

[compliance_audit_trail]
label = Generate Compliance Audit Trail
fields = input_user, policy_path
type = link
link.method = get
link.uri = /app/search/search?q=search%20(index%3Dmain%20sourcetype%3D%22opa%3Adecision%22%20input_user%3D%22$input_user$%22)%20OR%20(index%3Dmain%20sourcetype%3D%22styra%3Aaudit%22%20user_email%3D%22$input_user$%22)%20%7C%20sort%20_time%20%7C%20table%20_time%2C%20sourcetype%2C%20input_user%2C%20user_email%2C%20policy_path%2C%20decision_result%2C%20change_type&earliest=-30d%40d&latest=now
link.target = blank
description = Generate comprehensive audit trail for compliance review

[sox_compliance_report]
label = SOX Compliance Report
fields = policy_path
type = link
link.method = get
link.uri = /app/search/search?q=search%20index%3Dmain%20sourcetype%3D%22opa%3Adecision%22%20%7C%20lookup%20policy_criticality%20policy_path%20OUTPUT%20compliance_framework%20%7C%20where%20compliance_framework%3D%22SOX%22%20%7C%20stats%20count(eval(decision_result%3D%22allow%22))%20as%20compliant%2C%20count(eval(decision_result%3D%22deny%22))%20as%20violations%20by%20policy_path&earliest=-30d%40d&latest=now
link.target = blank
description = Generate SOX compliance report for the selected policy

[pci_compliance_report]
label = PCI Compliance Report
fields = policy_path
type = link
link.method = get
link.uri = /app/search/search?q=search%20index%3Dmain%20sourcetype%3D%22opa%3Adecision%22%20%7C%20lookup%20policy_criticality%20policy_path%20OUTPUT%20compliance_framework%20%7C%20where%20compliance_framework%3D%22PCI-DSS%22%20%7C%20stats%20count(eval(decision_result%3D%22allow%22))%20as%20compliant%2C%20count(eval(decision_result%3D%22deny%22))%20as%20violations%20by%20policy_path&earliest=-30d%40d&latest=now
link.target = blank
description = Generate PCI-DSS compliance report for the selected policy

[hipaa_compliance_report]
label = HIPAA Compliance Report
fields = policy_path
type = link
link.method = get
link.uri = /app/search/search?q=search%20index%3Dmain%20sourcetype%3D%22opa%3Adecision%22%20%7C%20lookup%20policy_criticality%20policy_path%20OUTPUT%20compliance_framework%20%7C%20where%20compliance_framework%3D%22HIPAA%22%20%7C%20stats%20count(eval(decision_result%3D%22allow%22))%20as%20compliant%2C%20count(eval(decision_result%3D%22deny%22))%20as%20violations%20by%20policy_path&earliest=-30d%40d&latest=now
link.target = blank
description = Generate HIPAA compliance report for the selected policy

# ============================================================================
# OPERATIONAL RESPONSE WORKFLOWS
# ============================================================================

[investigate_opa_performance]
label = Investigate OPA Performance
fields = policy_path, decision_evaluation_duration_ns
type = link
link.method = get
link.uri = /app/search/search?q=search%20index%3Dmain%20sourcetype%3D%22opa%3Adecision%22%20policy_path%3D%22$policy_path$%22%20%7C%20eval%20response_time_ms%3Ddecision_evaluation_duration_ns%2F1000000%20%7C%20stats%20avg(response_time_ms)%20as%20avg_response%2C%20perc95(response_time_ms)%20as%20p95_response%2C%20count%20as%20total_requests%20by%20policy_path&earliest=-24h%40h&latest=now
link.target = blank
description = Analyze performance metrics for the selected policy

[check_opa_instance_health]
label = Check OPA Instance Health
fields = endpoint
type = link
link.method = get
link.uri = /app/search/search?q=search%20index%3Dmain%20sourcetype%3D%22opa%3Ahealth%22%20endpoint%3D%22$endpoint$%22%20%7C%20head%2010%20%7C%20table%20_time%2C%20endpoint%2C%20status%2C%20checks.bundle_status.status%2C%20endpoint_state.consecutive_failures&earliest=-1h%40h&latest=now
link.target = blank
description = Check current health status of the selected OPA instance

[restart_opa_instance]
label = Restart OPA Instance
fields = endpoint
type = link
link.method = post
link.uri = /app/search/ops_actions?action=restart&endpoint=$endpoint$&timestamp=$_time$
link.target = blank
description = Initiate restart procedure for the selected OPA instance

[scale_opa_resources]
label = Scale OPA Resources
fields = endpoint
type = link
link.method = post
link.uri = /app/search/ops_actions?action=scale&endpoint=$endpoint$&timestamp=$_time$
link.target = blank
description = Initiate resource scaling for the selected OPA instance

[update_opa_bundles]
label = Update OPA Bundles
fields = endpoint
type = link
link.method = post
link.uri = /app/search/ops_actions?action=update_bundles&endpoint=$endpoint$&timestamp=$_time$
link.target = blank
description = Force bundle update for the selected OPA instance

# ============================================================================
# POLICY MANAGEMENT WORKFLOWS
# ============================================================================

[review_policy_in_styra]
label = Review Policy in Styra DAS
fields = policy_path, system_name
type = link
link.method = get
link.uri = https://styra-das-instance.com/systems/$system_name$/policies/$policy_path$
link.target = blank
description = Open the selected policy in Styra DAS for review and editing

[policy_impact_analysis]
label = Policy Impact Analysis
fields = policy_path
type = link
link.method = get
link.uri = /app/search/search?q=search%20index%3Dmain%20sourcetype%3D%22opa%3Adecision%22%20policy_path%3D%22$policy_path$%22%20%7C%20stats%20count%20as%20total_evaluations%2C%20count(eval(decision_result%3D%22allow%22))%20as%20approvals%2C%20count(eval(decision_result%3D%22deny%22))%20as%20denials%2C%20dc(input_user)%20as%20unique_users%2C%20avg(decision_evaluation_duration_ns%2F1000000)%20as%20avg_response_time&earliest=-7d%40d&latest=now
link.target = blank
description = Analyze the impact and usage patterns of the selected policy

[policy_version_history]
label = Policy Version History
fields = policy_path, system_name
type = link
link.method = get
link.uri = /app/search/search?q=search%20index%3Dmain%20sourcetype%3D%22styra%3Aaudit%22%20policy_path%3D%22$policy_path$%22%20system_name%3D%22$system_name$%22%20%7C%20sort%20_time%20desc%20%7C%20table%20_time%2C%20user_email%2C%20change_type%2C%20description%2C%20version&earliest=-90d%40d&latest=now
link.target = blank
description = View version history and changes for the selected policy

[test_policy_changes]
label = Test Policy Changes
fields = policy_path, system_name
type = link
link.method = post
link.uri = /app/search/policy_testing?policy=$policy_path$&system=$system_name$&action=test
link.target = blank
description = Initiate policy testing workflow for the selected policy

# ============================================================================
# USER MANAGEMENT WORKFLOWS
# ============================================================================

[user_risk_assessment]
label = User Risk Assessment
fields = input_user
type = link
link.method = get
link.uri = /app/search/search?q=search%20index%3Dmain%20sourcetype%3D%22opa%3Adecision%22%20input_user%3D%22$input_user$%22%20%7C%20lookup%20user_info%20input_user%20OUTPUT%20department%2C%20role%2C%20risk_score%20%7C%20stats%20count%20as%20total_activity%2C%20count(eval(decision_result%3D%22deny%22))%20as%20violations%2C%20dc(policy_path)%20as%20unique_policies%2C%20dc(input_remote_addr)%20as%20unique_ips%2C%20values(department)%20as%20department%2C%20values(role)%20as%20role%2C%20values(risk_score)%20as%20user_risk_score&earliest=-30d%40d&latest=now
link.target = blank
description = Comprehensive risk assessment for the selected user

[user_access_review]
label = User Access Review
fields = input_user
type = link
link.method = get
link.uri = /app/search/search?q=search%20index%3Dmain%20sourcetype%3D%22opa%3Adecision%22%20input_user%3D%22$input_user$%22%20decision_result%3D%22allow%22%20%7C%20stats%20count%20by%20policy_path%20%7C%20sort%20-count%20%7C%20head%2020&earliest=-30d%40d&latest=now
link.target = blank
description = Review access patterns and permissions for the selected user

[update_user_clearance]
label = Update User Clearance
fields = input_user
type = link
link.method = post
link.uri = /app/search/user_management?action=update_clearance&user=$input_user$&timestamp=$_time$
link.target = blank
description = Initiate clearance level update for the selected user

[disable_user_account]
label = Disable User Account
fields = input_user
type = link
link.method = post
link.uri = /app/search/user_management?action=disable&user=$input_user$&timestamp=$_time$&reason=security_violation
link.target = blank
description = Disable user account due to security violations

# ============================================================================
# THREAT HUNTING WORKFLOWS
# ============================================================================

[hunt_lateral_movement]
label = Hunt Lateral Movement
fields = input_user
type = link
link.method = get
link.uri = /app/search/search?q=search%20index%3Dmain%20sourcetype%3D%22opa%3Adecision%22%20input_user%3D%22$input_user$%22%20%7C%20stats%20dc(input_remote_addr)%20as%20unique_ips%2C%20dc(policy_path)%20as%20unique_policies%2C%20count(eval(decision_result%3D%22deny%22))%20as%20denials%2C%20values(input_remote_addr)%20as%20source_ips%20by%20input_user%20%7C%20where%20unique_ips%20%3E%3D%203%20AND%20unique_policies%20%3E%3D%205&earliest=-24h%40h&latest=now
link.target = blank
description = Hunt for lateral movement indicators for the selected user

[hunt_privilege_escalation]
label = Hunt Privilege Escalation
fields = input_user
type = link
link.method = get
link.uri = /app/search/search?q=search%20index%3Dmain%20sourcetype%3D%22opa%3Adecision%22%20input_user%3D%22$input_user$%22%20decision_result%3D%22deny%22%20(policy_path%3D%22*admin*%22%20OR%20policy_path%3D%22*root*%22%20OR%20policy_path%3D%22*sudo*%22%20OR%20policy_path%3D%22*elevated*%22)%20%7C%20lookup%20user_info%20input_user%20OUTPUT%20clearance_level%20%7C%20where%20clearance_level%20!%3D%20%22high%22&earliest=-24h%40h&latest=now
link.target = blank
description = Hunt for privilege escalation attempts by the selected user

[hunt_data_exfiltration]
label = Hunt Data Exfiltration
fields = input_user
type = link
link.method = get
link.uri = /app/search/search?q=search%20index%3Dmain%20sourcetype%3D%22opa%3Adecision%22%20input_user%3D%22$input_user$%22%20(policy_path%3D%22*data*%22%20OR%20policy_path%3D%22*file*%22%20OR%20policy_path%3D%22*export*%22%20OR%20policy_path%3D%22*download*%22)%20%7C%20stats%20count%20as%20data_access_attempts%2C%20dc(policy_path)%20as%20unique_data_resources%20by%20input_user&earliest=-24h%40h&latest=now
link.target = blank
description = Hunt for potential data exfiltration patterns by the selected user

[hunt_account_compromise]
label = Hunt Account Compromise
fields = input_user
type = link
link.method = get
link.uri = /app/search/search?q=search%20index%3Dmain%20sourcetype%3D%22opa%3Adecision%22%20input_user%3D%22$input_user$%22%20%7C%20iplocation%20input_remote_addr%20%7C%20stats%20dc(Country)%20as%20countries%2C%20dc(input_remote_addr)%20as%20unique_ips%2C%20count(eval(decision_result%3D%22deny%22))%20as%20denials%2C%20values(Country)%20as%20access_countries%20by%20input_user%20%7C%20where%20countries%20%3E%3D%202%20OR%20denials%20%3E%3D%2010&earliest=-24h%40h&latest=now
link.target = blank
description = Hunt for account compromise indicators for the selected user

# ============================================================================
# REPORTING WORKFLOWS
# ============================================================================

[generate_executive_report]
label = Generate Executive Report
fields = *
type = link
link.method = get
link.uri = /app/search/search?q=search%20index%3Dmain%20(sourcetype%3D%22opa%3Adecision%22%20OR%20sourcetype%3D%22styra%3Aaudit%22)%20%7C%20eval%20event_type%3Dcase(sourcetype%3D%22opa%3Adecision%22%2C%20%22Policy%20Decision%22%2C%20sourcetype%3D%22styra%3Aaudit%22%2C%20%22Policy%20Change%22)%20%7C%20stats%20count%20by%20event_type%2C%20decision_result%2C%20change_type&earliest=-7d%40d&latest=now
link.target = blank
description = Generate executive summary report for the last week

[generate_security_metrics]
label = Generate Security Metrics
fields = *
type = link
link.method = get
link.uri = /app/search/search?q=search%20index%3Dmain%20sourcetype%3D%22opa%3Adecision%22%20decision_result%3D%22deny%22%20%7C%20lookup%20policy_criticality%20policy_path%20OUTPUT%20risk_score%20%7C%20eval%20risk_category%3Dcase(risk_score%20%3E%3D%208%2C%20%22Critical%22%2C%20risk_score%20%3E%3D%206%2C%20%22High%22%2C%20risk_score%20%3E%3D%204%2C%20%22Medium%22%2C%201%3D1%2C%20%22Low%22)%20%7C%20stats%20count%20by%20risk_category&earliest=-24h%40h&latest=now
link.target = blank
description = Generate security metrics dashboard for the last 24 hours

[generate_compliance_dashboard]
label = Generate Compliance Dashboard
fields = *
type = link
link.method = get
link.uri = /app/search/search?q=search%20index%3Dmain%20sourcetype%3D%22opa%3Adecision%22%20%7C%20lookup%20policy_criticality%20policy_path%20OUTPUT%20compliance_framework%20%7C%20where%20isnotnull(compliance_framework)%20%7C%20stats%20count(eval(decision_result%3D%22allow%22))%20as%20compliant%2C%20count(eval(decision_result%3D%22deny%22))%20as%20violations%20by%20compliance_framework&earliest=-30d%40d&latest=now
link.target = blank
description = Generate compliance dashboard for all frameworks

# ============================================================================
# INTEGRATION WORKFLOWS
# ============================================================================

[create_jira_ticket]
label = Create JIRA Ticket
fields = input_user, policy_path, decision_result
type = link
link.method = post
link.uri = /app/search/integrations/jira?action=create&user=$input_user$&policy=$policy_path$&result=$decision_result$&time=$_time$
link.target = blank
description = Create a JIRA ticket for this security event

[send_to_siem]
label = Send to SIEM
fields = *
type = link
link.method = post
link.uri = /app/search/integrations/siem?action=forward&event_data=$*$&timestamp=$_time$
link.target = blank
description = Forward this event to the external SIEM system

[notify_slack_channel]
label = Notify Slack Channel
fields = input_user, policy_path, decision_result
type = link
link.method = post
link.uri = /app/search/integrations/slack?channel=security-alerts&user=$input_user$&policy=$policy_path$&result=$decision_result$&time=$_time$
link.target = blank
description = Send notification to the security Slack channel

[update_threat_intel]
label = Update Threat Intelligence
fields = input_remote_addr, input_user
type = link
link.method = post
link.uri = /app/search/integrations/threat_intel?action=update&ip=$input_remote_addr$&user=$input_user$&timestamp=$_time$
link.target = blank
description = Update threat intelligence feeds with this event data