# Testing Dependencies for OPA Policy Audit & Compliance Add-on

# Core Testing Framework
unittest2>=1.1.0
pytest>=7.4.3
pytest-cov>=4.1.0
pytest-mock>=3.12.0
pytest-asyncio>=0.21.1
pytest-timeout>=2.2.0
pytest-xdist>=3.5.0

# Coverage Analysis
coverage>=7.3.2
coverage[toml]>=7.3.2

# Mocking and Test Utilities
mock>=5.1.0
responses>=0.24.1
factory-boy>=3.3.0
faker>=20.1.0

# HTTP Testing
httpretty>=1.1.4
requests-mock>=1.11.0

# Performance Testing
memory-profiler>=0.61.0
psutil>=5.9.6

# Security Testing
bandit>=1.7.5
safety>=2.3.5

# Code Quality
flake8>=6.1.0
pylint>=3.0.3
black>=23.11.0
isort>=5.12.0
mypy>=1.7.1

# Test Reporting
pytest-html>=4.1.1
pytest-json-report>=1.5.0
allure-pytest>=2.13.2

# Load Testing
locust>=2.17.0

# Documentation Testing
doctest>=1.0
sphinx>=7.2.6

# Async Testing Support
aioresponses>=0.7.4
pytest-asyncio>=0.21.1

# Database Testing (if needed)
sqlalchemy>=2.0.23
alembic>=1.13.0

# Time/Date Testing
freezegun>=1.2.2

# Environment Management
python-dotenv>=1.0.0

# Logging Testing
testfixtures>=7.2.2

# Network Testing
pyfakefs>=5.3.2

# Configuration Testing
configparser>=6.0.0
pyyaml>=6.0.1

# Splunk SDK (optional - will be mocked if not available)
# splunk-sdk>=1.7.4

# Development Dependencies
ipython>=8.17.2
jupyter>=1.0.0

# CI/CD Integration
tox>=4.11.4
pre-commit>=3.6.0

# Performance Profiling
py-spy>=0.3.14
line-profiler>=4.1.1

# Test Data Generation
mimesis>=11.1.0
hypothesis>=6.92.1

# API Testing
tavern>=2.4.1

# Container Testing
testcontainers>=3.7.1

# Parallel Testing
pytest-parallel>=0.1.1

# Test Fixtures
pytest-fixtures>=0.1.0

# Snapshot Testing
syrupy>=4.6.0

# Property-based Testing
hypothesis>=6.92.1

# Visual Testing (for UI components)
selenium>=4.15.2
playwright>=1.40.0

# API Documentation Testing
dredd_hooks>=0.2.0

# Contract Testing
pact-python>=2.0.1

# Chaos Engineering
chaostoolkit>=1.17.0

# Monitoring Testing
prometheus-client>=0.19.0

# Log Analysis Testing
loguru>=0.7.2

# JSON Schema Validation
jsonschema>=4.20.0

# XML Testing
lxml>=4.9.3

# CSV Testing
pandas>=2.1.4

# Regular Expression Testing
regex>=2023.10.3

# URL Testing
furl>=2.1.3

# Encryption Testing
cryptography>=41.0.7

# Compression Testing
gzip
zlib

# File System Testing
watchdog>=3.0.0

# Process Testing
subprocess32>=3.5.4; python_version < '3.8'

# Signal Testing
signal

# Threading Testing
threading

# Multiprocessing Testing
multiprocessing

# Socket Testing
socket

# SSL Testing
ssl

# HTTP Client Testing
urllib3>=2.1.0

# JSON Testing
json

# Base64 Testing
base64

# Hash Testing
hashlib

# UUID Testing
uuid

# Random Testing
random

# Math Testing
math

# Statistics Testing
statistics

# Collections Testing
collections

# Itertools Testing
itertools

# Functools Testing
functools

# Operator Testing
operator

# Copy Testing
copy

# Pickle Testing
pickle

# Shelve Testing
shelve

# DBM Testing
dbm

# SQLite Testing
sqlite3

# CSV Testing
csv

# ConfigParser Testing
configparser

# Logging Testing
logging

# Warnings Testing
warnings

# Traceback Testing
traceback

# Inspect Testing
inspect

# Dis Testing
dis

# AST Testing
ast

# Tokenize Testing
tokenize

# Keyword Testing
keyword

# Builtins Testing
builtins

# Sys Testing
sys

# OS Testing
os

# Platform Testing
platform

# Subprocess Testing
subprocess

# Shutil Testing
shutil

# Glob Testing
glob

# Fnmatch Testing
fnmatch

# Pathlib Testing
pathlib

# Tempfile Testing
tempfile

# IO Testing
io

# StringIO Testing
stringio

# BytesIO Testing
bytesio

# Contextlib Testing
contextlib

# Weakref Testing
weakref

# GC Testing
gc

# Types Testing
types

# Typing Testing
typing

# Dataclasses Testing
dataclasses

# Enum Testing
enum

# Decimal Testing
decimal

# Fractions Testing
fractions

# Numbers Testing
numbers

# Cmath Testing
cmath

# Array Testing
array

# Struct Testing
struct

# Codecs Testing
codecs

# Locale Testing
locale

# Calendar Testing
calendar

# Time Testing
time

# Datetime Testing
datetime

# ZoneInfo Testing
zoneinfo

# Re Testing
re

# String Testing
string

# Text Testing
text

# Unicodedata Testing
unicodedata

# Difflib Testing
difflib

# Textwrap Testing
textwrap

# Readline Testing
readline

# Rlcompleter Testing
rlcompleter

# Cmd Testing
cmd

# Shlex Testing
shlex

# Getopt Testing
getopt

# Argparse Testing
argparse

# Getpass Testing
getpass

# Curses Testing
curses

# Turtle Testing
turtle

# Tkinter Testing
tkinter

# IDLE Testing
idle

# Pydoc Testing
pydoc

# Doctest Testing
doctest

# Unittest Testing
unittest

# Test Testing
test

# BZ2 Testing
bz2

# LZMA Testing
lzma

# Zipfile Testing
zipfile

# Tarfile Testing
tarfile

# Gzip Testing
gzip

# Zlib Testing
zlib

# Hashlib Testing
hashlib

# HMAC Testing
hmac

# Secrets Testing
secrets

# Random Testing
random

# Statistics Testing
statistics

# Math Testing
math

# Cmath Testing
cmath

# Decimal Testing
decimal

# Fractions Testing
fractions

# Numbers Testing
numbers

# Array Testing
array

# Struct Testing
struct

# Codecs Testing
codecs

# Locale Testing
locale

# Calendar Testing
calendar

# Time Testing
time

# Datetime Testing
datetime

# ZoneInfo Testing
zoneinfo

# Re Testing
re

# String Testing
string

# Text Testing
text

# Unicodedata Testing
unicodedata

# Difflib Testing
difflib

# Textwrap Testing
textwrap