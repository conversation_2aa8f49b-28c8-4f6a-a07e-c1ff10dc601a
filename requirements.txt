# OPA Policy Audit & Compliance Add-on - Python Dependencies
# This file lists all required Python packages for the Splunk add-on

# Core Splunk SDK
splunk-sdk>=1.7.4

# HTTP Server and Client Libraries
requests>=2.31.0
requests-oauthlib>=1.3.1
urllib3>=2.0.7
httpx>=0.25.0

# JSON and Data Processing
jsonschema>=4.19.0
pyjson5>=1.6.4
python-dateutil>=2.8.2
pytz>=2023.3

# Compression and Encoding
gzip
base64
zlib

# Logging and Configuration
pyyaml>=6.0.1
toml>=0.10.2
configparser

# Security and Authentication
cryptography>=41.0.7
pyjwt>=2.8.0
passlib>=1.7.4

# Network and IP Processing
ipaddress
netaddr>=0.9.0
dnspython>=2.4.2

# Async and Threading
aiohttp>=3.9.0
aiofiles>=23.2.1
threading
asyncio

# Data Validation and Parsing
cerberus>=1.3.5
marshmallow>=3.20.1
validators>=0.22.0

# Metrics and Monitoring
prometheus-client>=0.18.0
psutil>=5.9.6

# Error Handling and Retry Logic
retrying>=1.3.4
backoff>=2.2.1
tenacity>=8.2.3

# Time and Date Utilities
arrow>=1.3.0
pendulum>=2.1.2

# File and Path Utilities
pathlib
os
sys
glob
shutil

# Regular Expressions and Text Processing
re
string
textwrap

# Mathematical and Statistical Operations
numpy>=1.24.4
scipy>=1.11.4
statistics

# Development and Testing (Optional)
pytest>=7.4.3
pytest-cov>=4.1.0
pytest-mock>=3.12.0
black>=23.11.0
flake8>=6.1.0
mypy>=1.7.0

# Documentation (Optional)
sphinx>=7.2.6
sphinx-rtd-theme>=1.3.0

# Performance Optimization
uvloop>=0.19.0; sys_platform != "win32"
cython>=3.0.5

# Memory Management
memory-profiler>=0.61.0
objgraph>=3.6.0

# Caching
cachetools>=5.3.2
redis>=5.0.1

# Database Connectivity (Optional)
sqlalchemy>=2.0.23
psycopg2-binary>=2.9.9
pymongo>=4.6.0

# Cloud and Container Support
boto3>=1.34.0
kubernetes>=28.1.0
docker>=6.1.3

# Monitoring and Observability
opentelemetry-api>=1.21.0
opentelemetry-sdk>=1.21.0
opentelemetry-instrumentation>=0.42b0

# Additional Utilities
click>=8.1.7
rich>=13.7.0
tqdm>=4.66.1
colorama>=0.4.6

# Version Constraints for Compatibility
# Ensure compatibility with Splunk's Python environment
setuptools>=68.0.0
wheel>=0.42.0
pip>=23.3.1

# Security Scanning and Vulnerability Management
safety>=2.3.5
bandit>=1.7.5

# Code Quality and Linting
pylint>=3.0.3
autopep8>=2.0.4
isort>=5.12.0

# Environment and Configuration Management
python-dotenv>=1.0.0
envparse>=0.2.0

# Serialization and Data Formats
msgpack>=1.0.7
avro-python3>=1.11.3
protobuf>=4.25.1

# Networking and Protocol Support
websockets>=12.0
grpcio>=1.59.3
grpcio-tools>=1.59.3

# Machine Learning and Analytics (Optional)
scikit-learn>=1.3.2
pandas>=2.1.4

# Geolocation and IP Intelligence
geoip2>=4.7.0
maxminddb>=2.2.0

# Template and Report Generation
jinja2>=3.1.2
reportlab>=4.0.7

# Email and Notification Support
smtplib
email

# System Integration
subprocess
signal
atexit

# Concurrency and Parallel Processing
concurrent.futures
multiprocessing
queue

# File Format Support
csv
json
xml.etree.ElementTree

# Cryptographic Operations
hashlib
hmac
secrets

# URL and Web Utilities
urllib.parse
urllib.request
http.client

# Data Structures and Collections
collections
itertools
functools
operator

# Type Hints and Annotations
typing
typing_extensions>=4.8.0

# Debugging and Profiling
pdb
cProfile
traceback
inspect

# Platform and System Information
platform
socket
uuid

# Compression and Archive Support
tarfile
zipfile
bz2
lzma

# Internationalization and Localization
locale
gettext

# Random and Cryptographic Random
random
secrets

# Mathematical Constants and Functions
math
decimal
fractions

# Binary Data and Encoding
struct
codecs
binascii

# Weak References and Garbage Collection
weakref
gc

# Context Management
contextlib

# Abstract Base Classes
abc

# Enum Support
enum

# Data Classes
dataclasses

# Warnings and Deprecation
warnings

# Copy Operations
copy

# Pickle Serialization
pickle

# Shell and Command Utilities
shlex

# Temporary Files and Directories
tempfile

# File Locking
fcntl; sys_platform != "win32"
msvcrt; sys_platform == "win32"

# Process and Signal Handling
signal
os

# Memory Mapping
mmap

# Select and Polling
select

# Socket Programming
socket
ssl

# Thread Local Storage
threading
_thread

# Asynchronous I/O
asyncio

# Coroutines and Generators
types

# Metaclasses and Dynamic Class Creation
type

# Attribute Access and Descriptors
getattr
setattr
hasattr
delattr

# Built-in Functions and Utilities
builtins

# Import System
importlib
pkgutil

# Site and User Customization
site
user

# Keyword Arguments
keyword

# Token and Tokenization
token
tokenize

# AST and Code Objects
ast
code

# Compilation and Execution
compile
exec
eval

# Garbage Collection
gc

# System-specific Parameters
sys

# Runtime Services
atexit
traceback
__future__

# Python Language Services
keyword
token
tokenize
ast
symtable
symbol
compiler

# Miscellaneous Services
formatter

# MS Windows Specific Services
winreg; sys_platform == "win32"
winsound; sys_platform == "win32"

# Unix Specific Services
posix; sys_platform != "win32"
pwd; sys_platform != "win32"
spwd; sys_platform != "win32"
grp; sys_platform != "win32"
crypt; sys_platform != "win32"
termios; sys_platform != "win32"
tty; sys_platform != "win32"
pty; sys_platform != "win32"
fcntl; sys_platform != "win32"
pipes; sys_platform != "win32"
resource; sys_platform != "win32"
nis; sys_platform != "win32"
syslog; sys_platform != "win32"