#!/usr/bin/env python3
"""
Setup script for OPA Policy Audit & Compliance Add-on for Splunk

This script handles the packaging and distribution of the Splunk add-on,
including dependency management, metadata configuration, and build processes.
"""

import os
import sys
from pathlib import Path
from setuptools import setup, find_packages

# Ensure we're using Python 3.7+
if sys.version_info < (3, 7):
    raise RuntimeError("This package requires Python 3.7 or later")

# Get the directory containing this script
HERE = Path(__file__).parent.absolute()

# Read the README file for long description
def read_readme():
    """Read and return the contents of README.md"""
    readme_path = HERE / "README.md"
    if readme_path.exists():
        with open(readme_path, "r", encoding="utf-8") as f:
            return f.read()
    return "OPA Policy Audit & Compliance Add-on for Splunk"

# Read the requirements file
def read_requirements(filename="requirements.txt"):
    """Read and return the contents of requirements file"""
    req_path = HERE / filename
    if req_path.exists():
        with open(req_path, "r", encoding="utf-8") as f:
            return [
                line.strip() 
                for line in f.readlines() 
                if line.strip() and not line.startswith("#")
            ]
    return []

# Get version from app.conf
def get_version():
    """Extract version from app.conf file"""
    app_conf_path = HERE / "app.conf"
    if app_conf_path.exists():
        with open(app_conf_path, "r", encoding="utf-8") as f:
            for line in f:
                if line.strip().startswith("version"):
                    return line.split("=")[1].strip()
    return "1.0.0"

# Package metadata
NAME = "opa-policy-audit-splunk-addon"
VERSION = get_version()
DESCRIPTION = "Comprehensive Splunk add-on for OPA policy audit and compliance monitoring"
LONG_DESCRIPTION = read_readme()
AUTHOR = "OPA Community"
AUTHOR_EMAIL = "<EMAIL>"
URL = "https://github.com/your-org/opa-splunk-addon"
LICENSE = "Apache License 2.0"

# Package requirements
INSTALL_REQUIRES = read_requirements("requirements.txt")
DEV_REQUIRES = read_requirements("requirements-dev.txt")
TEST_REQUIRES = [
    "pytest>=7.4.3",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "pytest-asyncio>=0.21.1",
    "pytest-timeout>=2.2.0",
    "pytest-xdist>=3.5.0",
    "coverage>=7.3.2",
    "mock>=5.1.0",
]

# Classifiers for PyPI
CLASSIFIERS = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: System Administrators",
    "Intended Audience :: Information Technology",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: System :: Monitoring",
    "Topic :: System :: Logging",
    "Topic :: Security",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: System :: Systems Administration",
    "Environment :: No Input/Output (Daemon)",
    "Framework :: Splunk",
]

# Keywords for search
KEYWORDS = [
    "splunk", "opa", "open-policy-agent", "policy", "audit", "compliance",
    "security", "monitoring", "logging", "styra", "das", "governance",
    "authorization", "rbac", "abac", "cim", "siem", "sox", "pci", "hipaa",
    "gdpr", "threat-detection", "anomaly-detection", "risk-assessment"
]

# Package data to include
PACKAGE_DATA = {
    "": [
        "*.conf",
        "*.xml",
        "*.csv",
        "*.py",
        "*.md",
        "*.txt",
        "*.json",
        "*.yaml",
        "*.yml",
        "LICENSE",
        "NOTICE",
        "CHANGELOG.md",
    ]
}

# Data files to include in distribution
DATA_FILES = [
    ("default", ["default/" + f for f in os.listdir("default") if f.endswith(".conf")]),
    ("lookups", ["lookups/" + f for f in os.listdir("lookups") if f.endswith(".csv")]),
    ("bin", ["bin/" + f for f in os.listdir("bin") if f.endswith(".py")]),
]

# Entry points for console scripts
ENTRY_POINTS = {
    "console_scripts": [
        "opa-splunk-test=bin.test_inputs:main",
        "opa-splunk-validate=bin.validate_config:main",
    ],
}

# Custom commands
class CustomCommand:
    """Base class for custom setup commands"""
    
    def __init__(self):
        self.here = HERE
    
    def run_command(self, cmd):
        """Run a shell command"""
        import subprocess
        try:
            result = subprocess.run(
                cmd, 
                shell=True, 
                check=True, 
                capture_output=True, 
                text=True,
                cwd=self.here
            )
            return result.stdout
        except subprocess.CalledProcessError as e:
            print(f"Command failed: {cmd}")
            print(f"Error: {e.stderr}")
            sys.exit(1)

class TestCommand(CustomCommand):
    """Custom test command"""
    
    def run(self):
        """Run tests"""
        print("Running tests...")
        self.run_command("python -m pytest tests/ -v --cov=bin --cov-report=html")
        print("Tests completed successfully!")

class LintCommand(CustomCommand):
    """Custom lint command"""
    
    def run(self):
        """Run linting"""
        print("Running linting...")
        self.run_command("python -m flake8 bin/ --max-line-length=88")
        self.run_command("python -m black bin/ --check")
        self.run_command("python -m mypy bin/ --ignore-missing-imports")
        print("Linting completed successfully!")

class FormatCommand(CustomCommand):
    """Custom format command"""
    
    def run(self):
        """Format code"""
        print("Formatting code...")
        self.run_command("python -m black bin/")
        self.run_command("python -m isort bin/")
        print("Code formatting completed!")

class SecurityCommand(CustomCommand):
    """Custom security check command"""
    
    def run(self):
        """Run security checks"""
        print("Running security checks...")
        self.run_command("python -m safety check")
        self.run_command("python -m bandit -r bin/ -f json -o security-report.json")
        print("Security checks completed!")

class BuildSplunkAppCommand(CustomCommand):
    """Custom command to build Splunk app package"""
    
    def run(self):
        """Build Splunk app package"""
        import shutil
        import tarfile
        from datetime import datetime
        
        print("Building Splunk app package...")
        
        # Create build directory
        build_dir = self.here / "build" / "splunk-app"
        if build_dir.exists():
            shutil.rmtree(build_dir)
        build_dir.mkdir(parents=True)
        
        # Copy necessary files
        files_to_copy = [
            "app.conf",
            "default/",
            "bin/",
            "lookups/",
            "README.md",
            "LICENSE",
            "requirements.txt",
        ]
        
        for item in files_to_copy:
            src = self.here / item
            dst = build_dir / item
            if src.is_file():
                shutil.copy2(src, dst)
            elif src.is_dir():
                shutil.copytree(src, dst)
        
        # Create tarball
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        tarball_name = f"opa_policy_audit_addon_{VERSION}_{timestamp}.tar.gz"
        tarball_path = self.here / "dist" / tarball_name
        
        # Ensure dist directory exists
        tarball_path.parent.mkdir(exist_ok=True)
        
        with tarfile.open(tarball_path, "w:gz") as tar:
            tar.add(build_dir, arcname="opa_policy_audit_addon")
        
        print(f"Splunk app package created: {tarball_path}")
        print(f"Package size: {tarball_path.stat().st_size / 1024 / 1024:.2f} MB")

# Custom command mapping
CUSTOM_COMMANDS = {
    "test": TestCommand,
    "lint": LintCommand,
    "format": FormatCommand,
    "security": SecurityCommand,
    "build_splunk_app": BuildSplunkAppCommand,
}

# Handle custom commands
if len(sys.argv) > 1 and sys.argv[1] in CUSTOM_COMMANDS:
    command_class = CUSTOM_COMMANDS[sys.argv[1]]
    command = command_class()
    command.run()
    sys.exit(0)

# Setup configuration
setup(
    name=NAME,
    version=VERSION,
    description=DESCRIPTION,
    long_description=LONG_DESCRIPTION,
    long_description_content_type="text/markdown",
    author=AUTHOR,
    author_email=AUTHOR_EMAIL,
    url=URL,
    license=LICENSE,
    classifiers=CLASSIFIERS,
    keywords=" ".join(KEYWORDS),
    
    # Package discovery
    packages=find_packages(exclude=["tests", "tests.*", "build", "dist"]),
    package_data=PACKAGE_DATA,
    data_files=DATA_FILES,
    include_package_data=True,
    zip_safe=False,
    
    # Dependencies
    python_requires=">=3.7",
    install_requires=INSTALL_REQUIRES,
    extras_require={
        "dev": DEV_REQUIRES,
        "test": TEST_REQUIRES,
        "all": DEV_REQUIRES + TEST_REQUIRES,
    },
    
    # Entry points
    entry_points=ENTRY_POINTS,
    
    # Project URLs
    project_urls={
        "Documentation": "https://docs.example.com/opa-splunk-addon",
        "Source": "https://github.com/your-org/opa-splunk-addon",
        "Tracker": "https://github.com/your-org/opa-splunk-addon/issues",
        "Changelog": "https://github.com/your-org/opa-splunk-addon/blob/main/CHANGELOG.md",
        "Funding": "https://github.com/sponsors/your-org",
        "Say Thanks!": "https://saythanks.io/to/your-org",
    },
    
    # Additional metadata
    platforms=["any"],
    maintainer=AUTHOR,
    maintainer_email=AUTHOR_EMAIL,
    download_url=f"{URL}/archive/v{VERSION}.tar.gz",
    
    # Options
    options={
        "build_scripts": {
            "executable": "/usr/bin/env python3",
        },
        "egg_info": {
            "tag_build": "",
            "tag_date": False,
        },
    },
)

# Post-setup information
if __name__ == "__main__":
    print("\n" + "="*60)
    print(f"OPA Policy Audit & Compliance Add-on v{VERSION}")
    print("="*60)
    print("Setup completed successfully!")
    print("\nNext steps:")
    print("1. Install in development mode: pip install -e .")
    print("2. Run tests: python setup.py test")
    print("3. Build Splunk package: python setup.py build_splunk_app")
    print("4. Deploy to Splunk: Copy to $SPLUNK_HOME/etc/apps/")
    print("\nFor more information, see README.md")
    print("="*60)