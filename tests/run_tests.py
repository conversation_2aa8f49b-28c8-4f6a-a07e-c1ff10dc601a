#!/usr/bin/env python3
"""
Test Runner for OPA Policy Audit & Compliance Add-on

This script runs all unit tests for the Splunk add-on and generates
a comprehensive test report.

Usage:
    python run_tests.py [--verbose] [--coverage] [--module MODULE_NAME]

Author: OPA Community
Version: 1.0.0
"""

import unittest
import sys
import os
import argparse
import time
import importlib
import traceback
from io import StringIO
from unittest.mock import Mock, MagicMock

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'bin'))
sys.path.insert(0, os.path.join(project_root, 'tests'))

# Mock Splunk SDK if not available
try:
    import splunklib
except ImportError:
    print("⚠ Splunk SDK not available - creating mocks")
    # Create mock splunklib module
    mock_splunklib = MagicMock()
    mock_splunklib.modularinput.Script = MagicMock
    mock_splunklib.modularinput.Scheme = MagicMock
    mock_splunklib.modularinput.Argument = MagicMock
    mock_splunklib.modularinput.Argument.data_type_string = "string"
    mock_splunklib.modularinput.Argument.data_type_number = "number"
    mock_splunklib.modularinput.Argument.data_type_boolean = "boolean"
    mock_splunklib.modularinput.event_writer.EventWriter = MagicMock
    sys.modules['splunklib'] = mock_splunklib
    sys.modules['splunklib.modularinput'] = mock_splunklib.modularinput
    sys.modules['splunklib.modularinput.event_writer'] = mock_splunklib.modularinput.event_writer

# Mock Splunk admin if not available
try:
    import splunk.admin
except ImportError:
    print("⚠ Splunk admin module not available - creating mocks")
    mock_splunk = MagicMock()
    mock_splunk.admin.MConfigHandler = MagicMock
    mock_splunk.entity = MagicMock()
    sys.modules['splunk'] = mock_splunk
    sys.modules['splunk.admin'] = mock_splunk.admin
    sys.modules['splunk.entity'] = mock_splunk.entity
    sys.modules['splunk.appserver'] = MagicMock()
    sys.modules['splunk.appserver.mrsparkle'] = MagicMock()
    sys.modules['splunk.appserver.mrsparkle.lib'] = MagicMock()
    sys.modules['splunk.appserver.mrsparkle.lib.util'] = MagicMock()

# Test modules
TEST_MODULES = [
    'test_opa_decision_logs',
    'test_styra_das_audit',
    'test_rest_handler'
]

class ColoredTextTestResult(unittest.TextTestResult):
    """
    Custom test result class with colored output.
    """
    
    def __init__(self, stream, descriptions, verbosity):
        super().__init__(stream, descriptions, verbosity)
        self.success_count = 0
        self.start_time = None
        self.verbosity = verbosity  # Store verbosity for later use
        
        # ANSI color codes
        self.colors = {
            'green': '\033[92m',
            'red': '\033[91m',
            'yellow': '\033[93m',
            'blue': '\033[94m',
            'purple': '\033[95m',
            'cyan': '\033[96m',
            'white': '\033[97m',
            'bold': '\033[1m',
            'underline': '\033[4m',
            'end': '\033[0m'
        }
    
    def startTest(self, test):
        super().startTest(test)
        if self.verbosity > 1:
            self.stream.write(f"  {test._testMethodName} ... ")
            self.stream.flush()
    
    def addSuccess(self, test):
        super().addSuccess(test)
        self.success_count += 1
        if self.verbosity > 1:
            self.stream.write(f"{self.colors['green']}OK{self.colors['end']}\n")
        elif self.verbosity == 1:
            self.stream.write('.')
        self.stream.flush()
    
    def addError(self, test, err):
        super().addError(test, err)
        if self.verbosity > 1:
            self.stream.write(f"{self.colors['red']}ERROR{self.colors['end']}\n")
        elif self.verbosity == 1:
            self.stream.write('E')
        self.stream.flush()
    
    def addFailure(self, test, err):
        super().addFailure(test, err)
        if self.verbosity > 1:
            self.stream.write(f"{self.colors['red']}FAIL{self.colors['end']}\n")
        elif self.verbosity == 1:
            self.stream.write('F')
        self.stream.flush()
    
    def addSkip(self, test, reason):
        super().addSkip(test, reason)
        if self.verbosity > 1:
            self.stream.write(f"{self.colors['yellow']}SKIP{self.colors['end']} ({reason})\n")
        elif self.verbosity == 1:
            self.stream.write('s')
        self.stream.flush()
    
    def startTestRun(self):
        super().startTestRun()
        self.start_time = time.time()
        self.stream.write(f"{self.colors['bold']}{self.colors['blue']}")
        self.stream.write("=" * 70 + "\n")
        self.stream.write("OPA Policy Audit & Compliance Add-on - Test Suite\n")
        self.stream.write("=" * 70 + "\n")
        self.stream.write(f"{self.colors['end']}\n")
    
    def stopTestRun(self):
        super().stopTestRun()
        end_time = time.time()
        duration = end_time - self.start_time
        
        self.stream.write("\n")
        self.stream.write(f"{self.colors['bold']}{self.colors['blue']}")
        self.stream.write("=" * 70 + "\n")
        self.stream.write("Test Results Summary\n")
        self.stream.write("=" * 70 + "\n")
        self.stream.write(f"{self.colors['end']}")
        
        total_tests = self.testsRun
        
        # Success rate
        if total_tests > 0:
            success_rate = (self.success_count / total_tests) * 100
        else:
            success_rate = 0
        
        # Results summary
        self.stream.write(f"Tests run: {self.colors['bold']}{total_tests}{self.colors['end']}\n")
        self.stream.write(f"Successes: {self.colors['green']}{self.success_count}{self.colors['end']}\n")
        self.stream.write(f"Failures: {self.colors['red']}{len(self.failures)}{self.colors['end']}\n")
        self.stream.write(f"Errors: {self.colors['red']}{len(self.errors)}{self.colors['end']}\n")
        self.stream.write(f"Skipped: {self.colors['yellow']}{len(self.skipped)}{self.colors['end']}\n")
        self.stream.write(f"Success rate: {self.colors['bold']}{success_rate:.1f}%{self.colors['end']}\n")
        self.stream.write(f"Duration: {self.colors['bold']}{duration:.2f}s{self.colors['end']}\n")
        
        # Overall result
        if len(self.failures) == 0 and len(self.errors) == 0:
            self.stream.write(f"\n{self.colors['bold']}{self.colors['green']}")
            self.stream.write("✓ ALL TESTS PASSED\n")
            self.stream.write(f"{self.colors['end']}")
        else:
            self.stream.write(f"\n{self.colors['bold']}{self.colors['red']}")
            self.stream.write("✗ SOME TESTS FAILED\n")
            self.stream.write(f"{self.colors['end']}")
        
        self.stream.write("\n")

class ColoredTextTestRunner(unittest.TextTestRunner):
    """
    Custom test runner with colored output.
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.resultclass = ColoredTextTestResult

def discover_tests(test_dir, pattern='test_*.py'):
    """
    Discover all test modules in the test directory.
    """
    loader = unittest.TestLoader()
    suite = loader.discover(test_dir, pattern=pattern)
    return suite

def load_specific_module(module_name):
    """
    Load a specific test module.
    """
    try:
        loader = unittest.TestLoader()
        module = __import__(module_name)
        suite = loader.loadTestsFromModule(module)
        return suite
    except ImportError as e:
        print(f"Error loading test module '{module_name}': {e}")
        return unittest.TestSuite()

def run_coverage_analysis():
    """
    Run coverage analysis if coverage.py is available.
    """
    try:
        import coverage
        
        # Initialize coverage
        cov = coverage.Coverage(source=['bin'])
        cov.start()
        
        return cov
    except ImportError:
        print("Warning: coverage.py not available. Install with: pip install coverage")
        return None

def generate_coverage_report(cov):
    """
    Generate coverage report.
    """
    if cov is None:
        return
    
    cov.stop()
    cov.save()
    
    print("\n" + "=" * 70)
    print("Coverage Report")
    print("=" * 70)
    
    # Console report
    cov.report()
    
    # HTML report
    try:
        html_dir = os.path.join(os.path.dirname(__file__), 'coverage_html')
        cov.html_report(directory=html_dir)
        print(f"\nHTML coverage report generated: {html_dir}/index.html")
    except Exception as e:
        print(f"Warning: Could not generate HTML report: {e}")

def check_dependencies():
    """
    Check if required dependencies are available.
    """
    missing_deps = []
    
    # Check for Splunk SDK
    try:
        import splunklib
    except ImportError:
        missing_deps.append('splunk-sdk')
    
    # Check for requests
    try:
        import requests
    except ImportError:
        missing_deps.append('requests')
    
    # Check for jsonschema
    try:
        import jsonschema
    except ImportError:
        missing_deps.append('jsonschema')
    
    if missing_deps:
        print("Warning: Missing dependencies for full testing:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("Some tests may be skipped.\n")

def discover_and_load_tests(args):
    """
    Discover and load tests with enhanced error handling.
    
    Args:
        args: Parsed command line arguments
    
    Returns:
        tuple: (test_suite, load_summary)
    """
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    load_summary = {
        'loaded': [],
        'failed': [],
        'skipped': [],
        'total_tests': 0
    }
    
    # Determine which modules to load
    modules_to_load = [args.module] if args.module else TEST_MODULES
    
    for module_name in modules_to_load:
        if args.module and module_name not in TEST_MODULES:
            print(f"❌ Unknown module: {module_name}")
            print(f"Available modules: {', '.join(TEST_MODULES)}")
            load_summary['failed'].append((module_name, "Unknown module"))
            continue
        
        try:
            # Import the module
            module = importlib.import_module(module_name)
            
            # Load tests from the module
            module_suite = loader.loadTestsFromModule(module)
            test_count = module_suite.countTestCases()
            
            if test_count > 0:
                suite.addTest(module_suite)
                load_summary['loaded'].append((module_name, test_count))
                load_summary['total_tests'] += test_count
                
                if args.verbose:
                    print(f"✓ Loaded {module_name}: {test_count} tests")
            else:
                load_summary['skipped'].append((module_name, "No tests found"))
                if args.verbose:
                    print(f"⚠ Skipped {module_name}: No tests found")
                    
        except ImportError as e:
            error_msg = str(e)
            load_summary['failed'].append((module_name, error_msg))
            
            if args.verbose:
                print(f"⚠ Failed to import {module_name}: {error_msg}")
                print(f"  Traceback: {traceback.format_exc()}")
            
            # Create a placeholder test that documents the import failure
            class ImportFailureTest(unittest.TestCase):
                def test_import_failure(self):
                    self.skipTest(f"Module {module_name} failed to import: {error_msg}")
            
            failure_suite = loader.loadTestsFromTestCase(ImportFailureTest)
            suite.addTest(failure_suite)
            load_summary['total_tests'] += 1
            
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            load_summary['failed'].append((module_name, error_msg))
            
            if args.verbose:
                print(f"❌ Unexpected error loading {module_name}: {error_msg}")
                print(f"  Traceback: {traceback.format_exc()}")
    
    return suite, load_summary

def run_tests(args):
    """
    Main test runner function with enhanced reporting.
    
    Args:
        args: Parsed command line arguments
    
    Returns:
        bool: True if all tests passed, False otherwise
    """
    print("\n" + "="*70)
    print("🧪 OPA Policy Audit & Compliance Add-on - Test Suite")
    print("="*70)
    print()
    
    # Discover and load tests
    suite, load_summary = discover_and_load_tests(args)
    
    # Print load summary
    print("📋 Test Discovery Summary:")
    print(f"  • Modules loaded: {len(load_summary['loaded'])}")
    print(f"  • Modules failed: {len(load_summary['failed'])}")
    print(f"  • Modules skipped: {len(load_summary['skipped'])}")
    print(f"  • Total tests found: {load_summary['total_tests']}")
    print()
    
    if args.verbose and load_summary['loaded']:
        print("✅ Successfully loaded modules:")
        for module_name, test_count in load_summary['loaded']:
            print(f"  • {module_name}: {test_count} tests")
        print()
    
    if load_summary['failed']:
        print("⚠️  Failed to load modules:")
        for module_name, error in load_summary['failed']:
            print(f"  • {module_name}: {error}")
        print()
    
    if load_summary['skipped']:
        print("⏭️  Skipped modules:")
        for module_name, reason in load_summary['skipped']:
            print(f"  • {module_name}: {reason}")
        print()
    
    if suite.countTestCases() == 0:
        print("❌ No tests found to run.")
        return False
    
    # Create test runner
    verbosity = 2 if args.verbose else 1
    
    # Capture output for analysis
    stream = StringIO()
    runner = unittest.TextTestRunner(
        stream=stream,
        verbosity=verbosity,
        resultclass=ColoredTextTestResult
    )
    
    # Run tests
    print("🚀 Running tests...")
    print("-" * 70)
    start_time = time.time()
    result = runner.run(suite)
    end_time = time.time()
    
    # Print captured output
    output = stream.getvalue()
    if output.strip():
        print(output)
    
    # Calculate metrics
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    skipped = len(getattr(result, 'skipped', []))
    successes = total_tests - failures - errors - skipped
    duration = end_time - start_time
    
    # Print comprehensive summary
    print("\n" + "="*70)
    print("📊 Test Results Summary")
    print("="*70)
    
    print(f"📈 Execution Metrics:")
    print(f"  • Tests run: {total_tests}")
    print(f"  • Successes: {successes} ✅")
    print(f"  • Failures: {failures} ❌")
    print(f"  • Errors: {errors} 💥")
    print(f"  • Skipped: {skipped} ⏭️")
    
    if total_tests > 0:
        success_rate = (successes / total_tests) * 100
        print(f"  • Success rate: {success_rate:.1f}%")
    else:
        success_rate = 0.0
        print(f"  • Success rate: 0.0%")
    
    print(f"  • Duration: {duration:.2f}s")
    
    # Performance analysis
    if total_tests > 0:
        avg_time = duration / total_tests
        print(f"  • Average time per test: {avg_time:.3f}s")
        
        if avg_time > 1.0:
            print(f"  ⚠️  Tests are running slowly (>{avg_time:.1f}s per test)")
        elif avg_time < 0.1:
            print(f"  🚀 Tests are running fast (<{avg_time:.3f}s per test)")
    
    # Detailed failure reporting
    if failures or errors:
        print("\n" + "="*70)
        print("🔍 Detailed Failure Analysis")
        print("="*70)
        
        if failures:
            print("\n❌ Test Failures:")
            for i, (test, traceback_str) in enumerate(result.failures, 1):
                print(f"\n{i}. {test}")
                print("-" * 50)
                print(traceback_str)
        
        if errors:
            print("\n💥 Test Errors:")
            for i, (test, traceback_str) in enumerate(result.errors, 1):
                print(f"\n{i}. {test}")
                print("-" * 50)
                print(traceback_str)
    
    # Coverage analysis
    if args.coverage:
        print("\n" + "="*70)
        print("📊 Coverage Analysis")
        print("="*70)
        try:
            import coverage
            print("Running coverage analysis...")
            # Note: Coverage analysis would need to be integrated differently
            # for proper measurement during test execution
            print("Coverage analysis requires separate execution with coverage.py")
            print("Run: coverage run tests/run_tests.py && coverage report")
        except ImportError:
            print("❌ Coverage module not available.")
            print("Install with: pip install coverage")
    
    # Final result
    success = (failures == 0 and errors == 0)
    
    print("\n" + "="*70)
    if success:
        if successes == total_tests:
            print("🎉 ALL TESTS PASSED! 🎉")
        else:
            print(f"✅ {successes}/{total_tests} TESTS PASSED (some skipped)")
    else:
        print(f"❌ {failures + errors}/{total_tests} TESTS FAILED")
        if successes > 0:
            print(f"✅ {successes} tests still passed")
    
    print("="*70)
    
    return success

def main():
    """
    Main test runner function.
    """
    parser = argparse.ArgumentParser(
        description='Run unit tests for OPA Policy Audit & Compliance Add-on'
    )
    parser.add_argument(
        '--verbose', '-v',
        action='count',
        default=1,
        help='Increase verbosity (use -vv for more verbose output)'
    )
    parser.add_argument(
        '--coverage', '-c',
        action='store_true',
        help='Run with coverage analysis'
    )
    parser.add_argument(
        '--module', '-m',
        help='Run tests for specific module only'
    )
    parser.add_argument(
        '--pattern', '-p',
        default='test_*.py',
        help='Test file pattern (default: test_*.py)'
    )
    parser.add_argument(
        '--failfast', '-f',
        action='store_true',
        help='Stop on first failure'
    )
    
    args = parser.parse_args()
    
    # Check dependencies
    check_dependencies()
    
    # Initialize coverage if requested
    cov = None
    if args.coverage:
        cov = run_coverage_analysis()
    
    # Run tests with enhanced functionality
    success = run_tests(args)
    
    # Generate coverage report if requested
    if args.coverage:
        generate_coverage_report(cov)
    
    # Exit with appropriate code
    if success:
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == '__main__':
    main()