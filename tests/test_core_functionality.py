#!/usr/bin/env python3
"""
Core Functionality Tests - OPA Policy Audit & Compliance Add-on

Tests the essential functionality that must work for submission:
1. HTTP server starts and accepts requests
2. Configuration validation works
3. Events are properly written to Splunk
4. Basic error handling

Author: OPA Community
Version: 1.0.0
"""

import unittest
import json
import time
import threading
import requests
from unittest.mock import Mock, MagicMock, patch
import sys
import os

# Add project paths
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(project_root, 'bin'))

# Import our modules
from opa_decision_logs import OPADecisionLogsInput, OPAHTTPServer
from styra_das_audit import StyraDASAuditInput
from opa_addon_setup_rh import OPAAddonSetupHandler


class TestCoreHTTPFunctionality(unittest.TestCase):
    """Test core HTTP server functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.config = {
            'http_port': 18088,  # Use different port for testing
            'http_path': '/opa/logs',
            'ssl_enabled': False,
            'log_level': 'INFO'
        }
        self.mock_ew = Mock()
        self.mock_logger = Mock()
        self.server = None
        
    def tearDown(self):
        """Clean up after tests"""
        if self.server:
            self.server.stop()
            time.sleep(0.1)  # Allow server to stop
    
    def test_http_server_starts(self):
        """Test that HTTP server starts successfully"""
        self.server = OPAHTTPServer(self.config, self.mock_ew, self.mock_logger)
        
        # Start server in background thread
        server_thread = threading.Thread(target=self.server.start)
        server_thread.daemon = True
        server_thread.start()
        
        # Give server time to start
        time.sleep(0.5)
        
        # Test that server is responding
        try:
            response = requests.get(f"http://localhost:{self.config['http_port']}/health", timeout=2)
            self.assertEqual(response.status_code, 200)
        except requests.exceptions.RequestException:
            self.fail("HTTP server failed to start or respond")
    
    def test_decision_log_processing(self):
        """Test that decision logs are processed correctly"""
        self.server = OPAHTTPServer(self.config, self.mock_ew, self.mock_logger)
        
        # Start server in background thread
        server_thread = threading.Thread(target=self.server.start)
        server_thread.daemon = True
        server_thread.start()
        time.sleep(0.5)
        
        # Send test decision log
        test_log = {
            'decision_id': 'test-123',
            'timestamp': '2024-01-01T12:00:00.000Z',
            'path': '/api/v1/data/authz/allow',
            'result': True,
            'input': {'user': 'alice', 'action': 'read'}
        }
        
        try:
            response = requests.post(
                f"http://localhost:{self.config['http_port']}{self.config['http_path']}",
                json=test_log,
                timeout=2
            )
            self.assertEqual(response.status_code, 200)
            
            # Verify event was written (mock was called)
            time.sleep(0.1)  # Allow processing
            self.mock_ew.write_event.assert_called()
            
        except requests.exceptions.RequestException as e:
            self.fail(f"Failed to send decision log: {e}")


class TestConfigurationValidation(unittest.TestCase):
    """Test configuration validation"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.input_handler = OPADecisionLogsInput()
        self.mock_validation = Mock()
        self.mock_validation.parameters = {}
    
    def test_valid_configuration(self):
        """Test validation with valid configuration"""
        self.mock_validation.parameters = {
            'http_port': '8088',
            'http_path': '/opa/logs',
            'ssl_enabled': '0',
            'log_level': 'INFO'
        }
        
        # Should not raise exception
        try:
            self.input_handler.validate_input(self.mock_validation)
        except Exception as e:
            self.fail(f"Valid configuration failed validation: {e}")
    
    def test_invalid_port(self):
        """Test validation with invalid port"""
        self.mock_validation.parameters = {
            'http_port': 'invalid',
            'http_path': '/opa/logs',
            'ssl_enabled': '0',
            'log_level': 'INFO'
        }
        
        with self.assertRaises(Exception):
            self.input_handler.validate_input(self.mock_validation)


class TestStyraDASIntegration(unittest.TestCase):
    """Test Styra DAS integration functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.input_handler = StyraDASAuditInput()
        self.mock_validation = Mock()
        self.mock_validation.parameters = {}
    
    def test_configuration_validation(self):
        """Test Styra DAS configuration validation"""
        self.mock_validation.parameters = {
            'api_endpoint': 'https://test.styra.com/v1/systems/test/audits',
            'api_token': 'test-token',
            'polling_interval': '300',
            'log_level': 'INFO'
        }
        
        # Mock the API connection test to avoid real network calls
        with patch.object(self.input_handler, 'test_api_connection') as mock_test:
            mock_test.return_value = True
            
            try:
                self.input_handler.validate_input(self.mock_validation)
            except Exception as e:
                self.fail(f"Valid Styra DAS configuration failed validation: {e}")


class TestRESTHandler(unittest.TestCase):
    """Test REST handler functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.handler = OPAAddonSetupHandler()
    
    def test_handler_initialization(self):
        """Test REST handler initializes correctly"""
        self.assertIsNotNone(self.handler)
        # Basic initialization test - handler should exist
        self.assertTrue(hasattr(self.handler, 'handleList'))
        self.assertTrue(hasattr(self.handler, 'handleEdit'))


class TestEventWriting(unittest.TestCase):
    """Test event writing functionality"""
    
    def test_event_enrichment(self):
        """Test that events are enriched with required fields"""
        config = {
            'http_port': 8088,
            'http_path': '/opa/logs',
            'ssl_enabled': False,
            'log_level': 'INFO'
        }
        mock_ew = Mock()
        mock_logger = Mock()
        
        server = OPAHTTPServer(config, mock_ew, mock_logger)
        handler_class = server.create_request_handler()
        handler = handler_class()
        
        test_log = {
            'decision_id': 'test-123',
            'timestamp': '2024-01-01T12:00:00.000Z',
            'path': '/api/v1/data/authz/allow',
            'result': True
        }
        
        enriched = handler.enrich_decision_log(test_log)
        
        # Check that enrichment adds required fields
        self.assertIn('decision_result', enriched)
        self.assertIn('policy_name', enriched)
        self.assertIn('ingestion_timestamp', enriched)


if __name__ == '__main__':
    unittest.main()
