#!/usr/bin/env python3
"""
Unit Tests for OPA Decision Logs Modular Input

This module contains comprehensive unit tests for the OPA decision logs
modular input, including validation, HTTP server functionality, and
event processing.

Author: OPA Community
Version: 1.0.0
"""

import unittest
import sys
import os
import json
import tempfile
import threading
import time
import requests
from unittest.mock import Mock, patch, MagicMock
from io import StringIO

# Add the bin directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'bin'))

try:
    from opa_decision_logs import OPADecisionLogsInput, OPAHTTPServer
except ImportError as e:
    print(f"Warning: Could not import OPA decision logs module: {e}")
    OPADecisionLogsInput = None
    OPAHTTPServer = None

class TestOPADecisionLogsInput(unittest.TestCase):
    """
    Test cases for OPA Decision Logs Input validation and configuration.
    """
    
    def setUp(self):
        """Set up test fixtures."""
        if OPADecisionLogsInput is None:
            self.skipTest("OPA decision logs module not available")
        
        self.input_handler = OPADecisionLogsInput()
        
        # Mock validation definition
        self.mock_validation = Mock()
        self.mock_validation.parameters = {
            'http_port': '8088',
            'http_path': '/opadecisions',
            'ssl_enabled': '0',
            'max_content_length': '10485760',
            'max_connections': '100',
            'buffer_size': '8192',
            'timeout': '30',
            'log_level': 'INFO'
        }
    
    def test_get_scheme(self):
        """Test input scheme definition."""
        scheme = self.input_handler.get_scheme()
        
        self.assertEqual(scheme.title, "OPA Decision Logs HTTP Listener")
        self.assertTrue(scheme.use_external_validation)
        self.assertFalse(scheme.use_single_instance)
        
        # Check required arguments
        arg_names = [arg.name for arg in scheme.args]
        required_args = ['http_port', 'http_path']
        
        for arg in required_args:
            self.assertIn(arg, arg_names)
    
    def test_validate_input_valid_config(self):
        """Test validation with valid configuration."""
        try:
            self.input_handler.validate_input(self.mock_validation)
        except Exception as e:
            self.fail(f"Validation failed with valid config: {e}")
    
    def test_validate_input_invalid_port(self):
        """Test validation with invalid port number."""
        self.mock_validation.parameters['http_port'] = '70000'
        
        with self.assertRaises(Exception) as context:
            self.input_handler.validate_input(self.mock_validation)
        
        self.assertIn('port must be between', str(context.exception))
    
    def test_validate_input_invalid_path(self):
        """Test validation with invalid HTTP path."""
        self.mock_validation.parameters['http_path'] = 'invalid-path'
        
        with self.assertRaises(Exception) as context:
            self.input_handler.validate_input(self.mock_validation)
        
        self.assertIn('path must start with', str(context.exception))
    
    def test_validate_input_ssl_missing_cert(self):
        """Test validation with SSL enabled but missing certificate."""
        self.mock_validation.parameters['ssl_enabled'] = '1'
        self.mock_validation.parameters['ssl_cert_path'] = ''
        
        with self.assertRaises(Exception) as context:
            self.input_handler.validate_input(self.mock_validation)
        
        self.assertIn('certificate', str(context.exception))
    
    def test_validate_input_invalid_content_length(self):
        """Test validation with invalid content length."""
        self.mock_validation.parameters['max_content_length'] = '500'
        
        with self.assertRaises(Exception) as context:
            self.input_handler.validate_input(self.mock_validation)
        
        self.assertIn('content length must be at least', str(context.exception))
    
    def test_validate_input_invalid_log_level(self):
        """Test validation with invalid log level."""
        self.mock_validation.parameters['log_level'] = 'INVALID'
        
        with self.assertRaises(Exception) as context:
            self.input_handler.validate_input(self.mock_validation)
        
        self.assertIn('Invalid log level', str(context.exception))
    
    @patch('opa_decision_logs.OPAHTTPServer')
    @patch('opa_decision_logs.logging')
    def test_stream_events(self, mock_logging, mock_server_class):
        """Test stream_events method."""
        # Mock inputs
        mock_inputs = Mock()
        mock_inputs.inputs = {
            'test_input': {
                'http_port': '8088',
                'http_path': '/opadecisions',
                'ssl_enabled': '0',
                'max_content_length': '10485760',
                'log_level': 'INFO',
                'index': 'main',
                'sourcetype': 'opa:decision',
                'host': 'opa-listener'
            }
        }
        
        # Mock event writer
        mock_ew = Mock()
        
        # Mock server instance
        mock_server = Mock()
        mock_server_class.return_value = mock_server
        
        # Mock service
        self.input_handler.service = Mock()
        self.input_handler.service.namespace = {}
        
        # Execute stream_events
        self.input_handler.stream_events(mock_inputs, mock_ew)
        
        # Verify server was created and started
        mock_server_class.assert_called_once()
        mock_server.start.assert_called_once()

class TestOPAHTTPServer(unittest.TestCase):
    """
    Test cases for OPA HTTP Server functionality.
    """
    
    def setUp(self):
        """Set up test fixtures."""
        if OPAHTTPServer is None:
            self.skipTest("OPA HTTP server module not available")
        
        self.config = {
            'http_port': 8088,
            'http_path': '/opadecisions',
            'ssl_enabled': False,
            'max_content_length': 10485760,
            'max_connections': 100,
            'buffer_size': 8192,
            'timeout': 30,
            'health_check_enabled': True,
            'health_check_interval': 300,
            'log_level': 'INFO',
            'input_name': 'test_input',
            'index': 'main',
            'sourcetype': 'opa:decision',
            'host': 'opa-listener'
        }
        
        self.mock_ew = Mock()
        self.mock_logger = Mock()
    
    def test_server_initialization(self):
        """Test HTTP server initialization."""
        server = OPAHTTPServer(self.config, self.mock_ew, self.mock_logger)
        
        self.assertEqual(server.config, self.config)
        self.assertEqual(server.event_writer, self.mock_ew)
        self.assertEqual(server.logger, self.mock_logger)
    
    def test_validate_decision_log_valid(self):
        """Test validation of valid decision log."""
        server = OPAHTTPServer(self.config, self.mock_ew, self.mock_logger)
        
        valid_log = {
            'decision_id': 'test-123',
            'timestamp': '2024-01-01T12:00:00.000Z',
            'path': 'data/authz/allow',
            'result': True,
            'input': {'user': 'alice', 'action': 'read'},
            'metrics': {'timer_rego_query_eval_ns': 1000000}
        }
        
        try:
            server._validate_decision_log(valid_log)
        except Exception as e:
            self.fail(f"Validation failed for valid log: {e}")
    
    def test_validate_decision_log_missing_fields(self):
        """Test validation with missing required fields."""
        server = OPAHTTPServer(self.config, self.mock_ew, self.mock_logger)
        
        invalid_log = {
            'timestamp': '2024-01-01T12:00:00.000Z',
            'result': True
            # Missing decision_id and path
        }
        
        with self.assertRaises(ValueError) as context:
            server._validate_decision_log(invalid_log)
        
        self.assertIn('Missing required field', str(context.exception))
    
    def test_enrich_decision_log(self):
        """Test decision log enrichment."""
        server = OPAHTTPServer(self.config, self.mock_ew, self.mock_logger)

        log = {
            'decision_id': 'test-123',
            'timestamp': '2024-01-01T12:00:00.000Z',
            'path': 'data/authz/allow',
            'result': True,
            'input': {'user': 'alice', 'action': 'read'},
            'metrics': {'timer_rego_query_eval_ns': 1000000}
        }

        # Test the public method that actually exists
        handler = server.create_request_handler()
        request_handler = handler()
        enriched = request_handler.enrich_decision_log(log)

        # Check enriched fields
        self.assertIn('decision_result', enriched)
        self.assertEqual(enriched['decision_result'], 'allow')
        self.assertIn('policy_name', enriched)
        self.assertIn('query_duration_ms', enriched)
        self.assertIn('ingestion_timestamp', enriched)
    
    def test_sanitize_sensitive_data(self):
        """Test sensitive data sanitization."""
        server = OPAHTTPServer(self.config, self.mock_ew, self.mock_logger)
        
        log_with_sensitive_data = {
            'decision_id': 'test-123',
            'input': {
                'user': 'alice',
                'password': 'secret123',
                'token': 'bearer-token',
                'api_key': 'api-secret'
            }
        }
        
        sanitized = server._sanitize_sensitive_data(log_with_sensitive_data)
        
        # Check that sensitive fields are redacted
        self.assertEqual(sanitized['input']['password'], '[REDACTED]')
        self.assertEqual(sanitized['input']['token'], '[REDACTED]')
        self.assertEqual(sanitized['input']['api_key'], '[REDACTED]')
        self.assertEqual(sanitized['input']['user'], 'alice')  # Non-sensitive field preserved

class TestIntegration(unittest.TestCase):
    """
    Integration tests for the complete OPA decision logs workflow.
    """
    
    def setUp(self):
        """Set up integration test fixtures."""
        if OPAHTTPServer is None:
            self.skipTest("OPA modules not available")
        
        self.test_port = 18088  # Use different port for testing
        self.config = {
            'http_port': self.test_port,
            'http_path': '/test-opadecisions',
            'ssl_enabled': False,
            'max_content_length': 10485760,
            'max_connections': 10,
            'buffer_size': 8192,
            'timeout': 5,
            'health_check_enabled': False,
            'log_level': 'INFO',
            'input_name': 'test_input',
            'index': 'test',
            'sourcetype': 'opa:decision',
            'host': 'test-host'
        }
        
        self.mock_ew = Mock()
        self.mock_logger = Mock()
        self.server = None
        self.server_thread = None
    
    def tearDown(self):
        """Clean up after integration tests."""
        if self.server:
            try:
                self.server.stop()
            except:
                pass
        
        if self.server_thread and self.server_thread.is_alive():
            self.server_thread.join(timeout=2)
    
    def test_http_server_lifecycle(self):
        """Test HTTP server start and stop lifecycle."""
        self.server = OPAHTTPServer(self.config, self.mock_ew, self.mock_logger)
        
        # Start server in a separate thread
        self.server_thread = threading.Thread(target=self.server.start)
        self.server_thread.daemon = True
        self.server_thread.start()
        
        # Give server time to start
        time.sleep(1)
        
        # Test health check endpoint
        try:
            response = requests.get(f'http://localhost:{self.test_port}/health', timeout=2)
            self.assertEqual(response.status_code, 200)
        except requests.exceptions.RequestException:
            self.fail("Health check endpoint not accessible")
        
        # Stop server
        self.server.stop()
    
    def test_decision_log_submission(self):
        """Test submitting decision logs to the HTTP server."""
        self.server = OPAHTTPServer(self.config, self.mock_ew, self.mock_logger)
        
        # Start server in a separate thread
        self.server_thread = threading.Thread(target=self.server.start)
        self.server_thread.daemon = True
        self.server_thread.start()
        
        # Give server time to start
        time.sleep(1)
        
        # Prepare test decision log
        decision_log = {
            'decision_id': 'test-integration-123',
            'timestamp': '2024-01-01T12:00:00.000Z',
            'path': 'data/authz/allow',
            'result': True,
            'input': {
                'user': 'test-user',
                'action': 'read',
                'resource': '/api/data'
            },
            'metrics': {
                'timer_rego_query_eval_ns': 1500000,
                'timer_server_handler_ns': 2000000
            }
        }
        
        # Submit decision log
        try:
            response = requests.post(
                f'http://localhost:{self.test_port}{self.config["http_path"]}',
                json=decision_log,
                timeout=5
            )
            self.assertEqual(response.status_code, 200)
        except requests.exceptions.RequestException as e:
            self.fail(f"Failed to submit decision log: {e}")
        
        # Verify event was written
        time.sleep(0.5)  # Give time for processing
        self.mock_ew.write_event.assert_called()
        
        # Stop server
        self.server.stop()

class TestPerformance(unittest.TestCase):
    """
    Performance tests for the OPA decision logs input.
    """
    
    def setUp(self):
        """Set up performance test fixtures."""
        if OPAHTTPServer is None:
            self.skipTest("OPA modules not available")
    
    def test_decision_log_processing_performance(self):
        """Test performance of decision log processing."""
        config = {
            'http_port': 18089,
            'http_path': '/perf-test',
            'ssl_enabled': False,
            'max_content_length': 10485760,
            'log_level': 'ERROR',  # Reduce logging overhead
            'input_name': 'perf_test',
            'index': 'test',
            'sourcetype': 'opa:decision',
            'host': 'perf-test'
        }
        
        mock_ew = Mock()
        mock_logger = Mock()
        server = OPAHTTPServer(config, mock_ew, mock_logger)
        
        # Create test decision log
        decision_log = {
            'decision_id': 'perf-test-123',
            'timestamp': '2024-01-01T12:00:00.000Z',
            'path': 'data/authz/allow',
            'result': True,
            'input': {'user': 'perf-user', 'action': 'read'},
            'metrics': {'timer_rego_query_eval_ns': 1000000}
        }
        
        # Measure processing time for multiple logs
        num_logs = 1000
        start_time = time.time()
        
        for i in range(num_logs):
            decision_log['decision_id'] = f'perf-test-{i}'
            try:
                server._process_decision_log(json.dumps(decision_log))
            except Exception as e:
                self.fail(f"Processing failed at iteration {i}: {e}")
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Performance assertion: should process at least 100 logs per second
        logs_per_second = num_logs / processing_time
        self.assertGreater(logs_per_second, 100, 
                          f"Performance too slow: {logs_per_second:.2f} logs/sec")

if __name__ == '__main__':
    # Configure test logging
    import logging
    logging.basicConfig(level=logging.WARNING)
    
    # Run tests
    unittest.main(verbosity=2)