#!/usr/bin/env python3
"""
Unit Tests for OPA Add-on REST Handler

This module contains comprehensive unit tests for the REST handler
that manages configuration for the OPA Policy Audit & Compliance Add-on.

Author: OPA Community
Version: 1.0.0
"""

import unittest
import sys
import os
import json
import tempfile
from unittest.mock import Mock, patch, MagicMock, mock_open
from io import StringIO

# Add the bin directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'bin'))

try:
    from opa_addon_setup_rh import OPAAddonSetupHandler
except ImportError as e:
    print(f"Warning: Could not import REST handler module: {e}")
    OPAAddonSetupHandler = None

class TestOPAAddonSetupHandler(unittest.TestCase):
    """
    Test cases for OPA Add-on Setup REST Handler.
    """
    
    def setUp(self):
        """Set up test fixtures."""
        if OPAAddonSetupHandler is None:
            self.skipTest("REST handler module not available")
        
        # Mock Splunk REST handler components
        self.mock_request = Mock()
        self.mock_response = Mock()
        
        # Create handler instance
        self.handler = OPAAddonSetupHandler()
        
        # Mock service and session
        self.mock_service = Mock()
        self.mock_session = Mock()
        self.handler.service = self.mock_service
        self.handler.session = self.mock_session
    
    def test_handler_initialization(self):
        """Test REST handler initialization."""
        self.assertIsInstance(self.handler, OPAAddonSetupHandler)
        self.assertEqual(self.handler.conf_file, 'opa_addon_settings')
    
    @patch('opa_addon_setup_rh.conf_manager')
    def test_handle_get_success(self, mock_conf_manager):
        """Test successful GET request handling."""
        # Mock configuration data
        mock_conf_data = {
            'opa_decision_logs': {
                'http_port': '8088',
                'http_path': '/opadecisions',
                'ssl_enabled': '0',
                'max_content_length': '10485760'
            },
            'styra_das_integration': {
                'enabled': '0',
                'api_endpoint': 'https://api.styra.com',
                'api_token': '',
                'polling_interval': '300'
            },
            'health_monitoring': {
                'enabled': '0',
                'opa_endpoints': 'http://localhost:8181',
                'check_interval': '300'
            },
            'data_configuration': {
                'target_index': 'main',
                'log_level': 'INFO'
            }
        }
        
        mock_conf_manager.get_conf.return_value = mock_conf_data
        
        # Mock request
        self.mock_request.method = 'GET'
        
        # Execute GET request
        result = self.handler.handle_GET()
        
        # Verify response
        self.assertIsInstance(result, dict)
        self.assertIn('opa_decision_logs', result)
        self.assertIn('styra_das_integration', result)
        self.assertIn('health_monitoring', result)
        self.assertIn('data_configuration', result)
    
    @patch('opa_addon_setup_rh.conf_manager')
    def test_handle_get_missing_config(self, mock_conf_manager):
        """Test GET request with missing configuration."""
        # Mock missing configuration
        mock_conf_manager.get_conf.side_effect = Exception("Configuration not found")
        
        # Mock request
        self.mock_request.method = 'GET'
        
        # Execute GET request
        result = self.handler.handle_GET()
        
        # Should return default configuration
        self.assertIsInstance(result, dict)
        self.assertIn('opa_decision_logs', result)
        self.assertEqual(result['opa_decision_logs']['http_port'], '8088')
    
    @patch('opa_addon_setup_rh.conf_manager')
    def test_handle_post_success(self, mock_conf_manager):
        """Test successful POST request handling."""
        # Mock request data
        request_data = {
            'opa_decision_logs': {
                'http_port': '8089',
                'http_path': '/newpath',
                'ssl_enabled': '1',
                'ssl_cert_path': '/path/to/cert.pem',
                'ssl_key_path': '/path/to/key.pem',
                'max_content_length': '20971520'
            },
            'styra_das_integration': {
                'enabled': '1',
                'api_endpoint': 'https://custom.styra.com',
                'api_token': 'new-token-123',
                'polling_interval': '600'
            },
            'health_monitoring': {
                'enabled': '1',
                'opa_endpoints': 'http://opa1:8181,http://opa2:8181',
                'check_interval': '600'
            },
            'data_configuration': {
                'target_index': 'opa_logs',
                'log_level': 'DEBUG'
            }
        }
        
        # Mock request
        self.mock_request.method = 'POST'
        
        # Mock form data
        with patch.object(self.handler, '_get_form_data', return_value=request_data):
            # Mock configuration update
            mock_conf_manager.update_conf.return_value = True
            
            # Execute POST request
            result = self.handler.handle_POST()
        
        # Verify response
        self.assertIsInstance(result, dict)
        self.assertIn('success', result)
        self.assertTrue(result['success'])
        self.assertIn('message', result)
        
        # Verify configuration was updated
        mock_conf_manager.update_conf.assert_called_once()
    
    def test_validate_configuration_valid(self):
        """Test configuration validation with valid data."""
        valid_config = {
            'opa_decision_logs': {
                'http_port': '8088',
                'http_path': '/opadecisions',
                'ssl_enabled': '0',
                'max_content_length': '10485760'
            },
            'styra_das_integration': {
                'enabled': '0',
                'api_endpoint': 'https://api.styra.com',
                'polling_interval': '300'
            },
            'health_monitoring': {
                'enabled': '0',
                'opa_endpoints': 'http://localhost:8181',
                'check_interval': '300'
            },
            'data_configuration': {
                'target_index': 'main',
                'log_level': 'INFO'
            }
        }
        
        # Should not raise any exceptions
        try:
            self.handler._validate_configuration(valid_config)
        except Exception as e:
            self.fail(f"Validation failed for valid configuration: {e}")
    
    def test_validate_configuration_invalid_port(self):
        """Test configuration validation with invalid port."""
        invalid_config = {
            'opa_decision_logs': {
                'http_port': '70000',  # Invalid port
                'http_path': '/opadecisions'
            }
        }
        
        with self.assertRaises(ValueError) as context:
            self.handler._validate_configuration(invalid_config)
        
        self.assertIn('port must be between', str(context.exception))
    
    def test_validate_configuration_invalid_path(self):
        """Test configuration validation with invalid HTTP path."""
        invalid_config = {
            'opa_decision_logs': {
                'http_port': '8088',
                'http_path': 'invalid-path'  # Missing leading slash
            }
        }
        
        with self.assertRaises(ValueError) as context:
            self.handler._validate_configuration(invalid_config)
        
        self.assertIn('path must start with', str(context.exception))
    
    def test_validate_configuration_ssl_missing_cert(self):
        """Test configuration validation with SSL enabled but missing certificate."""
        invalid_config = {
            'opa_decision_logs': {
                'http_port': '8088',
                'http_path': '/opadecisions',
                'ssl_enabled': '1',
                'ssl_cert_path': '',  # Missing certificate path
                'ssl_key_path': '/path/to/key.pem'
            }
        }
        
        with self.assertRaises(ValueError) as context:
            self.handler._validate_configuration(invalid_config)
        
        self.assertIn('certificate path is required', str(context.exception))
    
    def test_validate_configuration_invalid_styra_endpoint(self):
        """Test configuration validation with invalid Styra endpoint."""
        invalid_config = {
            'styra_das_integration': {
                'enabled': '1',
                'api_endpoint': 'invalid-url',  # Invalid URL
                'api_token': 'token-123'
            }
        }
        
        with self.assertRaises(ValueError) as context:
            self.handler._validate_configuration(invalid_config)
        
        self.assertIn('Invalid Styra DAS API endpoint', str(context.exception))
    
    def test_validate_configuration_styra_missing_token(self):
        """Test configuration validation with Styra enabled but missing token."""
        invalid_config = {
            'styra_das_integration': {
                'enabled': '1',
                'api_endpoint': 'https://api.styra.com',
                'api_token': ''  # Missing token
            }
        }
        
        with self.assertRaises(ValueError) as context:
            self.handler._validate_configuration(invalid_config)
        
        self.assertIn('API token is required', str(context.exception))
    
    def test_validate_configuration_invalid_polling_interval(self):
        """Test configuration validation with invalid polling interval."""
        invalid_config = {
            'styra_das_integration': {
                'enabled': '1',
                'api_endpoint': 'https://api.styra.com',
                'api_token': 'token-123',
                'polling_interval': '30'  # Too short
            }
        }
        
        with self.assertRaises(ValueError) as context:
            self.handler._validate_configuration(invalid_config)
        
        self.assertIn('Polling interval must be at least', str(context.exception))
    
    def test_validate_configuration_invalid_log_level(self):
        """Test configuration validation with invalid log level."""
        invalid_config = {
            'data_configuration': {
                'target_index': 'main',
                'log_level': 'INVALID'  # Invalid log level
            }
        }
        
        with self.assertRaises(ValueError) as context:
            self.handler._validate_configuration(invalid_config)
        
        self.assertIn('Invalid log level', str(context.exception))
    
    @patch('opa_addon_setup_rh.requests')
    def test_test_connection_opa_success(self, mock_requests):
        """Test successful OPA connection test."""
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'result': True}
        mock_requests.get.return_value = mock_response
        
        config = {
            'opa_decision_logs': {
                'http_port': '8088'
            }
        }
        
        result = self.handler._test_connection(config)
        
        self.assertTrue(result['success'])
        self.assertIn('OPA connection successful', result['message'])
    
    @patch('opa_addon_setup_rh.requests')
    def test_test_connection_opa_failure(self, mock_requests):
        """Test failed OPA connection test."""
        # Mock connection failure
        mock_requests.get.side_effect = Exception("Connection refused")
        
        config = {
            'opa_decision_logs': {
                'http_port': '8088'
            }
        }
        
        result = self.handler._test_connection(config)
        
        self.assertFalse(result['success'])
        self.assertIn('Failed to connect to OPA', result['message'])
    
    @patch('opa_addon_setup_rh.requests')
    def test_test_connection_styra_success(self, mock_requests):
        """Test successful Styra DAS connection test."""
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'systems': []}
        mock_requests.get.return_value = mock_response
        
        config = {
            'styra_das_integration': {
                'enabled': '1',
                'api_endpoint': 'https://api.styra.com',
                'api_token': 'token-123'
            }
        }
        
        result = self.handler._test_connection(config)
        
        self.assertTrue(result['success'])
        self.assertIn('Styra DAS connection successful', result['message'])
    
    @patch('opa_addon_setup_rh.requests')
    def test_test_connection_styra_failure(self, mock_requests):
        """Test failed Styra DAS connection test."""
        # Mock authentication failure
        mock_response = Mock()
        mock_response.status_code = 401
        mock_response.text = 'Unauthorized'
        mock_requests.get.return_value = mock_response
        
        config = {
            'styra_das_integration': {
                'enabled': '1',
                'api_endpoint': 'https://api.styra.com',
                'api_token': 'invalid-token'
            }
        }
        
        result = self.handler._test_connection(config)
        
        self.assertFalse(result['success'])
        self.assertIn('Failed to connect to Styra DAS', result['message'])
    
    def test_get_default_configuration(self):
        """Test default configuration generation."""
        default_config = self.handler._get_default_configuration()
        
        # Verify structure
        self.assertIn('opa_decision_logs', default_config)
        self.assertIn('styra_das_integration', default_config)
        self.assertIn('health_monitoring', default_config)
        self.assertIn('data_configuration', default_config)
        
        # Verify default values
        opa_config = default_config['opa_decision_logs']
        self.assertEqual(opa_config['http_port'], '8088')
        self.assertEqual(opa_config['http_path'], '/opadecisions')
        self.assertEqual(opa_config['ssl_enabled'], '0')
        
        styra_config = default_config['styra_das_integration']
        self.assertEqual(styra_config['enabled'], '0')
        self.assertEqual(styra_config['api_endpoint'], 'https://api.styra.com')
        
        data_config = default_config['data_configuration']
        self.assertEqual(data_config['target_index'], 'main')
        self.assertEqual(data_config['log_level'], 'INFO')
    
    @patch('opa_addon_setup_rh.conf_manager')
    def test_update_inputs_conf(self, mock_conf_manager):
        """Test updating inputs.conf with new configuration."""
        config = {
            'opa_decision_logs': {
                'http_port': '8089',
                'http_path': '/newpath',
                'ssl_enabled': '1',
                'ssl_cert_path': '/path/to/cert.pem',
                'ssl_key_path': '/path/to/key.pem'
            },
            'styra_das_integration': {
                'enabled': '1',
                'api_endpoint': 'https://custom.styra.com',
                'api_token': 'new-token',
                'polling_interval': '600'
            },
            'data_configuration': {
                'target_index': 'opa_logs',
                'log_level': 'DEBUG'
            }
        }
        
        # Mock current inputs.conf
        mock_inputs_conf = {
            'opa_decision_logs': {
                'disabled': '0',
                'http_port': '8088',
                'http_path': '/opadecisions'
            },
            'styra_das_audit': {
                'disabled': '1'
            }
        }
        
        mock_conf_manager.get_conf.return_value = mock_inputs_conf
        
        # Execute update
        self.handler._update_inputs_conf(config)
        
        # Verify inputs.conf was updated
        mock_conf_manager.update_conf.assert_called()
        
        # Get the updated configuration
        call_args = mock_conf_manager.update_conf.call_args[0]
        updated_inputs = call_args[1]
        
        # Verify OPA decision logs configuration
        opa_input = updated_inputs['opa_decision_logs']
        self.assertEqual(opa_input['http_port'], '8089')
        self.assertEqual(opa_input['http_path'], '/newpath')
        self.assertEqual(opa_input['ssl_enabled'], '1')
        
        # Verify Styra DAS configuration
        styra_input = updated_inputs['styra_das_audit']
        self.assertEqual(styra_input['disabled'], '0')  # Should be enabled
        self.assertEqual(styra_input['api_endpoint'], 'https://custom.styra.com')
        self.assertEqual(styra_input['api_token'], 'new-token')

class TestConfigurationManager(unittest.TestCase):
    """
    Test cases for configuration management utilities.
    """
    
    def setUp(self):
        """Set up test fixtures."""
        if OPAAddonSetupHandler is None:
            self.skipTest("REST handler module not available")
    
    def test_sanitize_configuration(self):
        """Test configuration sanitization for logging."""
        handler = OPAAddonSetupHandler()
        
        config_with_secrets = {
            'styra_das_integration': {
                'api_token': 'secret-token-123',
                'api_endpoint': 'https://api.styra.com'
            },
            'opa_decision_logs': {
                'ssl_key_path': '/path/to/secret/key.pem',
                'ssl_cert_path': '/path/to/cert.pem',
                'http_port': '8088'
            }
        }
        
        sanitized = handler._sanitize_configuration(config_with_secrets)
        
        # Verify sensitive fields are redacted
        self.assertEqual(sanitized['styra_das_integration']['api_token'], '[REDACTED]')
        self.assertEqual(sanitized['opa_decision_logs']['ssl_key_path'], '[REDACTED]')
        
        # Verify non-sensitive fields are preserved
        self.assertEqual(sanitized['styra_das_integration']['api_endpoint'], 'https://api.styra.com')
        self.assertEqual(sanitized['opa_decision_logs']['ssl_cert_path'], '/path/to/cert.pem')
        self.assertEqual(sanitized['opa_decision_logs']['http_port'], '8088')
    
    def test_merge_configurations(self):
        """Test merging default and user configurations."""
        handler = OPAAddonSetupHandler()
        
        default_config = {
            'opa_decision_logs': {
                'http_port': '8088',
                'http_path': '/opadecisions',
                'ssl_enabled': '0'
            },
            'styra_das_integration': {
                'enabled': '0',
                'api_endpoint': 'https://api.styra.com'
            }
        }
        
        user_config = {
            'opa_decision_logs': {
                'http_port': '8089',
                'ssl_enabled': '1'
            }
        }
        
        merged = handler._merge_configurations(default_config, user_config)
        
        # Verify user values override defaults
        self.assertEqual(merged['opa_decision_logs']['http_port'], '8089')
        self.assertEqual(merged['opa_decision_logs']['ssl_enabled'], '1')
        
        # Verify default values are preserved when not overridden
        self.assertEqual(merged['opa_decision_logs']['http_path'], '/opadecisions')
        self.assertEqual(merged['styra_das_integration']['enabled'], '0')
        self.assertEqual(merged['styra_das_integration']['api_endpoint'], 'https://api.styra.com')

if __name__ == '__main__':
    # Configure test logging
    import logging
    logging.basicConfig(level=logging.WARNING)
    
    # Run tests
    unittest.main(verbosity=2)