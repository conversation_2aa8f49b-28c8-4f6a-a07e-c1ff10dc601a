#!/usr/bin/env python3
"""
Unit Tests for Styra DAS Audit Modular Input

This module contains comprehensive unit tests for the Styra DAS audit
modular input, including API polling, authentication, and event processing.

Author: OPA Community
Version: 1.0.0
"""

import unittest
import sys
import os
import json
import tempfile
import time
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from io import StringIO

# Add the bin directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'bin'))

try:
    from styra_das_audit import StyraDASAuditInput
except ImportError as e:
    print(f"Warning: Could not import Styra DAS audit module: {e}")
    StyraDASAuditInput = None

class TestStyraDASAuditInput(unittest.TestCase):
    """
    Test cases for Styra DAS Audit Input validation and configuration.
    """
    
    def setUp(self):
        """Set up test fixtures."""
        if StyraDASAuditInput is None:
            self.skipTest("Styra DAS audit module not available")
        
        self.input_handler = StyraDASAuditInput()
        
        # Mock validation definition
        self.mock_validation = Mock()
        self.mock_validation.parameters = {
            'api_endpoint': 'https://api.styra.com',
            'api_token': 'test-token-123',
            'polling_interval': '300',
            'max_events_per_request': '1000',
            'timeout': '30',
            'max_retries': '3',
            'retry_delay': '5',
            'time_range_hours': '24',
            'event_types': 'policy.created,policy.updated,policy.deleted',
            'log_level': 'INFO'
        }
    
    def test_get_scheme(self):
        """Test input scheme definition."""
        scheme = self.input_handler.get_scheme()
        
        self.assertEqual(scheme.title, "Styra DAS Policy Audit Logs")
        self.assertTrue(scheme.use_external_validation)
        self.assertFalse(scheme.use_single_instance)
        
        # Check required arguments
        arg_names = [arg.name for arg in scheme.args]
        required_args = ['api_endpoint', 'api_token']
        
        for arg in required_args:
            self.assertIn(arg, arg_names)
    
    def test_validate_input_valid_config(self):
        """Test validation with valid configuration."""
        try:
            self.input_handler.validate_input(self.mock_validation)
        except Exception as e:
            self.fail(f"Validation failed with valid config: {e}")
    
    def test_validate_input_invalid_endpoint(self):
        """Test validation with invalid API endpoint."""
        self.mock_validation.parameters['api_endpoint'] = 'invalid-url'
        
        with self.assertRaises(Exception) as context:
            self.input_handler.validate_input(self.mock_validation)
        
        self.assertIn('Invalid API endpoint URL', str(context.exception))
    
    def test_validate_input_missing_token(self):
        """Test validation with missing API token."""
        self.mock_validation.parameters['api_token'] = ''
        
        with self.assertRaises(Exception) as context:
            self.input_handler.validate_input(self.mock_validation)
        
        self.assertIn('API token is required', str(context.exception))
    
    def test_validate_input_invalid_polling_interval(self):
        """Test validation with invalid polling interval."""
        self.mock_validation.parameters['polling_interval'] = '30'
        
        with self.assertRaises(Exception) as context:
            self.input_handler.validate_input(self.mock_validation)
        
        self.assertIn('Polling interval must be at least', str(context.exception))
    
    def test_validate_input_invalid_max_events(self):
        """Test validation with invalid max events per request."""
        self.mock_validation.parameters['max_events_per_request'] = '10000'
        
        with self.assertRaises(Exception) as context:
            self.input_handler.validate_input(self.mock_validation)
        
        self.assertIn('Max events per request cannot exceed', str(context.exception))
    
    def test_validate_input_invalid_timeout(self):
        """Test validation with invalid timeout."""
        self.mock_validation.parameters['timeout'] = '5'
        
        with self.assertRaises(Exception) as context:
            self.input_handler.validate_input(self.mock_validation)
        
        self.assertIn('Timeout must be at least', str(context.exception))
    
    def test_validate_input_invalid_time_range(self):
        """Test validation with invalid time range."""
        self.mock_validation.parameters['time_range_hours'] = '200'
        
        with self.assertRaises(Exception) as context:
            self.input_handler.validate_input(self.mock_validation)
        
        self.assertIn('Time range cannot exceed', str(context.exception))
    
    def test_validate_input_invalid_log_level(self):
        """Test validation with invalid log level."""
        self.mock_validation.parameters['log_level'] = 'INVALID'
        
        with self.assertRaises(Exception) as context:
            self.input_handler.validate_input(self.mock_validation)
        
        self.assertIn('Invalid log level', str(context.exception))
    
    @patch('styra_das_audit.requests')
    @patch('styra_das_audit.logging')
    def test_stream_events(self, mock_logging, mock_requests):
        """Test stream_events method."""
        # Mock inputs
        mock_inputs = Mock()
        mock_inputs.inputs = {
            'test_input': {
                'api_endpoint': 'https://api.styra.com',
                'api_token': 'test-token',
                'polling_interval': '300',
                'max_events_per_request': '1000',
                'timeout': '30',
                'log_level': 'INFO',
                'index': 'main',
                'sourcetype': 'styra:das:policy:audit',
                'host': 'styra-das'
            }
        }
        
        # Mock event writer
        mock_ew = Mock()
        
        # Mock successful API response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'events': [
                {
                    'id': 'event-1',
                    'timestamp': '2024-01-01T12:00:00Z',
                    'type': 'policy.created',
                    'data': {'policy_id': 'policy-123'}
                }
            ],
            'has_more': False
        }
        mock_requests.get.return_value = mock_response
        
        # Mock service
        self.input_handler.service = Mock()
        self.input_handler.service.namespace = {}
        
        # Execute stream_events (should run once due to mocking)
        with patch('time.sleep'):
            with patch.object(self.input_handler, '_should_continue_polling', side_effect=[True, False]):
                self.input_handler.stream_events(mock_inputs, mock_ew)
        
        # Verify API was called
        mock_requests.get.assert_called()
        
        # Verify event was written
        mock_ew.write_event.assert_called()

class TestStyraDASAPIClient(unittest.TestCase):
    """
    Test cases for Styra DAS API client functionality.
    """
    
    def setUp(self):
        """Set up test fixtures."""
        if StyraDASAuditInput is None:
            self.skipTest("Styra DAS audit module not available")
        
        self.config = {
            'api_endpoint': 'https://api.styra.com',
            'api_token': 'test-token-123',
            'polling_interval': 300,
            'max_events_per_request': 1000,
            'timeout': 30,
            'max_retries': 3,
            'retry_delay': 5,
            'time_range_hours': 24,
            'event_types': ['policy.created', 'policy.updated', 'policy.deleted'],
            'log_level': 'INFO',
            'input_name': 'test_input',
            'index': 'main',
            'sourcetype': 'styra:das:policy:audit',
            'host': 'styra-das'
        }
        
        self.mock_ew = Mock()
        self.mock_logger = Mock()
        self.client = StyraDASAuditInput()
        self.client.config = self.config
        self.client.event_writer = self.mock_ew
        self.client.logger = self.mock_logger
    
    @patch('styra_das_audit.requests')
    def test_fetch_audit_events_success(self, mock_requests):
        """Test successful audit events fetch."""
        # Mock successful API response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'events': [
                {
                    'id': 'event-1',
                    'timestamp': '2024-01-01T12:00:00Z',
                    'type': 'policy.created',
                    'actor': {'id': 'user-123', 'name': 'Alice'},
                    'data': {
                        'policy_id': 'policy-123',
                        'policy_name': 'test-policy',
                        'system_id': 'system-456'
                    }
                },
                {
                    'id': 'event-2',
                    'timestamp': '2024-01-01T12:05:00Z',
                    'type': 'policy.updated',
                    'actor': {'id': 'user-456', 'name': 'Bob'},
                    'data': {
                        'policy_id': 'policy-456',
                        'policy_name': 'updated-policy',
                        'system_id': 'system-789'
                    }
                }
            ],
            'has_more': False,
            'next_cursor': None
        }
        mock_requests.get.return_value = mock_response
        
        # Execute fetch
        events = self.client._fetch_audit_events()
        
        # Verify API call
        mock_requests.get.assert_called_once()
        call_args = mock_requests.get.call_args
        
        # Check URL
        self.assertIn('/v1/audit/events', call_args[0][0])
        
        # Check headers
        headers = call_args[1]['headers']
        self.assertEqual(headers['Authorization'], 'Bearer test-token-123')
        self.assertEqual(headers['Content-Type'], 'application/json')
        
        # Check parameters
        params = call_args[1]['params']
        self.assertEqual(params['limit'], 1000)
        self.assertIn('from', params)
        self.assertIn('to', params)
        
        # Verify returned events
        self.assertEqual(len(events), 2)
        self.assertEqual(events[0]['id'], 'event-1')
        self.assertEqual(events[1]['id'], 'event-2')
    
    @patch('styra_das_audit.requests')
    def test_fetch_audit_events_api_error(self, mock_requests):
        """Test API error handling."""
        # Mock API error response
        mock_response = Mock()
        mock_response.status_code = 401
        mock_response.text = 'Unauthorized'
        mock_requests.get.return_value = mock_response
        
        # Execute fetch and expect exception
        with self.assertRaises(Exception) as context:
            self.client._fetch_audit_events()
        
        self.assertIn('API request failed', str(context.exception))
        self.assertIn('401', str(context.exception))
    
    @patch('styra_das_audit.requests')
    def test_fetch_audit_events_with_retry(self, mock_requests):
        """Test retry mechanism on transient failures."""
        # Mock transient failure followed by success
        mock_response_fail = Mock()
        mock_response_fail.status_code = 500
        mock_response_fail.text = 'Internal Server Error'
        
        mock_response_success = Mock()
        mock_response_success.status_code = 200
        mock_response_success.json.return_value = {
            'events': [],
            'has_more': False
        }
        
        mock_requests.get.side_effect = [mock_response_fail, mock_response_success]
        
        # Execute fetch
        with patch('time.sleep'):  # Mock sleep to speed up test
            events = self.client._fetch_audit_events()
        
        # Verify retry occurred
        self.assertEqual(mock_requests.get.call_count, 2)
        self.assertEqual(len(events), 0)
    
    def test_enrich_audit_event(self):
        """Test audit event enrichment."""
        event = {
            'id': 'event-123',
            'timestamp': '2024-01-01T12:00:00Z',
            'type': 'policy.created',
            'actor': {'id': 'user-123', 'name': 'Alice'},
            'data': {
                'policy_id': 'policy-123',
                'policy_name': 'test-policy',
                'system_id': 'system-456'
            }
        }
        
        enriched = self.client._enrich_audit_event(event)
        
        # Check enriched fields
        self.assertIn('event_category', enriched)
        self.assertEqual(enriched['event_category'], 'policy_management')
        self.assertIn('severity', enriched)
        self.assertIn('ingestion_timestamp', enriched)
        self.assertIn('actor_name', enriched)
        self.assertEqual(enriched['actor_name'], 'Alice')
        self.assertIn('policy_name', enriched)
        self.assertEqual(enriched['policy_name'], 'test-policy')
    
    def test_filter_events_by_type(self):
        """Test event filtering by type."""
        events = [
            {'type': 'policy.created', 'id': 'event-1'},
            {'type': 'policy.updated', 'id': 'event-2'},
            {'type': 'policy.deleted', 'id': 'event-3'},
            {'type': 'system.created', 'id': 'event-4'},
            {'type': 'user.login', 'id': 'event-5'}
        ]
        
        # Filter for policy events only
        self.client.config['event_types'] = ['policy.created', 'policy.updated', 'policy.deleted']
        
        filtered = self.client._filter_events_by_type(events)
        
        # Should only include policy events
        self.assertEqual(len(filtered), 3)
        event_ids = [e['id'] for e in filtered]
        self.assertIn('event-1', event_ids)
        self.assertIn('event-2', event_ids)
        self.assertIn('event-3', event_ids)
        self.assertNotIn('event-4', event_ids)
        self.assertNotIn('event-5', event_ids)
    
    def test_calculate_time_range(self):
        """Test time range calculation."""
        # Mock current time
        mock_now = datetime(2024, 1, 1, 12, 0, 0)
        
        with patch('styra_das_audit.datetime') as mock_datetime:
            mock_datetime.utcnow.return_value = mock_now
            mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)
            
            from_time, to_time = self.client._calculate_time_range()
        
        # Verify time range (24 hours by default)
        expected_from = mock_now - timedelta(hours=24)
        self.assertEqual(from_time, expected_from)
        self.assertEqual(to_time, mock_now)
    
    def test_format_timestamp(self):
        """Test timestamp formatting."""
        timestamp = datetime(2024, 1, 1, 12, 0, 0)
        formatted = self.client._format_timestamp(timestamp)
        
        self.assertEqual(formatted, '2024-01-01T12:00:00Z')
    
    def test_parse_timestamp(self):
        """Test timestamp parsing."""
        timestamp_str = '2024-01-01T12:00:00Z'
        parsed = self.client._parse_timestamp(timestamp_str)
        
        expected = datetime(2024, 1, 1, 12, 0, 0)
        self.assertEqual(parsed, expected)

class TestIntegration(unittest.TestCase):
    """
    Integration tests for the complete Styra DAS audit workflow.
    """
    
    def setUp(self):
        """Set up integration test fixtures."""
        if StyraDASAuditInput is None:
            self.skipTest("Styra DAS audit module not available")
        
        self.config = {
            'api_endpoint': 'https://api.styra.com',
            'api_token': 'test-integration-token',
            'polling_interval': 60,  # Shorter for testing
            'max_events_per_request': 100,
            'timeout': 10,
            'max_retries': 2,
            'retry_delay': 1,
            'time_range_hours': 1,
            'event_types': ['policy.created', 'policy.updated'],
            'log_level': 'INFO',
            'input_name': 'integration_test',
            'index': 'test',
            'sourcetype': 'styra:das:policy:audit',
            'host': 'integration-test'
        }
        
        self.mock_ew = Mock()
        self.mock_logger = Mock()
    
    @patch('styra_das_audit.requests')
    def test_end_to_end_workflow(self, mock_requests):
        """Test complete end-to-end workflow."""
        # Mock API responses
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'events': [
                {
                    'id': 'integration-event-1',
                    'timestamp': '2024-01-01T12:00:00Z',
                    'type': 'policy.created',
                    'actor': {'id': 'user-123', 'name': 'Integration User'},
                    'data': {
                        'policy_id': 'integration-policy-123',
                        'policy_name': 'integration-test-policy',
                        'system_id': 'integration-system-456'
                    }
                }
            ],
            'has_more': False,
            'next_cursor': None
        }
        mock_requests.get.return_value = mock_response
        
        # Create input handler
        input_handler = StyraDASAuditInput()
        input_handler.config = self.config
        input_handler.event_writer = self.mock_ew
        input_handler.logger = self.mock_logger
        
        # Execute one polling cycle
        events = input_handler._fetch_audit_events()
        
        # Process events
        for event in events:
            enriched_event = input_handler._enrich_audit_event(event)
            input_handler._write_event(enriched_event)
        
        # Verify API was called correctly
        mock_requests.get.assert_called_once()
        
        # Verify event was processed and written
        self.assertEqual(len(events), 1)
        self.mock_ew.write_event.assert_called_once()
        
        # Verify event enrichment
        written_event = self.mock_ew.write_event.call_args[0][0]
        self.assertIn('event_category', written_event.data)
        self.assertIn('ingestion_timestamp', written_event.data)
        self.assertIn('actor_name', written_event.data)

class TestPerformance(unittest.TestCase):
    """
    Performance tests for the Styra DAS audit input.
    """
    
    def setUp(self):
        """Set up performance test fixtures."""
        if StyraDASAuditInput is None:
            self.skipTest("Styra DAS audit module not available")
    
    def test_event_processing_performance(self):
        """Test performance of event processing."""
        config = {
            'api_endpoint': 'https://api.styra.com',
            'api_token': 'perf-test-token',
            'max_events_per_request': 5000,
            'event_types': ['policy.created', 'policy.updated', 'policy.deleted'],
            'log_level': 'ERROR',  # Reduce logging overhead
            'input_name': 'perf_test',
            'index': 'test',
            'sourcetype': 'styra:das:policy:audit',
            'host': 'perf-test'
        }
        
        mock_ew = Mock()
        mock_logger = Mock()
        
        client = StyraDASAuditInput()
        client.config = config
        client.event_writer = mock_ew
        client.logger = mock_logger
        
        # Create test events
        num_events = 1000
        events = []
        for i in range(num_events):
            events.append({
                'id': f'perf-event-{i}',
                'timestamp': '2024-01-01T12:00:00Z',
                'type': 'policy.created',
                'actor': {'id': f'user-{i}', 'name': f'User {i}'},
                'data': {
                    'policy_id': f'policy-{i}',
                    'policy_name': f'perf-policy-{i}',
                    'system_id': f'system-{i}'
                }
            })
        
        # Measure processing time
        start_time = time.time()
        
        for event in events:
            try:
                enriched = client._enrich_audit_event(event)
                client._write_event(enriched)
            except Exception as e:
                self.fail(f"Processing failed: {e}")
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Performance assertion: should process at least 500 events per second
        events_per_second = num_events / processing_time
        self.assertGreater(events_per_second, 500, 
                          f"Performance too slow: {events_per_second:.2f} events/sec")

if __name__ == '__main__':
    # Configure test logging
    import logging
    logging.basicConfig(level=logging.WARNING)
    
    # Run tests
    unittest.main(verbosity=2)